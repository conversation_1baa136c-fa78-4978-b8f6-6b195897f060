.search_card {
    border-radius: 4px;
    border: 1px solid #d7d5d5;
    background-color: white;
    padding: 10px 10px;
    box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.search_select_bar {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
}

.search_select_bar_left {
    display: flex;
}

.search_select_bar_left_item {
    display: flex;
    align-items: center;
}

.search_select_reload_button {
    border: 1.5px solid #766d6d !important;
    padding: 3px 20px !important;
}

.search_input {
    height: 40px;
    width: 100%;
    // outline: none !important;
    border: 1px solid #cfcccc !important;
    border-radius: 5px;
    align-items: center;
    padding: 0px 8px;
}

.search_input::after {
    border: none !important;
}

.search_input::before {
    border: none !important;
}

.selectEmpty::after{
    border: none !important;
}

.selectEmpty::before{
    border: none !important;
}

.selectEmpty:active{
    background-color: none !important;
}