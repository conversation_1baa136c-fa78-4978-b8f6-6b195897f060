.pageWrapper {
  background-color: #ffffff;
  padding: 32px;
  min-height: 90vh;
}
.backButton {
  text-align: right;
  margin-bottom: 16px;
}
.backButton a {
  color: #4caf50;
  text-decoration: none;
  font-weight: 500;
}
.section {
  background-color: #fff;
  padding: 12px 24px 24px 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.title {
  font-size: 18px;
  font-weight: bold;
  color: #4caf50;
  padding-bottom: 4px;
  border-bottom: 1px solid #e0e0e0;
}
.label {
  color: #888;
  font-size: 14px;
}
.value {
  font-weight: 500;
  font-size: 16px;
  margin-top: 4px;
}
.statusRunning {
  color: #4caf50;
  font-weight: bold;
}
.logBox {
  background-color: #f5f5f5;
  padding: 16px;
  font-family: monospace;
  white-space: pre-wrap;
  border-radius: 4px;
  margin-top: 16px;
  font-size: 13px;
  color: #333;
}
.statCard {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}
.actionButtons {
  display: flex;
  gap: 16px;
  margin-top: 16px;
}
