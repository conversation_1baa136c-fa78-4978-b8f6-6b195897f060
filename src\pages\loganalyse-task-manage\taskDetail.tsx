import { Box, Button, <PERSON>lapse, Grid, IconButton, InputLabel, LinearProgress, makeStyles, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, Tooltip, Typography, withStyles } from "@material-ui/core";
import { useState,useRef, useEffect } from "react";
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import * as echarts from 'echarts';
import apiClient from "../apis/apiClient";
import { showSnackbar } from "../points-manage/component/myMessageBar";
import { Pagination } from "@material-ui/lab";
interface TaskResult {
  id: number;
  log_level: string;
  log_source: string;
  source_ip: string;
  destination_ip: string;
  threat_level: string;
  threat_score: number;
  ai_analysis: string;
  recommended_actions: Array<string>;
  is_reviewed: boolean;
  log_timestamp: string;
}
const StyledTableCell = withStyles((theme) => ({
    head: {
      backgroundColor: '#F5F5F5',
      color: theme.palette.common.black,
      textAlign: 'center',
    },
    body: {
      fontSize: 14,
      textAlign: 'center',
    },
  }))(TableCell)
export default function TaskDetail(props: any) {
  const { handleCloseDetail,detailData,fetchTasks } = props;
  const [page, setPage] = useState(0); // MUI 从 0 开始
  const [pageSize, setPageSize] = useState(5);
  const [total, setTotal] = useState(0);
  const chartRef = useRef<HTMLDivElement | null>(null);//创建一个 ref，用来挂载 ECharts 容器
  const [formData, setFormData] = useState(detailData);
  const [open, setOpen] = useState(false);
  const [taskResult, setTaskResult]= useState<TaskResult[]>([]);
  const getTaskResult = async (id: number) => {
    try {
      const res = await apiClient.get(`/api/log-analysis/tasks/${id}/result` ,{
      params: {
        page: page + 1,
        per_page: pageSize,
      },
    });
      const result = res.data;
      setTaskResult(result.items);
      setTotal(result.pagination.total);
      console.log(result);
    } catch (error) {
      showSnackbar('获取任务结果失败，请检查网络或稍后再试', 'error');
    }
  }
  useEffect (() => {
    getTaskResult(formData.id);
  }, []);
  useEffect(() => {
    getTaskResult(formData.id);
  }, [page, pageSize]);
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return '#4caf50'; // green
      case 'running':
        return '#2196f3'; // blue
      case 'pending':
        return '#ff9800'; // orange
      default:
        return '#9e9e9e'; // grey
    }
  };

  const renderStatus = (status: string) => {
    const textMap: Record<string, string> = {completed: '已完成',running: '运行中',pending: '待处理',failed:'已失败',cancelled:'已取消'}
    return (
      <span style={{padding: '8px 18px',borderRadius: 4, color: '#fff',fontSize: 16,display: 'inline-block', backgroundColor: getStatusColor(status),marginTop: 10 }} >
        {textMap[status] || status}
      </span>
    );
  };

  const handleToggle = () => setOpen(prev => !prev)
  
  useEffect(() => {
    if (!chartRef.current) return; //确保 ref.current 已经指向了真实 DOM
    const myChart = echarts.init(chartRef.current);//初始化图表
    myChart.setOption({
          color: ['#2E7D32', '#F9A825', '#C62828'],
          grid: {left:'0%',right:'2%',top:'12%',bottom:'0%',containLabel: true},
          xAxis: {
            data: ['低危事件数量', '中危事件数量', '高危事件数量']
          },
          yAxis: {},
          series: [
            {
              type: 'bar',
              colorBy: 'data',
              data: [detailData.low_risk_events, detailData.medium_risk_events, detailData.high_risk_events]
            }
          ]
        })
  }, []);
  const getDetailData = async (id: number) => {
       try {
          const res = await apiClient.get(`/api/log-analysis/tasks/${id}`);
          const { task } = res.data;
          setFormData(task);
      } catch (error) {
        showSnackbar('获取任务详情失败，请检查网络或稍后再试', 'error');
      }
    }
  
  const handleStartTask = async (taskId: number) => {
    try {
      const res = await apiClient.post(`/api/log-analysis/tasks/${taskId}/start`);
      if (res.data.message === '任务启动成功') {
        getDetailData(taskId);
        showSnackbar('任务启动成功', 'success');
        fetchTasks(); // 刷新列表
      } else {
        showSnackbar('启动失败：' + res.data.message, 'error');
      }
    } catch (error: any) {
      console.error('启动任务失败:', error);
      showSnackbar('启动任务失败，请检查网络或稍后再试', 'error');
    }
  };
  
  const handleCloseTask =async (id: number) => { 
          try {
            const res = await apiClient.post(`/api/log-analysis/tasks/${id}/cancel`);
            if (res.data.message === '任务已取消') {
              showSnackbar('任务取消成功', 'success');
            } else {
              showSnackbar('取消失败：' + res.data.message, 'error');
            }
          } catch (error) {
            console.error('取消任务失败:', error);
            showSnackbar('取消任务失败，请检查网络或稍后再试', 'error');
          }
  }

  const formatDate = (isoString: string) => {
    if (!isoString) return '-';
    const date = new Date(isoString);
    return date.toLocaleString('zh-CN', { hour12: false });
  };
  return <>
  <div style={{overflow: 'auto'}}>
    <div style={{display: 'flex',alignItems: 'center',paddingTop: 20,paddingLeft: 20,color: '#397B44',paddingBottom: 20,borderBottom: '1px solid #E0E0E0'}}>
         <Typography variant="h4" style={{ fontWeight: 'bold' }}>任务详情</Typography>
         <div style={{ display: 'flex', marginLeft: 'auto', gap: '8px' }}>
            {formData.status === 'running' ?
            <Button style={{backgroundColor: '#2196f3',color: 'white',width: 100}} >任务运行中</Button>:
            <Button style={{backgroundColor: '#4CAF50',color: 'white',width: 100}} onClick={() => {handleStartTask(formData.id)}}>启动任务</Button>
            }
            <Button style={{backgroundColor:"red",color:"white",width: 100}} onClick={()=>handleCloseTask(formData.id)}>取消任务</Button>
            <Button style={{backgroundColor:"#455A64",color:"white",width: 100}}>下载报告</Button>
            <Button style={{width: 100}} onClick={handleCloseDetail}>返回</Button>
         </div>
    </div>
    <div style={{paddingLeft: 20,paddingTop: 20,fontSize: 25,fontWeight: 'bold'}}>基本信息</div>
    <div style={{padding: 20}}>
        <Grid item xs={6} style={{marginBottom: 20}}>
           <InputLabel>任务名称</InputLabel>
           <TextField name="task_name"  margin="dense"   fullWidth  value={formData.task_name}  InputProps={{disableUnderline: true,style: {color: 'black',fontSize: 22} }} disabled/>
        </Grid>
        <Grid item xs={6} style={{marginBottom: 20}}>
           <InputLabel>状态</InputLabel>
           {renderStatus(formData.status)}
        </Grid>
        <Grid item xs={6} style={{marginBottom: 20}}>
            <InputLabel>创建时间</InputLabel>
            <TextField name="created_at"  margin="dense"   fullWidth  value={formatDate(formData.created_at)}  InputProps={{disableUnderline: true,style: {color: 'black',fontSize: 22} }} disabled/>
        </Grid>
        <Grid item xs={6} style={{marginBottom: 20}}>
            <InputLabel>输入源</InputLabel>
            <TextField name="input_source"  margin="dense"   fullWidth  value={formData.input_source=== 'file'? '文件上传' : '文本上传  '}  InputProps={{disableUnderline: true,style: {color: 'black',fontSize: 22} }} disabled/>
        </Grid>
        <Grid item xs={6} style={{marginBottom: 20}}>
            <InputLabel>进度</InputLabel>
                <div style={{display: 'flex'}}>
                <LinearProgress variant="determinate" value={formData.progress} style={{width: '100%',height: 7,marginTop: 10}} />
                <span style={{marginLeft: 10}}>{formData.progress || 0}%</span>
                </div>
        </Grid>
    </div>
    <div style={{paddingLeft: 20,fontSize: 25,fontWeight: 'bold',borderBottom: '1px solid #E0E0E0',cursor: 'pointer'}} onClick={handleToggle}>配置快照
        <IconButton size="small">
            <ExpandMoreIcon style={{ transform: open ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'transform 0.2s' }}/>
        </IconButton>
    </div>
    <Collapse in={open} >
    <div style={{padding: 20}}>
        <div style={{marginBottom: 20}}>
           <Box component="pre" style={{backgroundColor: '#f5f5f5', borderRadius: 1,fontFamily: 'Consolas, "Courier New", monospace'}}>
                      {`
                        {
                            "config_id": ${detailData.config_id},
                            "config_name": "${detailData.config_name}",
                            "ai_model": "${detailData.ai_model}"
                            "model_params": {
                                "threshold": 0.7,
                                "max_tokens": 1024
                            },
                            analysis_params: ${JSON.stringify(detailData.analysis_config)}
                        }
                      `}
            </Box>
        </div>
    </div>
    </Collapse>
    <div style={{paddingLeft: 20,paddingTop: 20,fontSize: 25,fontWeight: 'bold'}}>实时统计</div>
    <div style={{padding: 20}}>
        <Grid item xs={6} style={{marginBottom: 20}}>
            <InputLabel>已处理日志条数</InputLabel>
            <TextField name="processed_logs"  margin="dense"   fullWidth  value={formData.processed_logs}  InputProps={{disableUnderline: true,style: {color: 'black',fontSize: 22} }} disabled/>
        </Grid>
        <Grid item xs={6} style={{marginBottom: 20}}>
            <InputLabel>已检测威胁事件</InputLabel>
            <TextField name="threats_detected"  margin="dense"   fullWidth  value={formData.threats_detected}  InputProps={{disableUnderline: true,style: {color: 'black',fontSize: 22} }} disabled/>
        </Grid>
         <Grid item xs={6} style={{marginBottom: 20}}>
            <InputLabel>威胁分布</InputLabel>
            <div ref={chartRef} style={{width: '100%',  height: '400px'}}/>
        </Grid>
    </div>
    {/* <div style={{paddingLeft: 20,fontSize: 25,fontWeight: 'bold'}}>运行时信息</div>
    <div style={{padding: 20}}>
        <Grid item xs={6} style={{marginBottom: 20}}>
            <InputLabel>开始时间</InputLabel>
            <TextField name="start_time"  margin="dense"   fullWidth  value={formatDate(formData.start_time)}  InputProps={{disableUnderline: true,style: {color: 'black',fontSize: 22} }} disabled/>
        </Grid>
        <Grid item xs={6} style={{marginBottom: 20}}>
            <InputLabel>结束时间</InputLabel>
            <TextField name="end_time"  margin="dense"   fullWidth  value={formatDate(formData.end_time)}  InputProps={{disableUnderline: true,style: {color: 'black',fontSize: 22} }} disabled/>
        </Grid>
    </div> */}
    {
        taskResult?.length > 0 ?
        <>
    <div style={{paddingLeft: 20,fontSize: 25,fontWeight: 'bold'}}>任务分析结果</div>
    <div style={{padding: 20}}>
        <TableContainer component={Paper} style={{marginTop: 20}}>
                      <Table aria-label="simple table">
                        <TableHead>
                          <TableRow>
                             <StyledTableCell >ID</StyledTableCell>
                             <StyledTableCell >日志级别</StyledTableCell>
                             <StyledTableCell>日志来源</StyledTableCell>
                             <StyledTableCell>源IP</StyledTableCell>
                             <StyledTableCell>目的IP</StyledTableCell>
                             <StyledTableCell>威胁等级</StyledTableCell>
                             <StyledTableCell>威胁评分</StyledTableCell>
                             <StyledTableCell>AI分析</StyledTableCell>
                             <StyledTableCell>推荐措施</StyledTableCell>
                             {/* <StyledTableCell>是否已审核</StyledTableCell> */}
                             <StyledTableCell>日志时间</StyledTableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                             {taskResult?.map((row, index) => {
                                console.log(row)
                                return (
                                <TableRow >
                                  <TableCell align="center">{page * pageSize + index + 1}</TableCell>
                                  <TableCell align="center"> {row.log_level || '无'}</TableCell>
                                  <TableCell align="center">{row.log_source || '无'}</TableCell>
                                  <TableCell align="center">{row.source_ip || '无'}</TableCell>
                                  <TableCell align="center">{row.destination_ip || '无'}</TableCell>
                                  <TableCell align="center">{row.threat_level || '无'}</TableCell>
                                  <TableCell align="center">{row.threat_score || '无'}</TableCell>
                                  <TableCell align="center">{row.ai_analysis || '无'}</TableCell>
                                  <TableCell align="center">
                                    {row.recommended_actions && row.recommended_actions.length > 0 ? (
                                      row.recommended_actions.map((action, index) => (
                                        <div key={index} style={{textAlign: 'left'}}>
                                          {index + 1}.{action}
                                        </div>
                                      ))
                                    ) : (
                                      <span >无</span>
                                    )}
                                  </TableCell>
                                  {/* <TableCell align="center">{row.is_reviewed ? '已审核' : '未审核'}</TableCell> */}
                                  <TableCell align="center">{formatDate(row.log_timestamp)}</TableCell>
                                </TableRow>)})
                             }
                        </TableBody>
                      </Table>
                  </TableContainer>
                  <div className="attack-list-pagination-div">
                       <div>共 {total} 条数据</div>
                       <Pagination
                         className="attack-list-pagination"
                         showFirstButton
                         count={Math.ceil(total/ pageSize)}
                         page={page + 1}
                         onChange={(event, value) => setPage(value - 1)}
                       />
                  </div></div>
                  </>:<></>
    }
</div>
  </>
}