import React from 'react';
import { Paper, Typography, Box, Grid, Button } from '@material-ui/core';

interface Threat {
  ip: string;
  type: string;
  asset: string;
}
interface Props {
  threatList: Threat[];
  isolated: { [ip: string]: boolean };
  handleIsolate: (ip: string) => void;
  classes: any;
}

const ThreatList: React.FC<Props> = ({ threatList, isolated, handleIsolate, classes }) => (
  <Box >
    <Paper className={classes.card}>
      <div className={classes.metricHeader}>
        <Typography variant="subtitle1">TOP 5 威胁源</Typography>
        <Button size="small" variant="outlined">实时更新</Button>
      </div>
      <Box>
        <Grid container style={{ fontWeight: 600, color: '#7f8c8d', borderBottom: '1px solid #e0e0e0', padding: '12px 0' }}>
          <Grid item xs={3}>攻击IP</Grid>
          <Grid item xs={3}>漏洞类型</Grid>
          <Grid item xs={3}>受影响资产</Grid>
          <Grid item xs={3}>操作</Grid>
        </Grid>
        {threatList.map(t => (
          <Grid container key={t.ip} alignItems="center" style={{ borderBottom: '1px solid #e0e0e0', padding: '12px 0', opacity: isolated[t.ip] ? 0.5 : 1 }}>
            <Grid item xs={3}>{t.ip}</Grid>
            <Grid item xs={3}>{t.type}</Grid>
            <Grid item xs={3}>{t.asset}</Grid>
            <Grid item xs={3}>
              <Button
                variant="contained"
                color="secondary"
                disabled={!!isolated[t.ip]}
                onClick={() => handleIsolate(t.ip)}
              >
                {isolated[t.ip] ? '已隔离' : '隔离'}
              </Button>
            </Grid>
          </Grid>
        ))}
      </Box>
    </Paper>
  </Box>
);

export default ThreatList;
