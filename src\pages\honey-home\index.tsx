import React, { useEffect, useState } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import { 
  Grid, 
  Paper, 
  Typography, 
  LinearProgress,
  Box,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@material-ui/core';
import {
  Memory as CpuIcon,
  Storage as MemoryIcon,
  Save as DiskIcon,
  Error as ErrorIcon,
  Security as SecurityIcon,
} from '@material-ui/icons';
import axios from 'axios';

import Attack<PERSON>hain from './components/attackchain';
import Gauge from './components/gauge';
import SceneSelector from './components/SceneSelector';

// 定义接口返回数据类型
interface StatisticsData {
  online_windows_nodes: number;
  online_non_windows_nodes: number;
  offline_windows_nodes: number;
  offline_non_windows_nodes: number;
  active_honeypots: number;
  inactive_honeypots: number;
}

interface ApiResponse {
  success: boolean;
  data: StatisticsData;
  message: string;
  code: number;
}

const useStyles = makeStyles((theme) => ({
  root: {
    flexGrow: 1,
    padding: theme.spacing(3),
  },
  paper: {
    padding: theme.spacing(1),
    height: '100%',
  },
  statusItem: {
    display: 'flex',
    alignItems: 'center',
    height: '100%',
    padding: theme.spacing(2),
  },
  icon: {
    marginRight: theme.spacing(2),
    display: 'flex',
    alignItems: 'center',
    '& .MuiSvgIcon-root': {
      fontSize: '28px',
    },
  },
  progressContainer: {
    flexGrow: 1,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
  },
  statusWrapper: {
    display: 'flex',
    flexDirection: 'row',
    width: '100%',
    gap: theme.spacing(2),
    height: '100%',
  },
  statusBox: {
    flex: 1,
    padding: theme.spacing(2),
    border: `1px solid ${theme.palette.divider}`,
    borderRadius: theme.shape.borderRadius,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    '& .MuiTypography-h6': {
      fontSize: '16px',
      marginBottom: theme.spacing(1),
      color: '#333',
      textAlign: 'center',
      width: '100%',
    },
  },
  nodeList: {
    marginTop: theme.spacing(2),
    padding: 0,
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
  },
  nodeItem: {
    padding: theme.spacing(0.5),
    minHeight: '32px',
    display: 'flex',
    justifyContent: 'center',
    width: '100%',
    '& .MuiListItemIcon-root': {
      minWidth: '28px',
    },
    '& .MuiTypography-root': {
      fontSize: '13px',
    },
    '& .MuiListItemText-root': {
      margin: 0,
      flex: 'none',
    },
  },
  subNodeItem: {
    padding: theme.spacing(0.5),
    minHeight: '32px',
    display: 'flex',
    justifyContent: 'flex-start',
    paddingLeft: theme.spacing(7),
    '& .MuiListItemIcon-root': {
      minWidth: '28px',
    },
    '& .MuiTypography-root': {
      fontSize: '13px',
    },
    '& .MuiListItemText-root': {
      margin: 0,
      flex: 'none',
    },
  },
  onlineNode: {
    color: '#52c41a',
  },
  offlineNode: {
    color: '#8c8c8c',
  },
  errorNode: {
    color: '#ff4d4f',
  },
  systemLogo: {
    display: 'flex',
    alignItems: 'center',
    padding: theme.spacing(2),
    '& img': {
      width: '90px',
      height: '45px',
      marginRight: theme.spacing(1),
    },
    '& h6': {
      margin: 0,
      fontSize: '16px',
      color: '#333',
    },
  },
  systemIcon: {
    width: '20px',
    height: '20px',
    objectFit: 'contain',
  },
  nodeStatistics: {
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing(0.5),
    '& .MuiListItemIcon-root': {
      minWidth: '28px',
      display: 'flex',
      justifyContent: 'center',
    },
    '& .MuiTypography-root': {
      fontSize: '13px',
      color: '#52c41a',
    },
    '& .MuiListItemText-root': {
      margin: 0,
      flex: 'none',
      textAlign: 'center',
    },
  },
  divider: {
    width: '2px',
    height: '40px',
    backgroundColor: '#d1d1d1',
    margin: '0 16px',
  },
  treeStructure: {
    position: 'relative',
    paddingLeft: theme.spacing(2),
    marginLeft: theme.spacing(2),
    '&::before': {
      content: '""',
      position: 'absolute',
      left: '0',
      top: '0',
      height: '80%',
      width: '1px',
      backgroundColor: '#d9d9d9',
    },
  },
  treeItem: {
    position: 'relative',
    marginBottom: theme.spacing(1),
    '&::before': {
      content: '""',
      position: 'absolute',
      left: '-16px',
      top: '50%',
      width: '16px',
      height: '1px',
      backgroundColor: '#d9d9d9',
    },
  },
  nodeGroupWrapper: {
    border: '1px solid #e8e8e8',
    borderRadius: '4px',
    padding: theme.spacing(1),
    marginBottom: theme.spacing(2),
    position: 'relative',
    '&::before': {
      content: '""',
      position: 'absolute',
      left: 0,
      top: 0,
      bottom: 0,
      width: '4px',
      borderTopLeftRadius: '4px',
      borderBottomLeftRadius: '4px',
    },
  },
  onlineNodeWrapper: {
    '&::before': {
      background: 'linear-gradient(to bottom, #4400ff 33%, #0099ff 33% 66%, #00edff 66%)',
    },
  },
  offlineNodeWrapper: {
    '&::before': {
      background: 'linear-gradient(to bottom, #afb5ba 33%, #d8d6d6 33% 66%, #e5e0e1 66%)',
    },
  },
  onlineHoneypotWrapper: {
    '&::before': {
      backgroundColor: '#4400ff',
    },
  },
  offlineHoneypotWrapper: {
    '&::before': {
      backgroundColor: '#afb5ba',
    },
  },
}));

const StatusIndicator: React.FC<{
  icon: React.ReactNode;
  title: string;
  value: number;
  color: string;
}> = ({ icon, title, value, color }) => {
  const classes = useStyles();
  
  return (
    <div className={classes.statusItem}>
      <Box className={classes.icon}>{icon}</Box>
      <Box className={classes.progressContainer}>
        <Typography variant="subtitle1">{title}</Typography>
        <LinearProgress 
          variant="determinate" 
          value={value} 
          style={{ backgroundColor: `${color}40`, color: color }}
        />
        <Typography variant="body2" color="textSecondary">
          {`${value}%`}
        </Typography>
      </Box>
    </div>
  );
};

// 节点状态组件
const NodeStatus: React.FC<{ data: StatisticsData }> = ({ data }) => {
  const classes = useStyles();
  const totalNodes = data.online_windows_nodes + data.online_non_windows_nodes + 
                    data.offline_windows_nodes + data.offline_non_windows_nodes;
  const onlineNodes = data.online_windows_nodes + data.online_non_windows_nodes;

  return (
    <>
      <Gauge current={onlineNodes} total={totalNodes} />
      
      <List className={classes.nodeList}>
        <div className={`${classes.nodeGroupWrapper} ${classes.onlineNodeWrapper}`}>
          <ListItem className={classes.nodeStatistics}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '4px' }}>
              <ListItemIcon>
                <SecurityIcon style={{ fontSize: '20px', color: '#52c41a' }} />
              </ListItemIcon>
              <ListItemText 
                primary={`在线节点: ${onlineNodes}`}
              />
            </div>
          </ListItem>
          
          <div className={classes.treeStructure}>
            <ListItem className={`${classes.nodeItem} ${classes.treeItem}`}>
              <ListItemIcon>
                <img 
                  src="/Windows.png" 
                  alt="Windows" 
                  className={`${classes.systemIcon} ${classes.onlineNode}`}
                />
              </ListItemIcon>
              <ListItemText 
                primary={`win: ${data.online_windows_nodes}`}
                className={classes.onlineNode}
              />
            </ListItem>
            
            <ListItem className={`${classes.nodeItem} ${classes.treeItem}`}>
              <ListItemIcon>
                <img 
                  src="/linux.png" 
                  alt="Linux" 
                  className={`${classes.systemIcon} ${classes.onlineNode}`}
                />
              </ListItemIcon>
              <ListItemText 
                primary={`linux: ${data.online_non_windows_nodes}`}
                className={classes.onlineNode}
              />
            </ListItem>
          </div>
        </div>

        <div className={`${classes.nodeGroupWrapper} ${classes.offlineNodeWrapper}`}>
          <ListItem className={classes.nodeStatistics}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '4px' }}>
              <ListItemIcon>
                <ErrorIcon style={{ fontSize: '20px' }} className={classes.errorNode} />
              </ListItemIcon>
              <ListItemText 
                primary={`离线节点: ${data.offline_windows_nodes + data.offline_non_windows_nodes}`}
                className={classes.errorNode}
              />
            </div>
          </ListItem>

          <div className={classes.treeStructure}>
            <ListItem className={`${classes.nodeItem} ${classes.treeItem}`}>
              <ListItemIcon>
                <img 
                  src="/Windows.png" 
                  alt="Offline Windows" 
                  className={`${classes.systemIcon} ${classes.errorNode}`}
                />
              </ListItemIcon>
              <ListItemText 
                primary={`win: ${data.offline_windows_nodes}`}
                className={classes.errorNode}
              />
            </ListItem>

            <ListItem className={`${classes.nodeItem} ${classes.treeItem}`}>
              <ListItemIcon>
                <img 
                  src="/linux.png" 
                  alt="Offline Linux" 
                  className={`${classes.systemIcon} ${classes.errorNode}`}
                />
              </ListItemIcon>
              <ListItemText 
                primary={`linux: ${data.offline_non_windows_nodes}`}
                className={classes.errorNode}
              />
            </ListItem>
          </div>
        </div>
      </List>
    </>
  );
};

// 蜜罐状态组件
const HoneypotStatus: React.FC<{ data: StatisticsData }> = ({ data }) => {
  const classes = useStyles();
  const totalHoneypots = data.active_honeypots + data.inactive_honeypots;

  return (
    <>
      <Gauge current={data.active_honeypots} total={totalHoneypots} />
      
      <List className={classes.nodeList}>
        <div className={`${classes.nodeGroupWrapper} ${classes.onlineHoneypotWrapper}`}>
          <ListItem className={classes.nodeStatistics}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '4px', width: '100px' }}>
              <ListItemIcon>
                <SecurityIcon style={{ fontSize: '20px' }} className={classes.onlineNode} />
              </ListItemIcon>
              <ListItemText 
                primary={`在线蜜罐: ${data.active_honeypots}`}
                className={classes.onlineNode}
              />
            </div>
          </ListItem>
        </div>

        <div className={`${classes.nodeGroupWrapper} ${classes.offlineHoneypotWrapper}`}>
          <ListItem className={classes.nodeStatistics}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '4px', width: '100px' }}>
              <ListItemIcon>
                <ErrorIcon style={{ fontSize: '20px' }} className={classes.errorNode} />
              </ListItemIcon>
              <ListItemText 
                primary={`离线蜜罐: ${data.inactive_honeypots}`}
                className={classes.errorNode}
              />
            </div>
          </ListItem>
        </div>
      </List>
    </>
  );
};

const HoneyHome: React.FC = () => {
  const classes = useStyles();
  const [statisticsData, setStatisticsData] = useState<StatisticsData>({
    online_windows_nodes: 0,
    online_non_windows_nodes: 0,
    offline_windows_nodes: 0,
    offline_non_windows_nodes: 0,
    active_honeypots: 0,
    inactive_honeypots: 0
  });

  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        const response = await axios.get<ApiResponse>(`${import.meta.env.VITE_BACK_URL}/api/statistics`);
        if (response.data.success) {
          setStatisticsData(response.data.data);
        }
      } catch (error) {
        console.error('获取统计数据失败:', error);
      }
    };

    fetchStatistics();
  }, []);

  return (
    <div className={classes.root}>
      <Grid container spacing={3}>
        {/* 系统状态指标 */}
        <Grid item xs={12}>
          <Paper className={classes.paper}>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={2}>
                {/* <div className={classes.systemLogo} style={{ background: 'linear-gradient(135deg, #dfc389 0%, #c4a366 100%)', }}> */}
                <div className={classes.systemLogo} >
                    <img src="/state-grid.png" alt="蜜罐系统" />
                    <Typography variant="h6">蜜罐系统</Typography>
                </div>
              </Grid>

              <Grid item>
                <div className={classes.systemLogo}>
                  <Typography variant="h6">最新</Typography>
                </div>
              </Grid>
              
              <Grid item>
                <div className={classes.divider} />
              </Grid>
              
              <Grid item xs={2}>
                <StatusIndicator
                  icon={<CpuIcon />}
                  title="CPU"
                  value={21}
                  color="#4caf50"
                />
              </Grid>

              <Grid item>
                <div className={classes.divider} />
              </Grid>

              <Grid item xs={2}>
                <StatusIndicator
                  icon={<MemoryIcon />}
                  title="内存"
                  value={21}
                  color="#2196f3"
                />
              </Grid>

              <Grid item>
                <div className={classes.divider} />
              </Grid>

              <Grid item xs={2}>
                <StatusIndicator
                  icon={<DiskIcon />}
                  title="磁盘"
                  value={27}
                  color="#ff9800"
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* 蜜罐场景选择器 */}
        <Grid item xs={12}>
          <SceneSelector />
        </Grid>

        {/* 节点状态和蜜罐状态 */}
        <Grid item xs={4}>
          <Paper className={classes.paper}>
            <div className={classes.statusWrapper}>
              <div className={classes.statusBox}>
                <Typography variant="h6" gutterBottom>
                  节点状态
                </Typography>
                <NodeStatus data={statisticsData} />
              </div>
              <div className={classes.statusBox}>
                <Typography variant="h6" gutterBottom>
                  蜜罐状态
                </Typography>
                <HoneypotStatus data={statisticsData} />
              </div>
            </div>
          </Paper>
        </Grid>

        {/* 攻击链 */}
        <Grid item xs={8}>
          <Paper className={classes.paper}>
            <Typography variant="h6" gutterBottom>
              攻击链
            </Typography>
            <AttackChain />
          </Paper>
        </Grid>
      </Grid>
    </div>
  );
};

export default HoneyHome;