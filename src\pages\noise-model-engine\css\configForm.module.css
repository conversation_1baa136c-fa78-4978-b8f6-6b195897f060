.form {
  width: 100%;
}
.content {
  padding-top: 8px;
}
.section {
  margin-bottom: 32px;
}
.sectionTitle {
  font-size: 17px;
  font-weight: 700;
  color: #219653;
  margin-bottom: 18px;
  display: flex;
  align-items: center;
  gap: 6px;
}
.formRow {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
}
.label {
  flex: 0 0 140px;
  font-size: 15px;
  color: #222;
  font-weight: 500;
  margin-right: 16px;
  text-align: right;
}
.input {
  flex: 1;
  font-size: 15px;
  padding: 10px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  outline: none;
  background: #fff;
  transition: border 0.2s;
  color: #000; 
}
.input:focus {
  border-color: #219653;
}
.sliderGroup {
  display: flex;
  align-items: center;
  width: 100%;
}
.slider {
  flex: 1;
  margin-right: 12px;
  color: #666 !important;
}
.sliderValue {
  width: 36px;
  text-align: right;
  font-size: 15px;
  color: #333;
  margin-left: 8px;
}

.expressionTextarea {
  width: 100%;
  background-color: #ffffff;
  color: #090909;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  font-family: monospace;
  resize: vertical;
  box-sizing: border-box;
  font-size: 15px
}


.expressionTextarea::placeholder {
  color: #666; 
  font-size: 15px
}

.expressionTip {
  display: block;
  margin-top: 4px;
  color: #666;
  font-size: 15px;
}
.columnWrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
}