import React, { useState } from 'react';
import {
  Box,
  Grid,
  Checkbox,
  FormControlLabel,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@material-ui/core';
import { FilterConditions, AlertStatus, AlertType, SeverityLevel, DataSource } from './index';

interface SingleAlertFilterProps {
  conditions: FilterConditions;
  onFilterChange: (conditions: FilterConditions) => void;
  onReset: () => void;
}

const SingleAlertFilter: React.FC<SingleAlertFilterProps> = ({
  conditions,
  onFilterChange,
  onReset,
}) => {
  // 本地状态，用于跟踪UI中的变化
  const [localConditions, setLocalConditions] = useState<FilterConditions>(conditions);

  // 当外部条件变化时更新本地状态
  React.useEffect(() => {
    setLocalConditions(conditions);
  }, [conditions]);

  // 处理状态复选框变化
  const handleStatusChange = (status: AlertStatus) => {
    const newStatus = localConditions.status.includes(status)
      ? localConditions.status.filter(s => s !== status)
      : [...localConditions.status, status];
    
    const newConditions = { ...localConditions, status: newStatus };
    setLocalConditions(newConditions);
  };

  // 处理告警类型复选框变化
  const handleAlertTypeChange = (type: AlertType) => {
    const newTypes = localConditions.alertTypes.includes(type)
      ? localConditions.alertTypes.filter(t => t !== type)
      : [...localConditions.alertTypes, type];
    
    const newConditions = { ...localConditions, alertTypes: newTypes };
    setLocalConditions(newConditions);
  };

  // 处理严重程度复选框变化
  const handleSeverityChange = (level: SeverityLevel) => {
    const newLevels = localConditions.severityLevels.includes(level)
      ? localConditions.severityLevels.filter(l => l !== level)
      : [...localConditions.severityLevels, level];
    
    const newConditions = { ...localConditions, severityLevels: newLevels };
    setLocalConditions(newConditions);
  };

  // 处理数据源复选框变化
  const handleDataSourceChange = (source: DataSource) => {
    const newSources = localConditions.dataSources.includes(source)
      ? localConditions.dataSources.filter(s => s !== source)
      : [...localConditions.dataSources, source];
    
    const newConditions = { ...localConditions, dataSources: newSources };
    setLocalConditions(newConditions);
  };

  // 处理文本输入变化
  const handleTextChange = (field: keyof FilterConditions) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setLocalConditions({
      ...localConditions,
      [field]: event.target.value,
    });
  };

  // 处理排序字段变化
  const handleSortChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setLocalConditions({
      ...localConditions,
      sortField: event.target.value as string,
    });
  };

  // 处理排序方向变化
  const handleSortDirectionChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setLocalConditions({
      ...localConditions,
      sortDirection: event.target.value as 'asc' | 'desc',
    });
  };

  // 提交筛选
  const handleSubmit = () => {
    console.log('提交筛选条件:', localConditions);
    onFilterChange({...localConditions});
  };

  // 重置筛选
  const handleReset = () => {
    onReset();
  };

  // 检查状态是否被选中
  const isStatusChecked = (status: AlertStatus) => {
    return localConditions.status.includes(status);
  };

  // 检查告警类型是否被选中
  const isAlertTypeChecked = (type: AlertType) => {
    return localConditions.alertTypes.includes(type);
  };

  // 检查严重程度是否被选中
  const isSeverityChecked = (level: SeverityLevel) => {
    return localConditions.severityLevels.includes(level);
  };

  // 检查数据源是否被选中
  const isDataSourceChecked = (source: DataSource) => {
    return localConditions.dataSources.includes(source);
  };

  return (
    <Box className="single-alert-filter-form">
      <Grid container spacing={3}>
        {/* 状态筛选 */}
        <Grid item xs={12} md={4}>
          <Box className="single-alert-filter-section">
            <h4>状态</h4>
            <Box display="flex" flexWrap="wrap" style={{ gap: '8px' }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isStatusChecked('待处理')}
                    onChange={() => handleStatusChange('待处理')}
                    color="primary"
                  />
                }
                label="待处理"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isStatusChecked('处理中')}
                    onChange={() => handleStatusChange('处理中')}
                    color="primary"
                  />
                }
                label="处理中"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isStatusChecked('已处理')}
                    onChange={() => handleStatusChange('已处理')}
                    color="primary"
                  />
                }
                label="已处理"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isStatusChecked('已忽略')}
                    onChange={() => handleStatusChange('已忽略')}
                    color="primary"
                  />
                }
                label="已忽略"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isStatusChecked('误报')}
                    onChange={() => handleStatusChange('误报')}
                    color="primary"
                  />
                }
                label="误报"
              />
            </Box>
          </Box>
        </Grid>

        {/* 告警类型筛选 */}
        <Grid item xs={12} md={4}>
          <Box className="single-alert-filter-section">
            <h4>告警类型</h4>
            <Box display="flex" flexWrap="wrap" style={{ gap: '8px' }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isAlertTypeChecked('恶意攻击')}
                    onChange={() => handleAlertTypeChange('恶意攻击')}
                    color="primary"
                  />
                }
                label="恶意攻击"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isAlertTypeChecked('异常行为')}
                    onChange={() => handleAlertTypeChange('异常行为')}
                    color="primary"
                  />
                }
                label="异常行为"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isAlertTypeChecked('系统异常')}
                    onChange={() => handleAlertTypeChange('系统异常')}
                    color="primary"
                  />
                }
                label="系统异常"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isAlertTypeChecked('网络异常')}
                    onChange={() => handleAlertTypeChange('网络异常')}
                    color="primary"
                  />
                }
                label="网络异常"
              />
            </Box>
          </Box>
        </Grid>

        {/* 严重程度筛选 */}
        <Grid item xs={12} md={4}>
          <Box className="single-alert-filter-section">
            <h4>严重程度</h4>
            <Box display="flex" flexWrap="wrap" style={{ gap: '8px' }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isSeverityChecked('低')}
                    onChange={() => handleSeverityChange('低')}
                    color="primary"
                  />
                }
                label="低"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isSeverityChecked('中')}
                    onChange={() => handleSeverityChange('中')}
                    color="primary"
                  />
                }
                label="中"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isSeverityChecked('高')}
                    onChange={() => handleSeverityChange('高')}
                    color="primary"
                  />
                }
                label="高"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isSeverityChecked('严重')}
                    onChange={() => handleSeverityChange('严重')}
                    color="primary"
                  />
                }
                label="严重"
              />
            </Box>
          </Box>
        </Grid>

        {/* 数据源筛选 */}
        {/* <Grid item xs={12} md={3}>
          <Box className="single-alert-filter-section">
            <h4>数据源</h4>
            <Box display="flex" flexWrap="wrap" style={{ gap: '8px' }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isDataSourceChecked('蜜罐')}
                    onChange={() => handleDataSourceChange('蜜罐')}
                    color="primary"
                  />
                }
                label="蜜罐"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isDataSourceChecked('日志')}
                    onChange={() => handleDataSourceChange('日志')}
                    color="primary"
                  />
                }
                label="日志"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isDataSourceChecked('网络流量')}
                    onChange={() => handleDataSourceChange('网络流量')}
                    color="primary"
                  />
                }
                label="网络流量"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isDataSourceChecked('系统监控')}
                    onChange={() => handleDataSourceChange('系统监控')}
                    color="primary"
                  />
                }
                label="系统监控"
              />
            </Box>
          </Box>
        </Grid> */}

        {/* IP地址筛选 */}
        <Grid item xs={12} md={6}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="源IP地址"
                variant="outlined"
                size="small"
                value={localConditions.sourceIp || ''}
                onChange={handleTextChange('sourceIp')}
                placeholder="输入源IP地址"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="目标IP地址"
                variant="outlined"
                size="small"
                value={localConditions.targetIp || ''}
                onChange={handleTextChange('targetIp')}
                placeholder="输入目标IP地址"
              />
            </Grid>
          </Grid>
        </Grid>

        {/* 时间范围筛选 */}
        <Grid item xs={12} md={6}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="开始时间"
                type="datetime-local"
                variant="outlined"
                size="small"
                value={localConditions.startTime || ''}
                onChange={handleTextChange('startTime')}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="结束时间"
                type="datetime-local"
                variant="outlined"
                size="small"
                value={localConditions.endTime || ''}
                onChange={handleTextChange('endTime')}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
          </Grid>
        </Grid>

        {/* 排序选项 */}
        <Grid item xs={12} md={12}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControl variant="outlined" size="small" fullWidth>
                <InputLabel>排序字段</InputLabel>
                <Select
                  value={localConditions.sortField}
                  onChange={handleSortChange}
                  label="排序字段"
                >
                  <MenuItem value="detect_time">检测时间</MenuItem>
                  <MenuItem value="alert_name">告警名称</MenuItem>
                  <MenuItem value="severity">严重程度</MenuItem>
                  <MenuItem value="status">状态</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl variant="outlined" size="small" fullWidth>
                <InputLabel>排序方向</InputLabel>
                <Select
                  value={localConditions.sortDirection}
                  onChange={handleSortDirectionChange}
                  label="排序方向"
                >
                  <MenuItem value="desc">降序</MenuItem>
                  <MenuItem value="asc">升序</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Grid>

        {/* 操作按钮 */}
        <Grid item xs={12}>
          <Box className="single-alert-filter-actions">
            <Button
              variant="outlined"
              onClick={handleReset}
              className="single-alert-filter-reset-btn"
              style={{ marginRight: 16 }}
            >
              重置
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={handleSubmit}
              className="single-alert-filter-submit-btn"
            >
              筛选
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SingleAlertFilter;
