// RoseChart.tsx
import React, { forwardRef } from 'react';
import ReactECharts from 'echarts-for-react';

interface RoseChartProps {
  title: string;
  data: { value: number; name: string }[];
  max: number;
  interval: number;
}

const RoseChart = forwardRef<any, RoseChartProps>(({ title, data, max, interval }, ref) => {
  const colors = [
    '#f56c6c', '#e67e22', '#f39c12', '#2ecc71', '#1abc9c',
    '#3498db', '#9b59b6', '#34495e', '#7f8c8d', '#95a5a6',
  ];

  const center = ['50%', '45%'];
  const radius = '72%';

  const isEmpty = data.length === 0;

  const placeholderData = isEmpty
    ? [{ name: 'placeholder', value: 0 }]
    : data;

    const option = {
      title: {
        text: title,
        left: 'center',
        top: 10,
        textStyle: {
          fontSize: 14,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
        show: !isEmpty,
      },
      legend: {
        show: !isEmpty,
        bottom: 0,
        left: 'center',
        orient: 'horizontal',
        textStyle: {
          fontSize: 12,
        },
        data: data.map((item) => item.name),
        formatter: (name: string) => `${name}  `,
      },
      polar: {
        center: center,
        radius,
      },
      radiusAxis: {
        min: 0,
        max,
        interval,
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          show: true,
          color: '#999',
          fontSize: 10,
          fontWeight: 'bold',
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#bbb',
            type: 'solid',
            width: 1.8,
            opacity: 0.4,
          },
        },
      },
      angleAxis: {
        show: false,
        type: 'category',
        data: placeholderData.map((item) => item.name),
      },
      series: [
        {
          type: 'bar',
          data: new Array(placeholderData.length).fill(0),
          coordinateSystem: 'polar',
          roundCap: true,
          barWidth: 0,
          silent: true,
          z: 1,
        },
        {
          name: title,
          type: 'pie',
          radius,
          center: center,
          data: placeholderData.map((item, index) => ({
            ...item,
            itemStyle: isEmpty
              ? { color: 'transparent' }
              : {
                  opacity: 0.65,
                  borderWidth: 1,
                  color: colors[index % colors.length],
                  borderColor: '#fff',
                },
          })),
          label: { show: false },
          labelLine: { show: false },
          z: 2,
        },
      ],
    };
    

  return <ReactECharts ref={ref} style={{ height: 450, width: '100%'}} option={option} />;
});

export default RoseChart;
