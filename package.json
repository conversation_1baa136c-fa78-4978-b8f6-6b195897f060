{"name": "honeypotadmin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@date-io/date-fns": "^3.2.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@material-ui/core": "^4.12.4", "@material-ui/icons": "^4.11.3", "@material-ui/lab": "4.0.0-alpha.61", "@material-ui/pickers": "^3.3.11", "@material-ui/styles": "4", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.2", "@types/echarts": "^5.0.0", "@types/react-router-dom": "^5.3.3", "@types/vis": "^4.21.27", "Alert": "link:@material-ui/lab/Alert", "axios": "^1.9.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "less": "^4.3.0", "material-ui-dropzone": "^3.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.4.1", "vis-data": "^7.1.9", "vis-network": "^9.1.9"}, "devDependencies": {"@types/react": "^18.0.24", "@types/react-dom": "^18.0.8", "@vitejs/plugin-react": "^2.2.0", "less": "^4.3.0", "less-loader": "^12.3.0", "typescript": "^4.6.4", "vite": "^3.2.3"}}