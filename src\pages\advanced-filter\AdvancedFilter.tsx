import React, { useState } from 'react';
import {
  Box,
  Grid,
  Checkbox,
  FormControlLabel,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@material-ui/core';
import { FilterConditions, TaskStatus, TaskType, InputSource } from './index';

interface AdvancedFilterProps {
  conditions: FilterConditions;
  onFilterChange: (conditions: FilterConditions) => void;
  onReset: () => void;
}

const AdvancedFilter: React.FC<AdvancedFilterProps> = ({
  conditions,
  onFilterChange,
  onReset,
}) => {
  // 本地状态，用于跟踪UI中的变化
  const [localConditions, setLocalConditions] = useState<FilterConditions>(conditions);

  // 当外部条件变化时更新本地状态
  React.useEffect(() => {
    setLocalConditions(conditions);
  }, [conditions]);

  // 处理状态复选框变化
  const handleStatusChange = (status: TaskStatus) => {
    const newStatus = localConditions.status.includes(status)
      ? localConditions.status.filter(s => s !== status)
      : [...localConditions.status, status];
    
    const newConditions = { ...localConditions, status: newStatus };
    setLocalConditions(newConditions);
  };

  // 处理任务类型复选框变化
  const handleTaskTypeChange = (type: TaskType) => {
    const newTypes = localConditions.taskTypes.includes(type)
      ? localConditions.taskTypes.filter(t => t !== type)
      : [...localConditions.taskTypes, type];
    
    const newConditions = { ...localConditions, taskTypes: newTypes };
    setLocalConditions(newConditions);
  };

  // 处理输入源复选框变化
  const handleInputSourceChange = (source: InputSource) => {
    const newSources = localConditions.inputSources.includes(source)
      ? localConditions.inputSources.filter(s => s !== source)
      : [...localConditions.inputSources, source];
    
    const newConditions = { ...localConditions, inputSources: newSources };
    setLocalConditions(newConditions);
  };

  // 处理文本输入变化
  const handleTextChange = (field: 'channelId' | 'nodeId' | 'creatorName') => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setLocalConditions({
      ...localConditions,
      [field]: event.target.value,
    });
  };

  // 处理排序方式变化
  const handleSortChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setLocalConditions({
      ...localConditions,
      sortField: event.target.value as string,
    });
  };

  // 处理排序方向变化
  const handleSortDirectionChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setLocalConditions({
      ...localConditions,
      sortDirection: event.target.value as 'asc' | 'desc',
    });
  };

  // 提交筛选
  const handleSubmit = () => {
    console.log('提交筛选条件:', localConditions);
    onFilterChange({...localConditions}); // 传递本地状态的副本，确保对象引用发生变化
  };

  // 重置筛选
  const handleReset = () => {
    onReset();
  };

  // 检查状态是否被选中，处理空数组的情况
  const isStatusChecked = (status: TaskStatus) => {
    return localConditions.status.includes(status);
  };

  // 检查任务类型是否被选中，处理空数组的情况
  const isTaskTypeChecked = (type: TaskType) => {
    return localConditions.taskTypes.includes(type);
  };

  // 检查输入源是否被选中，处理空数组的情况
  const isInputSourceChecked = (source: InputSource) => {
    return localConditions.inputSources.includes(source);
  };

  return (
    <Box className="advanced-filter-form">
      <Grid container spacing={3}>
        {/* 状态筛选 */}
        <Grid item xs={12} md={4}>
          <Box className="advanced-filter-section">
            <h4>状态</h4>
            <Box display="flex" flexWrap="wrap" style={{ gap: '8px' }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isStatusChecked('待处理')}
                    onChange={() => handleStatusChange('待处理')}
                    color="primary"
                  />
                }
                label="待处理"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isStatusChecked('运行中')}
                    onChange={() => handleStatusChange('运行中')}
                    color="primary"
                  />
                }
                label="运行中"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isStatusChecked('已完成')}
                    onChange={() => handleStatusChange('已完成')}
                    color="primary"
                  />
                }
                label="已完成"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isStatusChecked('已取消')}
                    onChange={() => handleStatusChange('已取消')}
                    color="primary"
                  />
                }
                label="已取消"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isStatusChecked('失败')}
                    onChange={() => handleStatusChange('失败')}
                    color="primary"
                  />
                }
                label="失败"
              />
            </Box>
          </Box>
        </Grid>

        {/* 任务类型筛选 */}
        <Grid item xs={12} md={2}>
          <Box className="advanced-filter-section">
            <h4>任务类型</h4>
            <Box display="flex" flexWrap="wrap" style={{ gap: '8px' }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isTaskTypeChecked('批量')}
                    onChange={() => handleTaskTypeChange('批量')}
                    color="primary"
                  />
                }
                label="批量"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isTaskTypeChecked('实时')}
                    onChange={() => handleTaskTypeChange('实时')}
                    color="primary"
                  />
                }
                label="实时"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isTaskTypeChecked('计划')}
                    onChange={() => handleTaskTypeChange('计划')}
                    color="primary"
                  />
                }
                label="计划"
              />
            </Box>
          </Box>
        </Grid>

        {/* 输入源筛选 */}
        <Grid item xs={12} md={6}>
          <Box className="advanced-filter-section">
            <h4>输入源</h4>
            <Box display="flex" flexWrap="wrap" style={{ gap: '8px' }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isInputSourceChecked('文件')}
                    onChange={() => handleInputSourceChange('文件')}
                    color="primary"
                  />
                }
                label="文件"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isInputSourceChecked('文本')}
                    onChange={() => handleInputSourceChange('文本')}
                    color="primary"
                  />
                }
                label="文本"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isInputSourceChecked('数据库')}
                    onChange={() => handleInputSourceChange('数据库')}
                    color="primary"
                  />
                }
                label="数据库"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isInputSourceChecked('蜜罐')}
                    onChange={() => handleInputSourceChange('蜜罐')}
                    color="primary"
                  />
                }
                label="蜜罐"
              />
            </Box>
          </Box>
        </Grid>

        {/* 渠道ID和节点ID */}
        {/* <Grid item xs={12} md={6}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="渠道ID"
                variant="outlined"
                size="small"
                value={localConditions.channelId || ''}
                onChange={handleTextChange('channelId')}
                placeholder="输入渠道ID"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="节点ID"
                variant="outlined"
                size="small"
                value={localConditions.nodeId || ''}
                onChange={handleTextChange('nodeId')}
                placeholder="输入节点ID"
              />
            </Grid>
          </Grid>
        </Grid> */}

        {/* 创建者 */}
        {/* <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="创建者"
            variant="outlined"
            size="small"
            value={localConditions.creatorName || ''}
            onChange={handleTextChange('creatorName')}
            placeholder="输入创建者名称"
          />
        </Grid> */}

        {/* 排序方式 */}
        <Grid item xs={12} md={6}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControl variant="outlined" size="small" fullWidth>
                <InputLabel>排序字段</InputLabel>
                <Select
                  value={localConditions.sortField}
                  onChange={handleSortChange}
                  label="排序字段"
                >
                  <MenuItem value="createTime">创建时间</MenuItem>
                  <MenuItem value="taskName">任务名称</MenuItem>
                  <MenuItem value="status">状态</MenuItem>
                  <MenuItem value="type">类型</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl variant="outlined" size="small" fullWidth>
                <InputLabel>按时间排序</InputLabel>
                <Select
                  value={localConditions.sortDirection}
                  onChange={handleSortDirectionChange}
                  label="排序方式"
                >
                  <MenuItem value="asc">升序</MenuItem>
                  <MenuItem value="desc">降序</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Grid>

        {/* 按钮区域 */}
        <Grid item xs={12} className="advanced-filter-actions">
          <Box display="flex" justifyContent="flex-end">
            <Button
              variant="outlined"
              color="default"
              onClick={handleReset}
              className="advanced-filter-reset-btn"
            >
              重置
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={handleSubmit}
              className="advanced-filter-submit-btn"
              style={{ marginLeft: '10px' }}
            >
              筛选
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AdvancedFilter; 