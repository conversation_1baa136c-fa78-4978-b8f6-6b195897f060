// Header.js
import React from 'react';
import { makeStyles, Box, Typography, Avatar, Paper } from '@material-ui/core';
import PersonIcon from '@material-ui/icons/Person';


export default function Header() {
  const classes = useStyles();
  
  return (
    <Paper className={classes.root}>
      <Box className={classes.logo}>
        <Box className={classes.logoIcon}>⚡</Box>
        <Typography className={classes.logoText}>电力网络安全智能降噪引擎</Typography>
      </Box>
      <Box className={classes.statusInfo}>
        <Box className={classes.statusIndicator}>
          <span className={classes.statusDot}></span>
          <span>运行状态: 正常</span>
        </Box>
        <Box className={classes.userProfile}>
          <Avatar className={classes.userAvatar}><PersonIcon /></Avatar>
          <span>系统管理员</span>
        </Box>
      </Box>
    </Paper>
  );
}


const useStyles = makeStyles((theme) => ({
  root: {
    background: '#fff',
    borderRadius: 12,
    padding: '15px 30px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    boxShadow: '0 6px 15px rgba(0,0,0,0.08)',
    borderLeft: '5px solid #2E7D32',
    marginBottom: 30,
  },
  logo: {
    display: 'flex',
    alignItems: 'center',
    gap: 15,
  },
  logoIcon: {
    width: 50,
    height: 50,
    background: 'linear-gradient(135deg, #2E7D32, #8BC34A)',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#fff',
    fontSize: 22,
    boxShadow: '0 3px 10px rgba(76,175,80,0.4)',
  },
  logoText: {
    fontSize: '1.8rem',
    fontWeight: 700,
    background: 'linear-gradient(90deg, #2E7D32, #8BC34A)',
    WebkitBackgroundClip: 'text',
    backgroundClip: 'text',
    color: 'transparent',
  },
  statusInfo: {
    display: 'flex',
    alignItems: 'center',
    gap: 20,
  },
  statusIndicator: {
    display: 'flex',
    alignItems: 'center',
    gap: 8,
    fontWeight: 500,
    color: '#2E7D32',
  },
  statusDot: {
    width: 14,
    height: 14,
    borderRadius: '50%',
    background: '#2E7D32',
    boxShadow: '0 0 8px #8BC34A',
  },
  userProfile: {
    display: 'flex',
    alignItems: 'center',
    gap: 10,
    background: 'linear-gradient(45deg, #2E7D32, #8BC34A)',
    padding: '8px 15px',
    borderRadius: 30,
    color: '#fff',
    fontWeight: 500,
  },
  userAvatar: {
    width: 35,
    height: 35,
    borderRadius: '50%',
    background: '#fff',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#2E7D32',
  },
}));
