import React, { useState, useEffect } from 'react'
import { makeStyles } from '@material-ui/core/styles'
import { useNavigate, useLocation } from 'react-router-dom'
import {
  AppBar,
  Collapse,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Typography,
} from '@material-ui/core'
import HomeIcon from '@material-ui/icons/Home'
import SecurityIcon from '@material-ui/icons/Security'
import AssignmentIcon from '@material-ui/icons/Assignment'
import DnsIcon from '@material-ui/icons/Dns'
import ExpandLessIcon from '@material-ui/icons/ExpandLess'
import ExpandMoreIcon from '@material-ui/icons/ExpandMore'
import DragIndicatorIcon from '@material-ui/icons/DragIndicator'
import HorizontalSplitIcon from '@material-ui/icons/HorizontalSplit'
import FormatListBulletedIcon from '@material-ui/icons/FormatListBulleted'
import PageviewIcon from '@material-ui/icons/Pageview';
import ExtensionIcon from '@material-ui/icons/Extension';
// import StarOutlineIcon from '@material-ui/icons/StarOutline';
import FormatIndentIncreaseIcon from '@material-ui/icons/FormatIndentIncrease';
import Filter1Icon from '@material-ui/icons/Filter1';
import EcoIcon from '@material-ui/icons/Eco';
import SpaIcon from '@material-ui/icons/Spa';
import RedditIcon from '@material-ui/icons/Reddit';
import BallotIcon from '@material-ui/icons/Ballot';
import AssignmentLateIcon from '@material-ui/icons/AssignmentLate';
import FeedbackIcon from '@material-ui/icons/Feedback';
import FormatListNumberedIcon from '@material-ui/icons/FormatListNumbered';
import FormatLineSpacingIcon from '@material-ui/icons/FormatLineSpacing';
import Chat from '../../pages/chat/chat'
const drawerWidth = 240
const miniDrawerWidth = 57
const useStyles = makeStyles((theme) => ({
  root: {
    display: 'flex',
    width: '100%',
    height: '100vh',
    overflow: 'hidden',
    flexDirection: 'column',
  },
  appBar: {
    zIndex: theme.zIndex.drawer + 1,
    backgroundColor: 'rgb(19, 125, 62)',
    position: 'relative',
    height: '60px',
  },
  drawer: {
    width: drawerWidth,
    flexShrink: 0,
    whiteSpace: 'nowrap',
  },
  listItemIcon: {
    marginRight: -22, // 可选：图标和文字之间的间距
  },
  logo: {
    height: 40,
    marginRight: theme.spacing(2),
  },
  drawerOpen: {
    width: drawerWidth,
    transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
  },
  drawerClose: {
    transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
    overflowX: 'hidden',
    width: miniDrawerWidth,
  },
  drawerPaper: {
    overflowX: 'hidden',
  },
  drawerContainer: {
    overflow: 'hidden',
  },
//   drawerContainer: {
//   overflowY: 'auto',
//   height: '100%',
// },

  content: {
    flexGrow: 1,
    padding: theme.spacing(3),
    width: '100%',
    height: '100%',
    overflow: 'auto',
    transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
  },
  contentShift: {
    width: `calc(100% - ${drawerWidth}px)`,
    transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
  },
  toolbar: theme.mixins.toolbar,
}))

interface MainLayoutProps {
  children: React.ReactNode
}

// open 对象的类型
interface OpenState {
  [key: string]: boolean
}


const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const classes = useStyles()
  const navigate = useNavigate()
  const [open, setOpen] = useState<OpenState>({}) // 存在二级菜单的开关状态
  const [silderOpen, setSilderOpen] = useState(false) // 左侧Silder的开关状态

  const handleDrawerToggle = () => {
    setSilderOpen(!silderOpen)
  }

  const handleClick = (path: string, hasChildren: boolean, index: number) => {
    if (hasChildren) {
      setOpen({ ...open, [index]: !open[index] })
    } else {
      navigate(path)
      console.log('path', path)
    }
  }
const location = useLocation()

useEffect(() => {
  const expandAllKeys = (items: any[], level = 0, parentKey = '', acc: OpenState = {}) => {
    items.forEach((item, index) => {
      const hasChildren = Array.isArray(item.children) && item.children.length > 0
      const key = `${parentKey}${item.text}-${level}-${index}`
      if (hasChildren) {
        acc[key] = true
        expandAllKeys(item.children, level + 1, `${key}-`, acc)
      }
    })
    return acc
  }

  const allOpen = expandAllKeys(menuItems)
  setOpen(allOpen)
}, [])

const menuItems = [
  {
    text: '态势感知',
    icon: <SpaIcon />,
    children: [
      { text: '首页', path: '/', icon: <HomeIcon />},
    ],
  },
  {
    text: '数据管理',
    icon: <Filter1Icon />,
    children: [
      { text: '数据接入管理', path: '/dataAccessManage', icon: <FormatIndentIncreaseIcon /> },
    ],
  },
  {
    text: '智能降噪中心',
    icon: <EcoIcon />,
    children: [
      { text: '告警监控模块', path: '/AlertMonitor', icon: <RedditIcon /> },
      { text: '智能降噪模型引擎', path: '/noiseModelEngine', icon: <RedditIcon /> },
      {
    text: '告警降噪任务管理',
    path: '/noiseAnalysisTask/select',
    icon: <AssignmentIcon />,
  },
  
  // {
  //   text: '告警规则管理',
  //   path: '/alertRulesManagement',
  //   icon: <FormatListBulletedIcon />,
  // },
  // {
  //   text: '告警分析模块',
  //   path: '/alertAnalysis',
  //   icon: <AssignmentIcon />,
  // },
    ],
  },
  {
    text: '日志分析平台',
    icon: <BallotIcon />,
    children: [
      { text: '日志分析模型引擎', path: '/logAnalysisModel', icon: <ExtensionIcon /> },
      { text: '日志分析任务管理', path: '/logAlertAnalysisTask', icon: <AssignmentLateIcon /> },
    ],
  },
  {
    text: '智能蜜罐系统',
    icon: <SecurityIcon />,
    children: [
      { text: '态势感知', path: '/honeyHome',  icon: <PageviewIcon /> },
      {
        text: '威胁列表',
        icon: <HorizontalSplitIcon />,
        children: [
          { text: '攻击列表', path: '/attackList', icon: <FormatListNumberedIcon /> },
          { text: '攻击日志', path: '/attackLogs', icon: <FeedbackIcon /> },
        ],
      },
       {
    text: '蜜罐管理',
    icon: <FormatLineSpacingIcon />,
    children: [
      { text: '蜜罐概览', path: '/honeypotsOverview', icon: <DnsIcon /> },
      { text: '蜜罐管理', path: '/honeypotsManage', icon: <FormatListBulletedIcon /> },
      { text: '节点管理', path: '/pointsManage', icon: <DragIndicatorIcon /> },
    ],
  },
    ],
  },
]
const renderMenuItems = (items: any[], level = 0, parentKey = '') => {
  return items.map((item, index) => {
    const hasChildren = Array.isArray(item.children) && item.children.length > 0
    const key = `${parentKey}${item.text}-${level}-${index}`

    return (
      <React.Fragment key={key}>
       <ListItem
  button
  selected={item.path === location.pathname}
  onClick={() => {
    if (hasChildren) {
      setOpen((prev) => ({ ...prev, [key]: !prev[key] }))
    } else if (item.path) {
      navigate(item.path)
    }
  }}
  style={{
    paddingTop: 6,
    paddingBottom: 5.5,
    paddingLeft: 12 + level * 25,
   backgroundColor: item.path === location.pathname ? '#e0f2f1' : undefined, // 浅绿色背景
    boxShadow: item.path === location.pathname ? 'inset 4px 0 0 0 #137D3E' : undefined, // 左侧绿色边框
  }}
>

          {item.icon && <ListItemIcon className={classes.listItemIcon}>{item.icon}</ListItemIcon>}
          <ListItemText primary={item.text} />
          {hasChildren && (
            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation()
                setOpen((prev) => ({ ...prev, [key]: !prev[key] }))
              }}
            >
              {open[key] ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          )}
        </ListItem>

        {hasChildren && (
          <Collapse in={open[key]} timeout="auto" unmountOnExit>
            <List disablePadding>
              {renderMenuItems(item.children, level + 1, `${key}-`)}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    )
  })
}

  return (
    <div className={classes.root}>
    <AppBar className={classes.appBar}>
  <Toolbar style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
   <img src="/logo.jpg" alt="国家电网" className={classes.logo} />


    <Typography variant="h6" noWrap style={{ fontSize: '25px', fontWeight: 700 }}>
      电力网络安全智能运营系统
    </Typography>
  </Toolbar>
</AppBar>

      <div style={{ display: 'flex', flexDirection: 'row', height: '100%' }}>
        <Drawer
          className={`${classes.drawer} ${
            open ? classes.drawerOpen : classes.drawerClose
          }`}
          variant="permanent"
          anchor="left"
          classes={{
            paper: `${classes.drawerPaper} ${
              open ? classes.drawerOpen : classes.drawerClose
            }`,
          }}>
          <div className={classes.toolbar} />
          <div className={classes.drawerContainer}>
           <List>
  {renderMenuItems(menuItems)}
</List>

          </div>
        </Drawer>
        <main
          style={{ width: '100%' }}
          className={`${classes.content} ${open ? classes.contentShift : ''}`}>
          {children}
        </main>
      </div>
      <Chat/>
    </div>
  )
}

export default MainLayout
