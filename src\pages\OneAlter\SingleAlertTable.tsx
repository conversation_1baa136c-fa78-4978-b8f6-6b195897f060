import React from 'react'
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Typography,
  Chip,
  CircularProgress,
} from '@material-ui/core'
import { AlertData } from './index'
import { useNavigate } from 'react-router-dom'

interface SingleAlertTableProps {
  alerts: AlertData[]
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  onAlertAction: (id: number, action: 'detail' | 'process' | 'ignore') => void
  loading?: boolean
}

// 定义列宽样式
const columnStyles = {
  id: { width: '5%', minWidth: '60px' },
  alertName: { width: '15%', minWidth: '150px' },
  alertType: { width: '10%', minWidth: '100px' },
  status: { width: '8%', minWidth: '80px' },
  severity: { width: '8%', minWidth: '80px' },
  dataSource: { width: '10%', minWidth: '100px' },
  sourceIp: { width: '12%', minWidth: '120px' },
  targetIp: { width: '12%', minWidth: '120px' },
  detectTime: { width: '12%', minWidth: '120px' },
  actions: { width: '8%', minWidth: '120px' },
}

const SingleAlertTable: React.FC<SingleAlertTableProps> = ({
  alerts,
  currentPage,
  totalPages,
  onPageChange,
  onAlertAction,
  loading = false,
}) => {
  const navigate = useNavigate()

  // 获取状态样式
  const getStatusStyle = (status: string) => {
    switch (status) {
      case '待处理':
        return { backgroundColor: '#ff9800', color: 'white' }
      case '处理中':
        return { backgroundColor: '#2196f3', color: 'white' }
      case '已处理':
        return { backgroundColor: '#4caf50', color: 'white' }
      case '已忽略':
        return { backgroundColor: '#9e9e9e', color: 'white' }
      case '误报':
        return { backgroundColor: '#f44336', color: 'white' }
      default:
        return { backgroundColor: '#9e9e9e', color: 'white' }
    }
  }

  // 获取告警类型样式
  const getAlertTypeStyle = (type: string) => {
    switch (type) {
      case '恶意攻击':
        return { backgroundColor: '#f44336', color: 'white' }
      case '异常行为':
        return { backgroundColor: '#ff9800', color: 'white' }
      case '系统异常':
        return { backgroundColor: '#2196f3', color: 'white' }
      case '网络异常':
        return { backgroundColor: '#9c27b0', color: 'white' }
      default:
        return { backgroundColor: '#9e9e9e', color: 'white' }
    }
  }

  // 获取严重程度样式
  const getSeverityStyle = (severity: string) => {
    switch (severity) {
      case '低':
        return { backgroundColor: '#4caf50', color: 'white' }
      case '中':
        return { backgroundColor: '#ff9800', color: 'white' }
      case '高':
        return { backgroundColor: '#f44336', color: 'white' }
      case '严重':
        return { backgroundColor: '#d32f2f', color: 'white' }
      default:
        return { backgroundColor: '#9e9e9e', color: 'white' }
    }
  }

  // 渲染操作按钮
  const renderActionButtons = (alert: AlertData) => {
    const { id, status } = alert

    return (
      <Box className="single-alert-task-actions" display="flex" justifyContent="center" alignItems="center" flexWrap="nowrap">
        <Button
          variant="text"
          color="primary"
          onClick={() => onAlertAction(id, 'detail')}
          className="single-alert-action-btn">
          详情
        </Button>
        {status === '待处理' && (
          <Button
            variant="text"
            color="primary"
            onClick={() => onAlertAction(id, 'process')}
            className="single-alert-action-btn">
            处理
          </Button>
        )}
        {(status === '待处理' || status === '处理中') && (
          <Button
            variant="text"
            color="secondary"
            onClick={() => onAlertAction(id, 'ignore')}
            className="single-alert-action-btn">
            忽略
          </Button>
        )}
      </Box>
    )
  }

  // 渲染空数据提示
  const renderEmptyData = () => {
    return (
      <TableRow>
        <TableCell colSpan={10} align="center" style={{ padding: '40px 0' }}>
          <Typography variant="body1" color="textSecondary">
            暂无告警数据，请调整筛选条件后重试
          </Typography>
        </TableCell>
      </TableRow>
    )
  }

  // 渲染加载中提示
  const renderLoadingData = () => {
    return (
      <TableRow>
        <TableCell colSpan={10} align="center" style={{ padding: '40px 0' }}>
          <Box display="flex" justifyContent="center" alignItems="center">
            <CircularProgress size={24} style={{ marginRight: 10 }} />
            <Typography variant="body1" color="textSecondary">
              正在加载数据...
            </Typography>
          </Box>
        </TableCell>
      </TableRow>
    )
  }

  return (
    <Box className="single-alert-task-table">
      <TableContainer component={Paper}>
        <Table>
          <TableHead className="single-alert-table-head">
            <TableRow>
              <TableCell align="center" style={columnStyles.id}>ID</TableCell>
              <TableCell align="center" style={columnStyles.alertName}>告警名称</TableCell>
              <TableCell align="center" style={columnStyles.alertType}>告警类型</TableCell>
              <TableCell align="center" style={columnStyles.status}>状态</TableCell>
              <TableCell align="center" style={columnStyles.severity}>严重程度</TableCell>
              <TableCell align="center" style={columnStyles.dataSource}>数据源</TableCell>
              <TableCell align="center" style={columnStyles.sourceIp}>源IP</TableCell>
              <TableCell align="center" style={columnStyles.targetIp}>目标IP</TableCell>
              <TableCell align="center" style={columnStyles.detectTime}>检测时间</TableCell>
              <TableCell align="center" style={columnStyles.actions}>操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading
              ? renderLoadingData()
              : alerts.length > 0
              ? alerts.map((alert: AlertData, index: number) => (
                  <TableRow key={alert.id} className="single-alert-table-row">
                    <TableCell align="center" style={columnStyles.id}>
                      {(currentPage - 1) * 10 + index + 1}
                    </TableCell>
                    <TableCell align="center" style={columnStyles.alertName}>
                      <Typography variant="body2" title={alert.alertName}>
                        {alert.alertName.length > 20 ? `${alert.alertName.substring(0, 20)}...` : alert.alertName}
                      </Typography>
                    </TableCell>
                    <TableCell align="center" style={columnStyles.alertType}>
                      <Chip
                        label={alert.alertType}
                        size="small"
                        style={getAlertTypeStyle(alert.alertType)}
                        className="single-alert-type-chip"
                      />
                    </TableCell>
                    <TableCell align="center" style={columnStyles.status}>
                      <Chip
                        label={alert.status}
                        size="small"
                        style={getStatusStyle(alert.status)}
                        className="single-alert-status-chip"
                      />
                    </TableCell>
                    <TableCell align="center" style={columnStyles.severity}>
                      <Chip
                        label={alert.severity}
                        size="small"
                        style={getSeverityStyle(alert.severity)}
                        className="single-alert-severity-chip"
                      />
                    </TableCell>
                    <TableCell align="center" style={columnStyles.dataSource}>
                      {alert.dataSource}
                    </TableCell>
                    <TableCell align="center" style={columnStyles.sourceIp}>
                      {alert.sourceIp}
                    </TableCell>
                    <TableCell align="center" style={columnStyles.targetIp}>
                      {alert.targetIp}
                    </TableCell>
                    <TableCell align="center" style={columnStyles.detectTime}>
                      {(() => {
                        try {
                          // 尝试解析时间戳并格式化
                          const date = new Date(alert.detectTime);
                          if (!isNaN(date.getTime())) {
                            return date.toLocaleString('zh-CN', {
                              year: 'numeric',
                              month: '2-digit',
                              day: '2-digit',
                              hour: '2-digit',
                              minute: '2-digit',
                              second: '2-digit',
                              hour12: false
                            });
                          }
                          return alert.detectTime; // 如果解析失败，返回原始值
                        } catch (error) {
                          return alert.detectTime;
                        }
                      })()}
                    </TableCell>
                    <TableCell align="center" style={columnStyles.actions}>
                      {renderActionButtons(alert)}
                    </TableCell>
                  </TableRow>
                ))
              : renderEmptyData()}
          </TableBody>
        </Table>
      </TableContainer>

      {/* 分页控件 */}
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        mt={2}
        className="single-alert-pagination">
        <Button
          disabled={currentPage <= 1 || alerts.length === 0 || loading}
          onClick={() => onPageChange(currentPage - 1)}
          className="single-alert-page-btn">
          上一页
        </Button>
        
        <Box mx={2}>
          <Typography variant="body2" color="textSecondary">
            第 {currentPage} 页，共 {totalPages} 页
          </Typography>
        </Box>
        
        <Button
          disabled={currentPage >= totalPages || alerts.length === 0 || loading}
          onClick={() => onPageChange(currentPage + 1)}
          className="single-alert-page-btn">
          下一页
        </Button>
      </Box>
    </Box>
  )
}

export default SingleAlertTable
