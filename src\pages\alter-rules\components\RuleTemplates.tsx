import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardActions,
  Button,
  Grid,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Security as SecurityIcon,
  Schedule as ScheduleIcon,
  FilterList as FilterIcon,
  Psychology as PsychologyIcon,
} from '@mui/icons-material';

interface RuleTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: React.ReactNode;
  ruleData: {
    rule_name: string;
    rule_type: string;
    description: string;
    conditions: any;
    action: string;
    severity_filter?: string[];
    time_window_minutes?: number;
    count_threshold?: number;
    priority: number;
  };
}

const ruleTemplates: RuleTemplate[] = [
  {
    id: 'login_failure_burst',
    name: '登录失败爆发检测',
    description: '检测短时间内大量登录失败的攻击行为',
    category: '安全检测',
    icon: <SecurityIcon />,
    ruleData: {
      rule_name: '登录失败爆发检测',
      rule_type: 'threshold',
      description: '检测5分钟内超过10次登录失败的异常行为',
      conditions: {
        title: { operator: 'contains', value: 'login failed' },
        description: { operator: 'contains', value: 'authentication' }
      },
      action: 'merge',
      severity_filter: ['HIGH', 'MEDIUM'],
      time_window_minutes: 5,
      count_threshold: 10,
      priority: 2,
    },
  },
  {
    id: 'maintenance_window',
    name: '维护窗口抑制',
    description: '在维护时间窗口内抑制常规告警',
    category: '时间管理',
    icon: <ScheduleIcon />,
    ruleData: {
      rule_name: '维护窗口抑制',
      rule_type: 'time_based',
      description: '在维护时间窗口内抑制低优先级告警',
      conditions: {
        severity: { operator: 'equals', value: 'LOW' }
      },
      action: 'suppress',
      severity_filter: ['LOW', 'INFO'],
      time_window_minutes: 120,
      priority: 8,
    },
  },
  {
    id: 'duplicate_suppression',
    name: '重复告警抑制',
    description: '抑制相同来源的重复告警',
    category: '去重处理',
    icon: <FilterIcon />,
    ruleData: {
      rule_name: '重复告警抑制',
      rule_type: 'pattern',
      description: '抑制来自同一来源的重复告警',
      conditions: {
        source: { operator: 'equals', value: '*' },
        title: { operator: 'equals', value: '*' }
      },
      action: 'merge',
      time_window_minutes: 30,
      count_threshold: 3,
      priority: 5,
    },
  },
  {
    id: 'ai_false_positive',
    name: 'AI误报识别',
    description: '使用AI模型识别和抑制误报告警',
    category: 'AI增强',
    icon: <PsychologyIcon />,
    ruleData: {
      rule_name: 'AI误报识别',
      rule_type: 'ml_based',
      description: '使用机器学习模型识别误报模式',
      conditions: {
        confidence_threshold: { operator: 'less_than', value: '0.7' }
      },
      action: 'adjust_priority',
      priority: 3,
    },
  },
];

interface RuleTemplatesProps {
  onSelectTemplate: (template: RuleTemplate) => void;
}

const RuleTemplates: React.FC<RuleTemplatesProps> = ({ onSelectTemplate }) => {
  const [selectedTemplate, setSelectedTemplate] = useState<RuleTemplate | null>(null);
  const [previewOpen, setPreviewOpen] = useState(false);

  const handlePreview = (template: RuleTemplate) => {
    setSelectedTemplate(template);
    setPreviewOpen(true);
  };

  const handleUseTemplate = () => {
    if (selectedTemplate) {
      onSelectTemplate(selectedTemplate);
      setPreviewOpen(false);
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case '安全检测': return 'error';
      case '时间管理': return 'warning';
      case '去重处理': return 'info';
      case 'AI增强': return 'success';
      default: return 'default';
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        规则模板
      </Typography>
      <Typography variant="body2" color="textSecondary">
        选择预定义的规则模板快速创建常用规则
      </Typography>

      <Grid container spacing={2}>
        {ruleTemplates.map((template) => (
          <Grid item xs={12} sm={6} md={4} key={template.id}>
            <Card 
              sx={{ 
                height: '100%', 
                display: 'flex', 
                flexDirection: 'column',
                '&:hover': {
                  boxShadow: 3,
                  transform: 'translateY(-2px)',
                  transition: 'all 0.2s ease-in-out',
                }
              }}
            >
              <CardContent sx={{ flexGrow: 1 }}>
                <Box display="flex" alignItems="center" mb={1}>
                  {template.icon}
                  <Typography variant="h6" component="h3" ml={1}>
                    {template.name}
                  </Typography>
                </Box>
                <Chip 
                  label={template.category} 
                  size="small" 
                  color={getCategoryColor(template.category) as any}
                  sx={{ mb: 1 }}
                />
                <Typography variant="body2" color="textSecondary">
                  {template.description}
                </Typography>
              </CardContent>
              <CardActions>
                <Button 
                  size="small" 
                  onClick={() => handlePreview(template)}
                >
                  预览
                </Button>
                <Button 
                  size="small" 
                  variant="contained" 
                  onClick={() => onSelectTemplate(template)}
                >
                  使用模板
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* 模板预览对话框 */}
      <Dialog open={previewOpen} onClose={() => setPreviewOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          规则模板预览: {selectedTemplate?.name}
        </DialogTitle>
        <DialogContent>
          {selectedTemplate && (
            <Box>
              <Typography variant="body1" >
                <strong>描述:</strong> {selectedTemplate.description}
              </Typography>
              <Typography variant="body1" >
                <strong>类型:</strong> {selectedTemplate.ruleData.rule_type}
              </Typography>
              <Typography variant="body1" >
                <strong>动作:</strong> {selectedTemplate.ruleData.action}
              </Typography>
              <Typography variant="body1" >
                <strong>优先级:</strong> {selectedTemplate.ruleData.priority}
              </Typography>
              {selectedTemplate.ruleData.time_window_minutes && (
                <Typography variant="body1" >
                  <strong>时间窗口:</strong> {selectedTemplate.ruleData.time_window_minutes} 分钟
                </Typography>
              )}
              {selectedTemplate.ruleData.count_threshold && (
                <Typography variant="body1" >
                  <strong>计数阈值:</strong> {selectedTemplate.ruleData.count_threshold}
                </Typography>
              )}
              <Typography variant="body1">
                <strong>条件配置:</strong>
              </Typography>
              <Box component="pre" sx={{ 
                backgroundColor: '#f5f5f5', 
                padding: 2, 
                borderRadius: 1,
                fontSize: '0.875rem',
                overflow: 'auto'
              }}>
                {JSON.stringify(selectedTemplate.ruleData.conditions, null, 2)}
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewOpen(false)}>
            取消
          </Button>
          <Button onClick={handleUseTemplate} variant="contained">
            使用此模板
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RuleTemplates;
