import React, { useState } from 'react';
import { Grid, Paper, Typography, Box, Button } from '@material-ui/core';
import ReactEcharts from 'echarts-for-react';
import AttackMap from './attackMap';
interface Props {
  alertTrendOption: any;
  classes: any;
}

const DashboardCharts: React.FC<Props> = ({ alertTrendOption, classes }) => {
  // 新增：切换选中状态
  const [selected, setSelected] = useState<'原始告警' | '降噪后告警'>('原始告警');

  // 只显示选中的系列
  const filteredOption = {
    ...alertTrendOption,
    series: alertTrendOption.series.filter((s: any) => s.name === selected)
  };

  return (
    <Grid container spacing={2}>
      <Grid item xs={12} md={8}>
       <Paper className={classes.card} style={{ height: 340 }}>
  <Typography variant="subtitle1" gutterBottom>电力设施攻击热力图</Typography>
  <AttackMap />
</Paper>
      </Grid>
      <Grid item xs={12} md={4}>
        <Paper className={classes.card} style={{ height: 340 }}>
          {/* 标题和按钮 */}
          <Box className={classes.chartHeader}>
            <Typography variant="subtitle1" style={{ fontWeight: 700, color: '#2c3e50' }}>
              告警趋势分析
            </Typography>
            <Box>
             <Button
  className={classes.chartSwitchBtn}
  variant={selected === '原始告警' ? 'contained' : 'outlined'}
  style={
    selected === '原始告警'
      ? { background: '#219653', color: '#fff', border: 'none' }
      : { background: '#fff', color: '#219653', border: '1px solid #e0e0e0' }
  }
  onClick={() => setSelected('原始告警')}
>
  原始告警
</Button>
<Button
  className={classes.chartSwitchBtn}
  variant={selected === '降噪后告警' ? 'contained' : 'outlined'}
  style={
    selected === '降噪后告警'
      ? { background: '#219653', color: '#fff', border: 'none' }
      : { background: '#fff', color: '#219653', border: '1px solid #e0e0e0' }
  }
  onClick={() => setSelected('降噪后告警')}
>
  降噪后告警
</Button>

            </Box>
          </Box>
          {/* 图表 */}
          <ReactEcharts option={filteredOption} style={{ height: 270 }} />
        </Paper>
      </Grid>
    </Grid>
  );
};

export default DashboardCharts;
