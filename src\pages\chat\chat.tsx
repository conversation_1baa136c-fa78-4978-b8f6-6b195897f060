import React, { useState, useRef, useEffect } from 'react';
import { Fab, Paper, Button, Typography, makeStyles, Grid, Input, AppBar, Tabs, Tab, Box, InputAdornment, IconButton, TextField, Dialog, DialogActions, DialogContent, DialogTitle, MenuItem, Select } from '@material-ui/core';
import AddCircleOutlineIcon from '@material-ui/icons/AddCircleOutline';
import SearchIcon from '@material-ui/icons/Search';
import apiClient from '../apis/apiClient';
import Styles from './useStyles';
import ChatDelete from './delete';
import FileUpload from './fileUpload';
interface Session {
  session_id: string;
  session_name?: string;
  created_at?: string;
  updated_at?: string;
  last_message?: {
    created_at?: string;
    content?: string;
  };
}
interface TabPanelProps {
  children?: React.ReactNode;
  dir?: string;
  index: any;
  value: any;
}
interface Message {
  role: string
  content: string
}

function a11yProps(index: any) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}
function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div role="tabpanel" hidden={value !== index} id={`scrollable-auto-tabpanel-${index}`} aria-labelledby={`scrollable-auto-tab-${index}`} {...other}>
      {value === index && (
        <Box p={3}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

function getDateLabel(dateStr: string) {
  const now = new Date();
  const date = new Date(dateStr);
  const diff = (now.getTime() - date.getTime()) / 1000;
  if (diff < 60 * 60 * 24) return '今天';
  if (diff < 60 * 60 * 24 * 2) return '一天前';
  if (diff < 60 * 60 * 24 * 4) return '三天前';
  if (diff < 60 * 60 * 24 * 7) return '一周前';
  return '一个月前';
}
const useStyles=Styles

export default function Chat() {
  const classes = useStyles();
  const [hoveredId, setHoveredId] = useState<string | null>(null);
  const messageListRef = useRef<HTMLDivElement>(null)
  const [tabValue, setTabValue] = useState(0);
  const [value, setValue] = useState('');
  const [sendColor, setSendColor] = useState(false);
  const [messages, setMessages] = useState<Message[]>([])
  const [openAdd, setOpenAdd] = useState(false);
  const [{ x, y }, setPos] = useState({ x: 0, y: 0 });
  const dialogRef = useRef<HTMLDivElement | null>(null);
  const dragging = useRef(false);
  const offset = useRef({ dx: 0, dy: 0 });
  const hasCenteredOnce = useRef(false);
  // 会话历史
  const [sessions, setSessions] = useState<any[]>([]);
  const [loadingSessions, setLoadingSessions] = useState(false);
  const [search, setSearch] = useState('');
  const [sessionId, setSessionId] = useState('');
  const [sessionName, setSessionName]= useState('');
  // 新建会话弹窗
  const [openCreate, setOpenCreate] = useState(false);
  const [creating, setCreating] = useState(false);
  const [newSessionName, setNewSessionName] = useState('');
  const [newSessionType, setNewSessionType] = useState<'general'|'log_analysis'>('general');

  // 拉取历史会话
  const fetchSessions = () => {
    setLoadingSessions(true);
    apiClient.get('/api/chat/sessions')
      .then(res => {
        const data = res.data || res;
        setSessions(Array.isArray(data.sessions) ? data.sessions : []);
      })
      .catch(() => setSessions([]))
      .finally(() => setLoadingSessions(false));
  };

  useEffect(() => {
    if (!openAdd) return;
    fetchSessions();
  }, [openAdd]);

  // 拖拽逻辑
  const handleMouseDown: React.MouseEventHandler = e => {
    dragging.current = true;
    offset.current = { dx: e.clientX - x, dy: e.clientY - y };
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);
  };
  const handleMouseMove = (e: MouseEvent) => {
    if (!dragging.current) return;
    setPos({x: e.clientX - offset.current.dx,y: e.clientY - offset.current.dy});
  };
  const handleMouseUp = () => {
    dragging.current = false;
    window.removeEventListener('mousemove', handleMouseMove);
    window.removeEventListener('mouseup', handleMouseUp);
  };
  useEffect(() => {
    if (openAdd && dialogRef.current && !hasCenteredOnce.current) {
      const { clientWidth: w, clientHeight: h } = dialogRef.current;
      setPos({x: window.innerWidth / 2 - w / 2,y: window.innerHeight / 2 - h / 2});
      hasCenteredOnce.current = true;
    }
  }, [openAdd]);
  useEffect(() => {
    const list = messageListRef.current
    if (list) list.scrollTop = list.scrollHeight
  }, [messages])

  const handleSend =async () => {
    if (value) {
      setMessages(prev => [...prev, { role: 'user', content: value }])
      setValue('')
      try {
          const res =await apiClient.post('/api/chat/ai/message', {
          session_id: sessionId,
          message: value,
          message_type: 'text',
          use_context: true,
          files: []
      })
        setMessages(prev => [...prev, { role: 'assistant', content: res.data.response }])
      }
      catch (error) {
        console.log(error)
      } finally {
      }
      setSendColor(true)
      setTimeout(() => {
        setSendColor(false)
      }, 1000)
    }
  };

  // 会话分组(含搜索)
  const filteredSessions = sessions.filter(sess => {
    if (!search) return true;
    return (sess.session_name || '').includes(search) ||
      (sess.last_message?.content || '').includes(search);
  });
  const groupedSessions = filteredSessions.reduce((acc, session) => {
    const label = getDateLabel(session.updated_at || session.created_at);
    if (!acc[label]) acc[label] = [];
    acc[label].push(session);
    return acc;
  }, {} as Record<string, Session[]>);

  // 点击历史会话
  const handleSessionClick = async (session: any) => {
    setSessionId(session.session_id);
    // 你可以根据实际接口获取完整消息列表
    const res = await apiClient.get(`/api/chat/sessions/${session.session_id}/messages`);
    const data = res.data || res;
    setMessages(data.messages || []);
    setSessionName(session.session_name || '');
  };

  // 新建对话弹窗逻辑
  const handleCreateSession = async () => {
    if (!newSessionName.trim()) return;
    setCreating(true);
    try {
      console.log(newSessionName)
      console.log(newSessionType)
      const res = await apiClient.post('/api/chat/sessions', {
          session_name: newSessionName,
          session_type: newSessionType,
      });
      const data = res.data || res;
      if (data.success) {
        setOpenCreate(false);
        setNewSessionName('');
        setNewSessionType('general');
        fetchSessions();
        // 自动选中新建的会话
        setTimeout(() => {
          handleSessionClick(data);
        }, 300);
      }
    } finally {
      setCreating(false);
    }
  };


  
  return (
    <div>
      <div
  onClick={() => setOpenAdd(!openAdd)}
  style={{
    position: 'fixed',
    bottom: 400,
    right: 5,
    cursor: 'pointer',
    width: 90,
    height: 90,
    borderRadius: '50%',
    transition: 'transform 0.2s, box-shadow 0.2s',
  }}
  onMouseEnter={(e) => {
    e.currentTarget.style.transform = 'scale(1.05)';
    e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
  }}
  onMouseLeave={(e) => {
    e.currentTarget.style.transform = 'scale(1)';
    e.currentTarget.style.boxShadow = 'none';
  }}
>
  <img
    src="/icons/机器人.png"
    alt="机器人"
    style={{ width: '100%', height: '100%' }}
  />
</div>
      {/* 新建对话弹窗 */}
      <Dialog open={openCreate} onClose={() => setOpenCreate(false)}>
        <DialogTitle>新建对话</DialogTitle>
        <DialogContent>
          <TextField
            label="对话名称"
            value={newSessionName}
            onChange={e => setNewSessionName(e.target.value)}
            fullWidth
            margin="dense"
          />
          <Select
            value={newSessionType}
            onChange={e => setNewSessionType(e.target.value as any)}
            style={{ marginTop: 20, width: '100%' }}
          >
            <MenuItem value="general">普通对话</MenuItem>
            <MenuItem value="log_analysis">日志分析会话</MenuItem>
          </Select>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenCreate(false)} disabled={creating}>取消</Button>
          <Button onClick={handleCreateSession} color="primary" disabled={creating || !newSessionName.trim()}>
            {creating ? '创建中...' : '创建'}
          </Button>
        </DialogActions>
      </Dialog>
      {/*对话框*/}
      {openAdd && (
        <Paper className={classes.dialog} ref={dialogRef} style={{ left: x, top: y }} elevation={3}>
          <Grid container className={classes.container} wrap="nowrap">
            {/* 左侧区域 */}
            <Grid item className={classes.left}>
              <Paper square style={{ height: '650px', backgroundColor: '#EFEFEF' }}>
                <div style={{ margin: '0px auto', width: '85%' }}>
                  <div className={classes.titleBar} onMouseDown={handleMouseDown}>
                    <img src="/icons/机器人.png" alt="机器人" className={classes.icon} />
                    <Typography style={{ fontWeight: 'bold', fontSize: '18px', }}>光明大模型</Typography>
                  </div>
                  <Button
                    variant="contained"
                    color="primary"
                    style={{ display: 'flex', alignItems: 'center', margin: '10px auto', width: '100%', fontSize: '16px' }}
                    onClick={() => setOpenCreate(true)}
                  >
                    <AddCircleOutlineIcon style={{ width: '18px', height: '18px', marginRight: '5px' }} />
                    <span>新建对话</span>
                  </Button>
                  <Input
                    style={{
                      display: 'block', margin: '0px auto', border: '1px solid rgb(172, 172, 172)', borderRadius: 6, padding: '2px 12px', paddingRight: 40, position: 'relative'
                    }}
                    placeholder='搜索历史对话'
                    value={search}
                    onChange={e => setSearch(e.target.value)}
                    endAdornment={
                      <InputAdornment position="end" style={{ position: 'absolute', right: 10, top: '50%', transform: 'translateY(-50%)' }}>
                        <IconButton edge="end" style={{ padding: 8, pointerEvents: 'auto' }}>
                          <SearchIcon />
                        </IconButton>
                      </InputAdornment>
                    } />
                  <AppBar position="static" style={{ display: 'block', margin: '10px auto' }} elevation={0} >
                    <Tabs value={tabValue} style={{ width: '100%', backgroundColor: '#EFEFEF', color: '#333333' }} >
                      <Tab label="历史记录" style={{ minWidth: 0, padding: '0 8px' }} {...a11yProps(0)} />
                    </Tabs>
                  </AppBar>
                  {/* 历史记录 */}
                  <TabPanel value={tabValue} index={0} >
                    <div style={{ height: "420px", overflowY: 'auto', scrollbarWidth: 'thin', scrollbarColor: '#888 rgba(241, 241, 241, 0)', overflowX: 'hidden' }}>
                      {loadingSessions ? (
                        <div style={{ textAlign: 'center', color: '#999' }}>加载中...</div>
                      ) : (
                        Object.keys(groupedSessions).length === 0 ? (
                          <div style={{ textAlign: 'center', color: '#999', marginTop: 60 }}>暂无历史对话</div>
                        ) : (
                          Object.keys(groupedSessions).map(date => (
                            <div key={date} style={{ marginTop: 5 }}>
                              <div style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 5 }}>{date}</div>
                              {(groupedSessions[date] as Session[]).map((session, idx) => (
                                <Button
                                  key={session.session_id}
                                  style={{ width: '250px', justifyContent: "flex-start" }}
                                  component="div"
                                  onClick={() => handleSessionClick(session)}
                                  variant={sessionId === session.session_id ? 'contained' : 'undefined'}
                                  onMouseEnter={() => setHoveredId(session.session_id)}
                                  onMouseLeave={() => setHoveredId(null)}
                                >
                                  <div style={{ width: "100%" }}>
                                    <div style={{ fontWeight: 500 }}>{session.session_name || '未命名会话'}</div>
                                    <div style={{ fontSize: 14, color: '#929292',display: 'flex',justifyContent: 'space-between',alignItems: 'center',}}>
                                      <span style={{ marginLeft: '3px', display: 'inline-block',  width: 'calc(100% - 50px)', whiteSpace: 'nowrap', overflowX: 'hidden', textOverflow: 'ellipsis'}}>
                                        <span style={{ color: '#107F41', marginRight: '3px', fontSize: '18px' }}>●</span>
                                        <span style={{ marginRight: '5px' }}>
                                          {session.last_message?.created_at ? session.last_message.created_at.slice(11, 16) : ''}
                                        </span>
                                        <span>{session.last_message?.content ? session.last_message.content.replace(/\n/g, ' ').slice(0, 40) : '无消息'}</span>
                                      </span>
                                      {hoveredId===session.session_id && ( <ChatDelete session={session} fetchSessions={fetchSessions} setMessages={setMessages} setSessionId={setSessionId} />)}
                                    </div>
                                  </div>
                                </Button>
                              ))}
                            </div>
                          ))
                        )
                      )}
                    </div>
                  </TabPanel>
                </div>
              </Paper>
            </Grid>
            {/* 右侧区域 */}
            <Grid item className={classes.right}>
              <Paper square style={{ height: '650px', backgroundColor: '#E8E8E8' }}>
                {/* 关闭 */}
                <div style={{ display: 'flex', justifyContent: 'flex-end', cursor: 'pointer' }} onMouseDown={handleMouseDown}>
                  <img src="/icons/关闭.png" alt="关闭" style={{ width: '20px', height: '18px', margin: '5px', cursor: 'pointer' }} onClick={() => setOpenAdd(false)} />
                </div>
                {/* 聊天窗口 */}
                <div style={{ margin: '0px auto', width: '95%', height: '632px', display: "flex", flexDirection: "column" }}>
                  {!messages.length && (
                    <div>
                      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px', flexDirection: 'column' }}>
                        <img src="/icons/机器人.png" alt="机器人" style={{ width: '100px', height: '100px', margin: '0px auto' }} />
                        <span style={{ fontSize: '25px', fontWeight: 'bold' }}>嗨 , 我是</span>
                        <span style={{ fontSize: '25px', fontWeight: 'bold', background: 'linear-gradient(90deg, #107F41, #7ABB4C)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>光明大模型</span>
                        <span style={{ fontSize: '20px', marginTop: '0px', color: '#929292'}}>👋 欢迎使用SGCC蜜罐AI助手</span> 
                        <span style={{ fontSize: '20px', marginTop: '10px', color: '#929292'}}>点击"新建对话"开始与AI助手交流</span>
                      </div>
                    </div>)}
                  {messages.length > 0 && (<>
                    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '30px', fontWeight: 'bold', fontSize: '18px', color: '#333333' }}>{sessionName}</div>
                    <div ref={messageListRef} className={classes.messageList}>
                      {messages.map((msg, i) => (
                        <div key={i} className={`${msg.role === 'user' ? classes.user : classes.ai}`}>
                          <div className={`${msg.role === 'user' ? classes.messageUserText : classes.messageAiText}`}>{msg.content}</div>
                        </div>
                      ))}
                      {messages[messages.length - 1].role == 'user' && (
                            <div className={classes.ai}>
                              <div className={classes.messageAiText}>思考中…</div>
                            </div>
                          )}
                    </div></>)}
                  {/* 输入框 */}
                  {sessionId && <>
                  <div style={{display: 'flex', alignItems: 'center', height: '50px', marginTop: '10px'}}>
                  <FileUpload/>
                  </div>
                  <TextField id="outlined-basic" variant="outlined" style={{ marginTop: "auto", marginBottom: "20px",}} rows={3} multiline fullWidth value={value} onChange={(e) => { setValue(e.target.value) }}
                    InputProps={{ style: { paddingRight: '45px', paddingBottom: '12px' } }} />
                  <div style={{ backgroundColor: value ? "#4CAF50" : "rgb(140, 224, 98)", position: 'absolute', bottom: '20px', right: '25px', width: '30px', height: '30px', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center', borderRadius: '4px' }}>
                    <img src="/icons/发送.png" style={{ width: '20px', height: '20px', cursor: 'pointer' }} onClick={handleSend} />
                  </div></>}
                </div>
              </Paper>
            </Grid>
          </Grid>
        </Paper>
      )}
    </div>
  );
}
