import React, { useState, useEffect } from "react";
import { Container, Typography, Paper, makeStyles } from "@material-ui/core";
import NetworkGraph from "./NetworkGraph";

const useStyles = makeStyles((theme) => ({
  root: {
    flexGrow: 1,
    padding: theme.spacing(3),
    width: "100%",
  },
  title: {
    marginBottom: theme.spacing(2),
  },
  statsContainer: {
    marginBottom: theme.spacing(2),
    padding: theme.spacing(1),
  },
}));

interface HoneypotData {
  id: number;
  name: string;
  type: string;
  location: string;
  status: string;
  visitCount: number;
}

const Dashboard: React.FC = () => {
  const classes = useStyles();
  const [honeypots, setHoneypots] = useState<HoneypotData[]>([]);

  // 模拟数据 - 实际项目中应该从API获取
  useEffect(() => {
    const mockHoneypots = [
      {
        id: 1,
        name: "管理系统",
        type: "admin",
        location: "总机房",
        status: "active",
        visitCount: 0,
      },
      {
        id: 2,
        name: "ssh-1",
        type: "SSH",
        location: "管理业务系统2",
        status: "active",
        visitCount: 280,
      },
      {
        id: 3,
        name: "ftp-3",
        type: "FTP",
        location: "分类分级业务系统",
        status: "inactive",
        visitCount: 130,
      },
      {
        id: 4,
        name: "pop-0",
        type: "邮件",
        location: "内部邮件系统",
        status: "inactive",
        visitCount: 20,
      },
    ];
    setHoneypots(mockHoneypots);
  }, []);

  // 转换蜜罐数据为网络图所需的格式
  const getNodeColor = (visitCount: number) => {
    // 将访问次数映射到颜色深度（从浅橙色到红色）
    const maxCount = Math.max(...honeypots.map((pot) => pot.visitCount));
    const intensity = Math.min(visitCount / maxCount, 1);
    if (visitCount === 0) return "#87CEFA";
    return `rgb(255, ${Math.round(165 - intensity * 165)}, 0)`;
  };

  const nodes = honeypots.map((pot) => ({
    id: pot.id,
    label: pot.name,
    title: `类型: ${pot.type}<br>位置: ${pot.location}<br>状态: ${pot.status}<br>访问次数: ${pot.visitCount}`,
    group: pot.type,
    color: getNodeColor(pot.visitCount),
  }));

  // 模拟蜜罐之间的连接关系
  const edges = [
    { from: 3, to: 1 },
    { from: 2, to: 1 },
    { from: 4, to: 1 },
  ];

  return (
    <div className={classes.root}>
      <Typography variant="h4" className={classes.title}>
        蜜罐网络监控
      </Typography>

      <Paper className={classes.statsContainer}>
        <Typography variant="h6" gutterBottom>
          系统概览
        </Typography>
        <Typography>
          活跃蜜罐数量:{" "}
          {honeypots.filter((pot) => pot.status === "active").length}
        </Typography>
      </Paper>

      <NetworkGraph nodes={nodes} edges={edges} />
    </div>
  );
};

export default Dashboard;
