// src/pages/TaskDetail.tsx
import React, { useEffect, useRef, useState } from 'react';
import {
  Box,
  Button,
  Grid,
  Paper,
  Typography,
  LinearProgress,
} from '@material-ui/core';
import { Snackbar, Alert} from '@mui/material';
import styles from './AlarmDetails.module.less';
import apiClient from '../../apis/apiClient';
import { useParams } from 'react-router-dom';
import { AxiosError } from 'axios';
import { useNavigate } from 'react-router-dom';

const POLL_INTERVAL = 10000;

const AlarmDetail: React.FC = () => {

  const { taskId } = useParams<{ taskId: string }>();
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [openSuccess, setOpenSuccess] = useState(false);
  const [openError, setOpenError] = useState(false);
  const [taskStatus, setTaskStatus] = useState();
  const [taskStartTime, setTaskStartTime] = useState();
  const [taskEndTime, setTaskEndTime] = useState();
  const [taskProcess, setTaskProcess] = useState();
  const [taskTotal, setTaskTotal] = useState();
  const [taskProcessed, setTaskProcessed] = useState();
  const [taskSuppressed, setTaskSuppressed] = useState();
  const [taskMerged, setTaskMerged] = useState();
  const [taskClusters, setTaskClusters] = useState();
  const [taskNoise, setTaskNoise] = useState();

  const intervalRef = useRef<number | null>(null);
const navigate = useNavigate();

  // 开始任务
  const startPolling = () => {
    if (intervalRef.current) return; // 如果已经在轮询中，直接返回
    timedTask(); // 立即执行一次
    intervalRef.current = setInterval(timedTask, POLL_INTERVAL); 
  };

  // 停止任务
  const stopPolling = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current); // 清除定时器
      intervalRef.current = null;
    }
  };

  // 定时任务
  const timedTask = async () => {
    try {
      const res = await apiClient.get(`/api/noise-reduction/tasks/${taskId}/status`);
        setTaskStatus(res.data.status)
        setTaskProcess(res.data.progress)
        setTaskStartTime(res.data.start_time)
        setTaskEndTime(res.data.end_time)
        setTaskTotal(res.data.statistics.total_alerts)
        setTaskProcessed(res.data.statistics.processed_alerts)
        setTaskSuppressed(res.data.statistics.suppressed_alerts)
        setTaskMerged(res.data.statistics.merged_alerts)
        setTaskClusters(res.data.statistics.clusters_count)
        setTaskNoise(res.data.statistics.noise_reduction_ratio)

        if(res.data.status !='running') {
          stopPolling();
        }
    } catch (error) {
        console.error('轮询任务状态失败', error);
    }
  };

  // 文件大小格式化
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    const size = bytes / Math.pow(k, i);
    return `${size.toFixed(2)} ${sizes[i]}`;
  };
  
  // 启动任务
  const handleExecute = async () => {
    try {
      const response = await apiClient.post(`/api/noise-reduction/tasks/${taskId}/execute`, {
      });
      startPolling();
      setSuccessMessage(response.data.message)
      setOpenSuccess(true);
    } catch (error) {
      const err = error as AxiosError<{ error: string }>;
      const backendMessage = err?.response?.data?.error || '任务启动失败，请稍后重试';
      setErrorMessage(backendMessage);
      setOpenError(true);
    }
  };
  // 停止任务
  const handleStop = async () => {
        try {
      const response = await apiClient.post(`/api/noise-reduction/tasks/${taskId}/stop`)
      const res = await apiClient.get(`/api/noise-reduction/tasks/${taskId}/status`)
        setTaskStatus(res.data.status)
        setTaskProcess(res.data.progress)
        setTaskStartTime(res.data.start_time)
        setTaskEndTime(res.data.end_time)
        setTaskTotal(res.data.statistics.total_alerts)
        setTaskProcessed(res.data.statistics.processed_alerts)
        setTaskSuppressed(res.data.statistics.suppressed_alerts)
        setTaskMerged(res.data.statistics.merged_alerts)
        setTaskClusters(res.data.statistics.clusters_count)
        setTaskNoise(res.data.statistics.noise_reduction_ratio)
        setSuccessMessage(response.data.message)
        setOpenSuccess(true);
    } catch (error) {
      const err = error as AxiosError<{ error: string }>;
      const backendMessage = err?.response?.data?.error || '任务停止失败，请稍后重试';
      setErrorMessage(backendMessage);
      setOpenError(true);
    }
  }
  // 重启任务
  const handleRestart = async () => {
        try {
      const response = await apiClient.post(`/api/noise-reduction/tasks/${taskId}/restart`)
      const res = await apiClient.post(`/api/noise-reduction/tasks/${taskId}/execute`, {
      });
      startPolling();
      setSuccessMessage('任务重启成功')
      setOpenSuccess(true);
    } catch (error) {
      const err = error as AxiosError<{ error: string }>;
      const backendMessage = err?.response?.data?.error || '任务重启失败，请稍后重试';
      setErrorMessage(backendMessage);
      setOpenError(true);
    }
  }

  useEffect(() => {
    if (!taskId) return;

    const numericTaskId = parseInt(taskId, 10);
    if (isNaN(numericTaskId)) return;

    const fetchTaskDetail = async () => {
      try {
        const response = await apiClient.get(`/api/noise-reduction/tasks/${numericTaskId}`);
        setData(response.data);
        setTaskStatus(response.data.status)
        setTaskProcess(response.data.progress)
        setTaskStartTime(response.data.start_time)
        setTaskEndTime(response.data.end_time)
        setTaskTotal(response.data.total_alerts)
        setTaskProcessed(response.data.processed_alerts)
        setTaskSuppressed(response.data.suppressed_alerts)
        setTaskMerged(response.data.merged_alerts)
        setTaskClusters(response.data.clusters_count)
        setTaskNoise(response.data.noise_reduction_ratio)
        if (response.data.status === 'running'){
          startPolling()
        }
      } catch (err: any) {
        setError(err.message || '请求失败');
      } finally {
        setLoading(false);
      }
    };

    fetchTaskDetail();

  }, [taskId]);

  // 页面卸载清除定时器
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, []);

  console.log(data)

  return (
    <>
    {loading?<div>加载中</div>:(
      <Box className={styles.pageWrapper}>
      {/* 任务概览 */}
<Paper className={styles.section}>
  <Box className={styles.titleRow}>
    <Typography variant="h6" className={styles.title}>
      告警详情
    </Typography>
    <Button
      variant="outlined"
      color="primary"
      onClick={() => navigate('/noiseAnalysisTask')}
    >
      返回
    </Button>
  </Box>



        <Grid container spacing={3} style={{ marginTop: 0}}>
          <Grid item xs={3}>
            <Typography className={styles.label}>任务名称</Typography>
            <Typography className={styles.value}> {data.task_name}</Typography>
          </Grid>
          <Grid item xs={3}>
            <Typography className={styles.label}>任务类型</Typography>
            <Typography className={styles.value}>{data.task_type}</Typography>
          </Grid>
          <Grid item xs={3}>
            <Typography className={styles.label}>任务状态</Typography>
            <Typography className={styles.statusRunning}>{taskStatus}</Typography>
          </Grid>
          <Grid item xs={3}>
            <Typography className={styles.label}>进度</Typography>
            <Box display="flex" alignItems="center">
              <Box width="100%" mr={1}>
                <LinearProgress
                  variant="determinate"
                  value={taskProcess ?? 0}
                />
              </Box>
              <Box minWidth={35}>
                <Typography variant="body2" color="textSecondary">
                  {taskProcess != null ? `${taskProcess}%` : '加载中...'}
                </Typography>
              </Box>
            </Box>
          </Grid>
          <Grid item xs={3}>
            <Typography className={styles.label}>创建时间</Typography>
            <Typography className={styles.value}>
              {(() => {
                try {
                  const date = new Date(data.created_at);
                  if (!isNaN(date.getTime())) {
                    return date.toLocaleString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit',
                      hour12: false
                    });
                  }
                  return data.created_at;
                } catch (error) {
                  return data.created_at;
                }
              })()}
            </Typography>
          </Grid>
          <Grid item xs={3}>
            <Typography className={styles.label}>开始时间</Typography>
            <Typography className={styles.value}>
              {taskStartTime ? (() => {
                try {
                  const date = new Date(taskStartTime);
                  if (!isNaN(date.getTime())) {
                    return date.toLocaleString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit',
                      hour12: false
                    });
                  }
                  return taskStartTime;
                } catch (error) {
                  return taskStartTime;
                }
              })() : '-'}
            </Typography>
          </Grid>
          <Grid item xs={3}>
            <Typography className={styles.label}>结束时间</Typography>
            <Typography className={styles.value}>
              {taskEndTime ? (() => {
                try {
                  const date = new Date(taskEndTime);
                  if (!isNaN(date.getTime())) {
                    return date.toLocaleString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit',
                      hour12: false
                    });
                  }
                  return taskEndTime;
                } catch (error) {
                  return taskEndTime;
                }
              })() : '-'}
            </Typography>
          </Grid>
          <Grid item xs={3}>
            <Typography className={styles.label}>使用配置</Typography>
            <Typography className={styles.value}>{data.config.config_name}</Typography>
          </Grid>
        </Grid>
      </Paper>

      {/* 文件信息 */}
      <Paper className={styles.section}>
        <Typography variant="h6" className={styles.title}>
          文件信息
        </Typography>
        <Grid container spacing={3} style={{ marginTop: 0}}>
          <Grid item xs={2}>
            <Typography className={styles.label}>文件名称</Typography>
            <Typography className={styles.value}>{data.file_info.original_filename}</Typography>
          </Grid>
          <Grid item xs={2}>
            <Typography className={styles.label}>文件大小</Typography>
            <Typography className={styles.value}>{formatFileSize(data.file_info.file_size)}</Typography>
          </Grid>
          <Grid item xs={2}>
            <Typography className={styles.label}>文件类型</Typography>
            <Typography className={styles.value}>{data.file_info.file_type}</Typography>
          </Grid>
          <Grid item xs={3}>
            <Typography className={styles.label}>上传时间</Typography>
            <Typography className={styles.value}>
              {(() => {
                try {
                  const date = new Date(data.file_info.upload_time);
                  if (!isNaN(date.getTime())) {
                    return date.toLocaleString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit',
                      hour12: false
                    });
                  }
                  return data.file_info.upload_time;
                } catch (error) {
                  return data.file_info.upload_time;
                }
              })()}
            </Typography>
          </Grid>
          <Grid item xs={3}>
            <Typography className={styles.label}>最后修改</Typography>
            <Typography className={styles.value}>
              {data.file_info.last_modified_time?data.file_info.last_modified_time:'-'}
            </Typography>
          </Grid>
        </Grid>

        {/* 日志内容 */}
        <Box className={styles.logBox}>
          {data.file_info.sample_lines?data.file_info.sample_lines:'-'}
        </Box>
      </Paper>

      {/* 实时统计 */}
      <Paper className={styles.section}>
        <Typography variant="h6" className={styles.title}>
          实时统计
        </Typography>
        <Grid container spacing={2} style={{ marginTop: 4}}>
          {[
            { label: '总告警数', value: taskTotal },
            { label: '已处理告警', value: taskProcessed },
            { label: '抑制告警', value: taskSuppressed },
            { label: '合并告警', value: taskMerged },
            { label: '聚类数量', value: taskClusters },
            { 
              label: '降噪比例', 
              value: taskNoise != null 
                ? `${(taskNoise * 100).toFixed(2)}%` 
                : 'N/A' 
            }
          ].map((item, index) => (
            <Grid item xs={2} key={index}>
              <Box className={styles.statCard}>
                <Typography variant="h6">{item.value}</Typography>
                <Typography variant="body2" className={styles.label}>
                  {item.label}
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>
      </Paper>

      {/* 操作按钮 */}
      <Paper className={styles.section}>
        <Typography variant="h6" className={styles.title}>
          操作
        </Typography>
        <Box className={styles.actionButtons}>
          <Button variant="contained" color="primary" onClick={handleExecute}>重启任务</Button>
          <Button variant="outlined" color="secondary" onClick={handleStop}>停止任务</Button>
          {/* <Button variant="outlined" onClick={handleRestart}>重启任务</Button> */}
        </Box>
      <Snackbar
        open={openError}
        autoHideDuration={5000}
        onClose={() => setOpenError(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setOpenError(false)}
          severity="error"
          sx={{ width: '100%' }}
        >
          {errorMessage}
        </Alert>
      </Snackbar>

      <Snackbar
        open={openSuccess}
        autoHideDuration={5000}
        onClose={() => setOpenSuccess(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setOpenSuccess(false)}
          severity="success"
          sx={{ width: '100%' }}
        >
          {successMessage}
        </Alert>
      </Snackbar>
      </Paper>
    </Box>)}
    </>
  );
};

export default AlarmDetail;
