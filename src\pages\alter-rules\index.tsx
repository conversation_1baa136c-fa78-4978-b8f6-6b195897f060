import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Tabs,
  Tab,
  Card,
  CardContent,
  Tooltip,
  Snackbar,
  Alert,
} from '@mui/material';
import { Grid } from '@material-ui/core';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PlayArrow as TestIcon,
  Visibility as ViewIcon,
  FilterList as FilterIcon,
  GetApp as ExportIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  Security as SecurityIcon,
  Rule as RuleIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';
import { makeStyles } from '@material-ui/core/styles';
import apiClient from '../apis/apiClient';
import './index.css';

const useStyles = makeStyles((theme: any) => ({
  root: {
    padding: theme?.spacing?.(3) || 24,
    backgroundColor: '#f5f5f5',
    minHeight: '100vh',
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme?.spacing?.(3) || 24,
    padding: theme?.spacing?.(2) || 16,
    backgroundColor: '#fff',
    borderRadius: 8,
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
  },
  title: {
    display: 'flex',
    alignItems: 'center',
    gap: theme?.spacing?.(1) || 8,
    fontWeight: 'bold',
    color: '#1976d2',
  },
  actionButtons: {
    display: 'flex',
    gap: theme?.spacing?.(1) || 8,
  },
  statsCards: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
    gap: theme?.spacing?.(2) || 16,
    marginBottom: theme?.spacing?.(3) || 24,
  },
  statCard: {
    padding: theme?.spacing?.(2) || 16,
    textAlign: 'center',
    borderRadius: 8,
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
  },
  tableContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
  },
  tableHeader: {
    backgroundColor: '#f8f9fa',
    '& .MuiTableCell-head': {
      fontWeight: 'bold',
      color: '#333',
    },
  },
  statusChip: {
    minWidth: 80,
  },
  priorityChip: {
    minWidth: 60,
  },
  actionCell: {
    display: 'flex',
    gap: theme?.spacing?.(0.5) || 4,
  },
  filterBar: {
    display: 'flex',
    gap: theme?.spacing?.(2) || 16,
    marginBottom: theme?.spacing?.(2) || 16,
    padding: theme?.spacing?.(2) || 16,
    backgroundColor: '#fff',
    borderRadius: 8,
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
  },
  dialogContent: {
    minWidth: 600,
    maxWidth: 800,
  },
  formGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
    gap: theme?.spacing?.(2) || 16,
    marginBottom: theme?.spacing?.(2) || 16,
  },
  conditionBuilder: {
    border: '1px solid #e0e0e0',
    borderRadius: 8,
    padding: theme?.spacing?.(2) || 16,
    backgroundColor: '#fafafa',
  },
  tabPanel: {
    padding: theme?.spacing?.(2) || 16,
  },
}));

// 规则类型枚举
const RULE_TYPES = {
  pattern: '模式匹配',
  threshold: '阈值检测',
  time_based: '时间基础',
  ml_based: '机器学习',
};

// 执行动作枚举
const ACTIONS = {
  suppress: '抑制',
  adjust_priority: '调整优先级',
  merge: '合并',
};

// 严重程度枚举
const SEVERITIES = {
  CRITICAL: '严重',
  HIGH: '高',
  MEDIUM: '中',
  LOW: '低',
  INFO: '信息',
};

interface SuppressionRule {
  id?: number;
  rule_name: string;
  rule_type: string;
  description?: string;
  conditions: any;
  action: string;
  severity_filter?: string[];
  source_filter?: string[];
  type_filter?: string[];
  time_window_minutes?: number;
  count_threshold?: number;
  is_active: boolean;
  priority: number;
  effectiveness_score?: number;
  false_positive_rate?: number;
  usage_count?: number;
  match_count?: number;
  suppressed_count?: number;
  created_at?: string;
  updated_at?: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = ({ children, value, index }: TabPanelProps) => (
  <div role="tabpanel" hidden={value !== index}>
    {value === index && <Box>{children}</Box>}
  </div>
);

const AlertRulesManagement: React.FC = () => {
  const classes = useStyles();
  const [rules, setRules] = useState<SuppressionRule[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [selectedRule, setSelectedRule] = useState<SuppressionRule | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState<'create' | 'edit' | 'view'>('create');
  const [tabValue, setTabValue] = useState(0);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  // 筛选状态
  const [filters, setFilters] = useState({
    rule_type: '',
    action: '',
    is_active: '',
    search: '',
  });

  // 统计数据
  const [stats, setStats] = useState({
    total_rules: 0,
    active_rules: 0,
    inactive_rules: 0,
    average_effectiveness: 0,
  });

  useEffect(() => {
    loadRules();
    loadStats();
  }, [page, rowsPerPage, filters]);

  const loadRules = async () => {
    try {
      setLoading(true);
      const params = {
        page: page + 1,
        per_page: rowsPerPage,
        ...filters,
      };

      const response = await apiClient.get('/api/noise-reduction/rules/', { params });
      setRules(response.data.rules || []);
      setTotalCount(response.data.total || 0);
    } catch (error) {
      console.error('加载规则失败:', error);
      showSnackbar('加载规则失败', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const response = await apiClient.get('/api/noise-reduction/rules/statistics');
      setStats(response.data.statistics || {});
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  };

  const showSnackbar = (message: string, severity: 'success' | 'error') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const handleCreateRule = () => {
    setSelectedRule(null);
    setDialogMode('create');
    setDialogOpen(true);
    setTabValue(0);
  };

  const handleEditRule = (rule: SuppressionRule) => {
    setSelectedRule(rule);
    setDialogMode('edit');
    setDialogOpen(true);
    setTabValue(0);
  };

  const handleViewRule = (rule: SuppressionRule) => {
    setSelectedRule(rule);
    setDialogMode('view');
    setDialogOpen(true);
    setTabValue(0);
  };

  const handleDeleteRule = async (ruleId: number) => {
    if (!window.confirm('确定要删除这个规则吗？')) {
      return;
    }

    try {
      await apiClient.delete(`/api/noise-reduction/rules/${ruleId}`);
      showSnackbar('规则删除成功', 'success');
      loadRules();
      loadStats();
    } catch (error) {
      console.error('删除规则失败:', error);
      showSnackbar('删除规则失败', 'error');
    }
  };

  const handleTestRule = async (rule: SuppressionRule) => {
    try {
      const response = await apiClient.post('/api/noise-reduction/rules/test', {
        rule_id: rule.id,
        test_data: {
          // 这里可以添加测试数据
        },
      });
      showSnackbar('规则测试完成', 'success');
      // 可以显示测试结果
    } catch (error) {
      console.error('测试规则失败:', error);
      showSnackbar('测试规则失败', 'error');
    }
  };

  const handleToggleActive = async (rule: SuppressionRule) => {
    try {
      await apiClient.put(`/api/noise-reduction/rules/${rule.id}`, {
        ...rule,
        is_active: !rule.is_active,
      });
      showSnackbar(`规则已${!rule.is_active ? '启用' : '禁用'}`, 'success');
      loadRules();
      loadStats();
    } catch (error) {
      console.error('更新规则状态失败:', error);
      showSnackbar('更新规则状态失败', 'error');
    }
  };

  const handleFilterChange = (field: string, value: any) => {
    setFilters({ ...filters, [field]: value });
    setPage(0);
  };

  const handleExportRules = async () => {
    try {
      const response = await apiClient.get('/api/noise-reduction/rules/export', {
        responseType: 'blob',
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `suppression_rules_${new Date().toISOString().split('T')[0]}.json`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      showSnackbar('规则导出成功', 'success');
    } catch (error) {
      console.error('导出规则失败:', error);
      showSnackbar('导出规则失败', 'error');
    }
  };

  return (
    <Box className={classes.root}>
      {/* 页面头部 */}
      <Paper className={classes.header}>
        <Typography variant="h4" className={classes.title}>
          <RuleIcon />
          告警规则管理
        </Typography>
        <Box className={classes.actionButtons}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleCreateRule}
          >
            新建规则
          </Button>
          <Button
            variant="outlined"
            startIcon={<ExportIcon />}
            onClick={handleExportRules}
          >
            导出规则
          </Button>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadRules}
          >
            刷新
          </Button>
        </Box>
      </Paper>

      {/* 统计卡片 */}
      <Box className={classes.statsCards}>
        <Card className={classes.statCard} style={{ backgroundColor: '#e3f2fd' }}>
          <CardContent>
            <Typography variant="h6" color="primary">
              <SecurityIcon style={{ verticalAlign: 'middle', marginRight: 8 }} />
              {stats.total_rules}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              总规则数
            </Typography>
          </CardContent>
        </Card>
        <Card className={classes.statCard} style={{ backgroundColor: '#e8f5e8' }}>
          <CardContent>
            <Typography variant="h6" style={{ color: '#4caf50' }}>
              <RuleIcon style={{ verticalAlign: 'middle', marginRight: 8 }} />
              {stats.active_rules}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              活跃规则
            </Typography>
          </CardContent>
        </Card>
        <Card className={classes.statCard} style={{ backgroundColor: '#fff3e0' }}>
          <CardContent>
            <Typography variant="h6" style={{ color: '#ff9800' }}>
              <TimelineIcon style={{ verticalAlign: 'middle', marginRight: 8 }} />
              {stats.inactive_rules}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              禁用规则
            </Typography>
          </CardContent>
        </Card>
        <Card className={classes.statCard} style={{ backgroundColor: '#f3e5f5' }}>
          <CardContent>
            <Typography variant="h6" style={{ color: '#9c27b0' }}>
              <AssessmentIcon style={{ verticalAlign: 'middle', marginRight: 8 }} />
              {(stats.average_effectiveness * 100).toFixed(1)}%
            </Typography>
            <Typography variant="body2" color="textSecondary">
              平均有效性
            </Typography>
          </CardContent>
        </Card>
      </Box>

      {/* 筛选栏 */}
      <Paper className={classes.filterBar}>
        <TextField
          label="搜索规则"
          variant="outlined"
          size="small"
          value={filters.search}
          onChange={(e) => handleFilterChange('search', e.target.value)}
          style={{ minWidth: 200 }}
        />
        <FormControl variant="outlined" size="small" style={{ minWidth: 150 }}>
          <InputLabel>规则类型</InputLabel>
          <Select
            value={filters.rule_type}
            onChange={(e) => handleFilterChange('rule_type', e.target.value)}
            label="规则类型"
          >
            <MenuItem value="">全部</MenuItem>
            {Object.entries(RULE_TYPES).map(([key, label]) => (
              <MenuItem key={key} value={key}>{label}</MenuItem>
            ))}
          </Select>
        </FormControl>
        <FormControl variant="outlined" size="small" style={{ minWidth: 150 }}>
          <InputLabel>执行动作</InputLabel>
          <Select
            value={filters.action}
            onChange={(e) => handleFilterChange('action', e.target.value)}
            label="执行动作"
          >
            <MenuItem value="">全部</MenuItem>
            {Object.entries(ACTIONS).map(([key, label]) => (
              <MenuItem key={key} value={key}>{label}</MenuItem>
            ))}
          </Select>
        </FormControl>
        <FormControl variant="outlined" size="small" style={{ minWidth: 120 }}>
          <InputLabel>状态</InputLabel>
          <Select
            value={filters.is_active}
            onChange={(e) => handleFilterChange('is_active', e.target.value)}
            label="状态"
          >
            <MenuItem value="">全部</MenuItem>
            <MenuItem value="true">启用</MenuItem>
            <MenuItem value="false">禁用</MenuItem>
          </Select>
        </FormControl>
      </Paper>

      {/* 规则表格 */}
      <TableContainer component={Paper} className={classes.tableContainer}>
        <Table>
          <TableHead className={classes.tableHeader}>
            <TableRow>
              <TableCell>规则名称</TableCell>
              <TableCell>类型</TableCell>
              <TableCell>动作</TableCell>
              <TableCell>状态</TableCell>
              <TableCell>优先级</TableCell>
              <TableCell>有效性</TableCell>
              <TableCell>使用次数</TableCell>
              <TableCell>创建时间</TableCell>
              <TableCell>操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {rules.map((rule) => (
              <TableRow key={rule.id} hover>
                <TableCell>
                  <Typography variant="body2" fontWeight="bold">
                    {rule.rule_name}
                  </Typography>
                  {rule.description && (
                    <Typography variant="caption" color="textSecondary">
                      {rule.description.length > 50
                        ? `${rule.description.substring(0, 50)}...`
                        : rule.description}
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Chip
                    label={RULE_TYPES[rule.rule_type as keyof typeof RULE_TYPES] || rule.rule_type}
                    size="small"
                    color="default"
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={ACTIONS[rule.action as keyof typeof ACTIONS] || rule.action}
                    size="small"
                    color={rule.action === 'suppress' ? 'error' :
                           rule.action === 'adjust_priority' ? 'warning' : 'info'}
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={rule.is_active ? '启用' : '禁用'}
                    size="small"
                    color={rule.is_active ? 'success' : 'default'}
                    className={classes.statusChip}
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={rule.priority}
                    size="small"
                    color={rule.priority <= 3 ? 'error' :
                           rule.priority <= 6 ? 'warning' : 'default'}
                    className={classes.priorityChip}
                  />
                </TableCell>
                <TableCell>
                  {rule.effectiveness_score !== undefined ? (
                    <Typography variant="body2">
                      {(rule.effectiveness_score * 100).toFixed(1)}%
                    </Typography>
                  ) : (
                    <Typography variant="body2" color="textSecondary">
                      -
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {rule.usage_count || 0}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {rule.created_at ? new Date(rule.created_at).toLocaleDateString() : '-'}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box className={classes.actionCell}>
                    <Tooltip title="查看详情">
                      <IconButton size="small" onClick={() => handleViewRule(rule)}>
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="编辑规则">
                      <IconButton size="small" onClick={() => handleEditRule(rule)}>
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="测试规则">
                      <IconButton size="small" onClick={() => handleTestRule(rule)}>
                        <TestIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title={rule.is_active ? '禁用规则' : '启用规则'}>
                      <Switch
                        checked={rule.is_active}
                        onChange={() => handleToggleActive(rule)}
                        size="small"
                      />
                    </Tooltip>
                    <Tooltip title="删除规则">
                      <IconButton
                        size="small"
                        onClick={() => handleDeleteRule(rule.id!)}
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <TablePagination
          component="div"
          count={totalCount}
          page={page}
          onPageChange={(_, newPage) => setPage(newPage)}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={(e) => {
            setRowsPerPage(parseInt(e.target.value, 10));
            setPage(0);
          }}
          rowsPerPageOptions={[5, 10, 25, 50]}
          labelRowsPerPage="每页行数:"
          labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count} 条`}
        />
      </TableContainer>

      {/* 规则创建/编辑对话框 */}
      <RuleDialog
        open={dialogOpen}
        mode={dialogMode}
        rule={selectedRule}
        onClose={() => setDialogOpen(false)}
        onSave={() => {
          setDialogOpen(false);
          loadRules();
          loadStats();
        }}
        showSnackbar={showSnackbar}
      />

      {/* 消息提示 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

// 规则对话框组件
interface RuleDialogProps {
  open: boolean;
  mode: 'create' | 'edit' | 'view';
  rule: SuppressionRule | null;
  onClose: () => void;
  onSave: () => void;
  showSnackbar: (message: string, severity: 'success' | 'error') => void;
}

const RuleDialog: React.FC<RuleDialogProps> = ({
  open,
  mode,
  rule,
  onClose,
  onSave,
  showSnackbar,
}) => {
  const classes = useStyles();
  const [tabValue, setTabValue] = useState(0);
  const [formData, setFormData] = useState<Partial<SuppressionRule>>({
    rule_name: '',
    rule_type: 'pattern',
    description: '',
    conditions: {},
    action: 'suppress',
    severity_filter: [],
    source_filter: [],
    type_filter: [],
    time_window_minutes: 60,
    count_threshold: 5,
    is_active: true,
    priority: 5,
  });

  const [conditionFields, setConditionFields] = useState([
    { field: 'title', operator: 'contains', value: '' }
  ]);

  useEffect(() => {
    if (rule && (mode === 'edit' || mode === 'view')) {
      setFormData(rule);
      // 解析条件字段
      if (rule.conditions && typeof rule.conditions === 'object') {
        const fields = Object.entries(rule.conditions).map(([field, condition]: [string, any]) => ({
          field,
          operator: condition.operator || 'contains',
          value: condition.value || '',
        }));
        setConditionFields(fields.length > 0 ? fields : [{ field: 'title', operator: 'contains', value: '' }]);
      }
    } else {
      // 重置表单
      setFormData({
        rule_name: '',
        rule_type: 'pattern',
        description: '',
        conditions: {},
        action: 'suppress',
        severity_filter: [],
        source_filter: [],
        type_filter: [],
        time_window_minutes: 60,
        count_threshold: 5,
        is_active: true,
        priority: 5,
      });
      setConditionFields([{ field: 'title', operator: 'contains', value: '' }]);
    }
    setTabValue(0);
  }, [rule, mode, open]);

  const handleInputChange = (field: string, value: any) => {
    setFormData({ ...formData, [field]: value });
  };

  const handleConditionChange = (index: number, field: string, value: any) => {
    const newConditions = [...conditionFields];
    newConditions[index] = { ...newConditions[index], [field]: value };
    setConditionFields(newConditions);
  };

  const addCondition = () => {
    setConditionFields([...conditionFields, { field: 'title', operator: 'contains', value: '' }]);
  };

  const removeCondition = (index: number) => {
    if (conditionFields.length > 1) {
      setConditionFields(conditionFields.filter((_, i) => i !== index));
    }
  };

  const handleSave = async () => {
    try {
      // 构建条件对象
      const conditions: any = {};
      conditionFields.forEach((condition) => {
        if (condition.field && condition.value) {
          conditions[condition.field] = {
            operator: condition.operator,
            value: condition.value,
          };
        }
      });

      const ruleData = {
        ...formData,
        conditions,
      };

      if (mode === 'create') {
        await apiClient.post('/api/noise-reduction/rules/', ruleData);
        showSnackbar('规则创建成功', 'success');
      } else if (mode === 'edit') {
        await apiClient.put(`/api/noise-reduction/rules/${rule?.id}`, ruleData);
        showSnackbar('规则更新成功', 'success');
      }

      onSave();
    } catch (error) {
      console.error('保存规则失败:', error);
      showSnackbar('保存规则失败', 'error');
    }
  };

  const isReadOnly = mode === 'view';

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Typography variant="h6">
          {mode === 'create' ? '新建规则' : mode === 'edit' ? '编辑规则' : '查看规则'}
        </Typography>
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
          <Tab label="基本信息" />
          <Tab label="条件配置" />
          <Tab label="高级设置" />
          {mode === 'view' && <Tab label="统计信息" />}
        </Tabs>

        {/* 基本信息标签页 */}
        <TabPanel value={tabValue} index={0}>
          <Box className={classes.tabPanel}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="规则名称"
                  value={formData.rule_name}
                  onChange={(e) => handleInputChange('rule_name', e.target.value)}
                  disabled={isReadOnly}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>规则类型</InputLabel>
                  <Select
                    value={formData.rule_type}
                    onChange={(e) => handleInputChange('rule_type', e.target.value)}
                    disabled={isReadOnly}
                    label="规则类型"
                  >
                    {Object.entries(RULE_TYPES).map(([key, label]) => (
                      <MenuItem key={key} value={key}>{label}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>执行动作</InputLabel>
                  <Select
                    value={formData.action}
                    onChange={(e) => handleInputChange('action', e.target.value)}
                    disabled={isReadOnly}
                    label="执行动作"
                  >
                    {Object.entries(ACTIONS).map(([key, label]) => (
                      <MenuItem key={key} value={key}>{label}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="优先级"
                  type="number"
                  value={formData.priority}
                  onChange={(e) => handleInputChange('priority', parseInt(e.target.value))}
                  disabled={isReadOnly}
                  inputProps={{ min: 1, max: 10 }}
                  helperText="1-10，数字越小优先级越高"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="规则描述"
                  multiline
                  rows={3}
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  disabled={isReadOnly}
                />
              </Grid>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.is_active}
                      onChange={(e) => handleInputChange('is_active', e.target.checked)}
                      disabled={isReadOnly}
                    />
                  }
                  label="启用规则"
                />
              </Grid>
            </Grid>
          </Box>
        </TabPanel>

        {/* 条件配置标签页 */}
        <TabPanel value={tabValue} index={1}>
          <Box className={classes.tabPanel}>
            <Typography variant="h6" gutterBottom>
              匹配条件
            </Typography>
            <Box className={classes.conditionBuilder}>
              {conditionFields.map((condition, index) => (
                <Grid container spacing={2} key={index} style={{ marginBottom: 16 }}>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>字段</InputLabel>
                      <Select
                        value={condition.field}
                        onChange={(e) => handleConditionChange(index, 'field', e.target.value)}
                        disabled={isReadOnly}
                        label="字段"
                      >
                        <MenuItem value="title">标题</MenuItem>
                        <MenuItem value="description">描述</MenuItem>
                        <MenuItem value="source">来源</MenuItem>
                        <MenuItem value="severity">严重程度</MenuItem>
                        <MenuItem value="alert_type">告警类型</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>操作符</InputLabel>
                      <Select
                        value={condition.operator}
                        onChange={(e) => handleConditionChange(index, 'operator', e.target.value)}
                        disabled={isReadOnly}
                        label="操作符"
                      >
                        <MenuItem value="contains">包含</MenuItem>
                        <MenuItem value="equals">等于</MenuItem>
                        <MenuItem value="starts_with">开始于</MenuItem>
                        <MenuItem value="ends_with">结束于</MenuItem>
                        <MenuItem value="regex">正则表达式</MenuItem>
                        <MenuItem value="not_contains">不包含</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <TextField
                      fullWidth
                      size="small"
                      label="值"
                      value={condition.value}
                      onChange={(e) => handleConditionChange(index, 'value', e.target.value)}
                      disabled={isReadOnly}
                    />
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    {!isReadOnly && (
                      <Box>
                        <Button
                          size="small"
                          onClick={() => removeCondition(index)}
                          disabled={conditionFields.length === 1}
                          color="error"
                        >
                          删除
                        </Button>
                      </Box>
                    )}
                  </Grid>
                </Grid>
              ))}
              {!isReadOnly && (
                <Button
                  variant="outlined"
                  onClick={addCondition}
                  startIcon={<AddIcon />}
                  size="small"
                >
                  添加条件
                </Button>
              )}
            </Box>

            <Typography variant="h6" gutterBottom style={{ marginTop: 24 }}>
              筛选器配置
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <InputLabel>严重程度筛选</InputLabel>
                  <Select
                    multiple
                    value={formData.severity_filter || []}
                    onChange={(e) => handleInputChange('severity_filter', e.target.value)}
                    disabled={isReadOnly}
                    label="严重程度筛选"
                  >
                    {Object.entries(SEVERITIES).map(([key, label]) => (
                      <MenuItem key={key} value={key}>{label}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="来源筛选"
                  value={(formData.source_filter || []).join(', ')}
                  onChange={(e) => handleInputChange('source_filter', e.target.value.split(',').map(s => s.trim()).filter(s => s))}
                  disabled={isReadOnly}
                  helperText="多个来源用逗号分隔"
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="类型筛选"
                  value={(formData.type_filter || []).join(', ')}
                  onChange={(e) => handleInputChange('type_filter', e.target.value.split(',').map(s => s.trim()).filter(s => s))}
                  disabled={isReadOnly}
                  helperText="多个类型用逗号分隔"
                />
              </Grid>
            </Grid>
          </Box>
        </TabPanel>

        {/* 高级设置标签页 */}
        <TabPanel value={tabValue} index={2}>
          <Box className={classes.tabPanel}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="时间窗口(分钟)"
                  type="number"
                  value={formData.time_window_minutes}
                  onChange={(e) => handleInputChange('time_window_minutes', parseInt(e.target.value))}
                  disabled={isReadOnly}
                  helperText="在此时间窗口内统计告警频率"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="计数阈值"
                  type="number"
                  value={formData.count_threshold}
                  onChange={(e) => handleInputChange('count_threshold', parseInt(e.target.value))}
                  disabled={isReadOnly}
                  helperText="触发规则的最小告警数量"
                />
              </Grid>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  规则说明
                </Typography>
                <Typography variant="body2" color="textSecondary" paragraph>
                  • <strong>模式匹配</strong>: 基于文本模式匹配告警内容
                </Typography>
                <Typography variant="body2" color="textSecondary" paragraph>
                  • <strong>阈值检测</strong>: 基于告警频率和数量进行检测
                </Typography>
                <Typography variant="body2" color="textSecondary" paragraph>
                  • <strong>时间基础</strong>: 基于时间窗口和业务时间进行判断
                </Typography>
                <Typography variant="body2" color="textSecondary" paragraph>
                  • <strong>机器学习</strong>: 使用AI模型进行智能判断
                </Typography>
              </Grid>
            </Grid>
          </Box>
        </TabPanel>

        {/* 统计信息标签页 (仅查看模式) */}
        {mode === 'view' && (
          <TabPanel value={tabValue} index={3}>
            <Box className={classes.tabPanel}>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" color="primary">
                        {rule?.usage_count || 0}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        使用次数
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" color="primary">
                        {rule?.match_count || 0}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        匹配次数
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" color="primary">
                        {rule?.suppressed_count || 0}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        抑制次数
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" color="primary">
                        {rule?.effectiveness_score ? (rule.effectiveness_score * 100).toFixed(1) + '%' : '-'}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        有效性分数
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="body2" color="textSecondary">
                    创建时间: {rule?.created_at ? new Date(rule.created_at).toLocaleString() : '-'}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    更新时间: {rule?.updated_at ? new Date(rule.updated_at).toLocaleString() : '-'}
                  </Typography>
                </Grid>
              </Grid>
            </Box>
          </TabPanel>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>
          取消
        </Button>
        {mode !== 'view' && (
          <Button onClick={handleSave} variant="contained" color="primary">
            {mode === 'create' ? '创建' : '保存'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default AlertRulesManagement;