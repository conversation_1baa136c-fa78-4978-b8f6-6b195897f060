/* 告警规则管理页面样式 */

.alert-rules-management {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.rules-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.rules-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
  color: #1976d2;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  padding: 16px;
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-header {
  background-color: #f8f9fa;
}

.table-header .MuiTableCell-head {
  font-weight: bold;
  color: #333;
}

.status-chip {
  min-width: 80px;
}

.priority-chip {
  min-width: 60px;
}

.action-cell {
  display: flex;
  gap: 4px;
}

.filter-bar {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dialog-content {
  min-width: 600px;
  max-width: 800px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.condition-builder {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  background-color: #fafafa;
}

.tab-panel {
  padding: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .alert-rules-management {
    padding: 16px;
  }

  .rules-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-buttons {
    justify-content: center;
  }

  .filter-bar {
    flex-direction: column;
  }

  .stats-cards {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .dialog-content {
    min-width: auto;
    max-width: 100%;
  }
}

/* 动画效果 */
.stat-card {
  transition: transform 0.2s ease-in-out;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.MuiTableRow-root:hover {
  background-color: #f5f5f5;
}

/* 自定义滚动条 */
.condition-builder::-webkit-scrollbar {
  width: 6px;
}

.condition-builder::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.condition-builder::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.condition-builder::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 状态指示器 */
.rule-active {
  color: #4caf50;
}

.rule-inactive {
  color: #f44336;
}

.rule-testing {
  color: #ff9800;
}

/* 优先级颜色 */
.priority-high {
  background-color: #ffebee;
  color: #c62828;
}

.priority-medium {
  background-color: #fff3e0;
  color: #ef6c00;
}

.priority-low {
  background-color: #e8f5e8;
  color: #2e7d32;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 48px 24px;
  color: #666;
}

.empty-state-icon {
  font-size: 64px;
  color: #ccc;
  margin-bottom: 16px;
}

/* 工具提示样式 */
.MuiTooltip-tooltip {
  font-size: 12px;
  max-width: 200px;
}

/* 表单验证错误 */
.form-error {
  color: #f44336;
  font-size: 12px;
  margin-top: 4px;
}

/* 成功状态 */
.success-message {
  color: #4caf50;
  background-color: #e8f5e8;
  padding: 8px 16px;
  border-radius: 4px;
  margin: 8px 0;
}

/* 错误状态 */
.error-message {
  color: #f44336;
  background-color: #ffebee;
  padding: 8px 16px;
  border-radius: 4px;
  margin: 8px 0;
}