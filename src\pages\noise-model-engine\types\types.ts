export interface ModelConfig {
  model_type: string; // "local" | "openai"
  model_path?: string;
  api_key?: string;
  api_base?: string;
  model_name?: string;
  temperature?: number;
  max_tokens?: number;
  // 其它可能的字段
}

export interface NoiseReductionConfig {
  similarity_threshold?: number;
  time_window_minutes?: number;
  confidence_threshold?: number;
  enable_clustering?: boolean;
  enable_suppression?: boolean;
  enable_priority_adjustment?: boolean;
  // 其它可能的字段
}

export interface ConfigType {
  id?: number;
  config_name: string;
  config_type: "local" | "openai" | "silence"| "escalate"| "clustering"| string;
  description?: string;
  model_config?: ModelConfig;
  noise_reduction_config?: NoiseReductionConfig;
  is_active?: boolean;
  is_default?: boolean;
  created_at?: string;
  silence_config?: SilenceRule;
  escalation_config?: EscalationRule;
  cluster_config?: ClusteringRule;

}

export interface SilenceRule {
    rule_name: string;
    condition: string;
    expire_at: Date;
    is_open: boolean;
  }

export interface EscalationRule {
    rule_name: string;             // 规则名称
    condition: string;       // 条件表达式
    threshold_count: number;        // 触发阈值（次数）
    threshold_period: number;         // 时间周期（分钟）
    new_severity: '严重' | '警告' | '错误'; // 升级至严重级别
    is_open: boolean; // 状态
}

export interface ClusteringRule {
    eps: number; // 规则半径
    time_window_minutes: number;   // 时间窗口（分钟）
    min_cluster_size: number;        // 最小聚类数
    cluster_features: string[];         // 聚类特征
    
}
  
// 模拟测试数据
export const fakeConfigs = [
    {
        id: 1,
        config_name: "本地模型配置",
        config_type: "local",
        description: "使用Foundation-Sec-8B本地模型的配置",
        is_active: true,
        is_default: true,
        created_at: "2024-06-01T10:20:30",
        model_config: {
            model_type: "local",
            model_path: "../Foundation-Sec-8B",
            temperature: 0.3,
            max_tokens: 512,
        },
        noise_reduction_config: {
            similarity_threshold: 0.8,
            time_window_minutes: 60,
            confidence_threshold: 0.7,
            enable_clustering: true,
            enable_suppression: true,
            enable_priority_adjustment: true,
        },
    },
    {
        id: 2,
        config_name: "自定义OpenAI配置",
        config_type: "openai",
        description: "使用GPT-4的自定义配置",
        is_active: false,
        is_default: false,
        created_at: "2024-06-02T11:15:00",
        model_config: {
            model_type: "openai",
            api_key: "sk-xxx",
            api_base: "https://api.openai.com/v1",
            model_name: "gpt-4",
            temperature: 0.3,
            max_tokens: 1024,
        },
        noise_reduction_config: {
            similarity_threshold: 0.85,
            time_window_minutes: 30,
            confidence_threshold: 0.8,
            enable_clustering: true,
            enable_suppression: true,
            enable_priority_adjustment: true,
        },
    },
    {
        id: 3,
        config_name: "本地模型配置",
        config_type: "local",
        description: "使用Foundation-Sec-8B本地模型的配置",
        is_active: true,
        is_default: true,
        created_at: "2024-06-01T10:20:30",
        model_config: {
            model_type: "local",
            model_path: "../Foundation-Sec-8B",
            temperature: 0.3,
            max_tokens: 512,
        },
        noise_reduction_config: {
            similarity_threshold: 0.8,
            time_window_minutes: 60,
            confidence_threshold: 0.7,
            enable_clustering: true,
            enable_suppression: true,
            enable_priority_adjustment: true,
        },
    },
    {
        id: 4,
        config_name: "自定义OpenAI配置",
        config_type: "openai",
        description: "使用GPT-4的自定义配置",
        is_active: false,
        is_default: false,
        created_at: "2024-06-02T11:15:00",
        model_config: {
            model_type: "openai",
            api_key: "sk-xxx",
            api_base: "https://api.openai.com/v1",
            model_name: "gpt-4",
            temperature: 0.3,
            max_tokens: 1024,
        },
        noise_reduction_config: {
            similarity_threshold: 0.85,
            time_window_minutes: 30,
            confidence_threshold: 0.8,
            enable_clustering: true,
            enable_suppression: true,
            enable_priority_adjustment: true,
        },
    },
    {
        id: 5,
        config_name: "本地模型配置",
        config_type: "local",
        description: "使用Foundation-Sec-8B本地模型的配置",
        is_active: true,
        is_default: true,
        created_at: "2024-06-01T10:20:30",
        model_config: {
            model_type: "local",
            model_path: "../Foundation-Sec-8B",
            temperature: 0.3,
            max_tokens: 512,
        },
        noise_reduction_config: {
            similarity_threshold: 0.8,
            time_window_minutes: 60,
            confidence_threshold: 0.7,
            enable_clustering: true,
            enable_suppression: true,
            enable_priority_adjustment: true,
        },
    },
    {
        id: 6,
        config_name: "自定义OpenAI配置",
        config_type: "openai",
        description: "使用GPT-4的自定义配置",
        is_active: false,
        is_default: false,
        created_at: "2024-06-02T11:15:00",
        model_config: {
            model_type: "openai",
            api_key: "sk-xxx",
            api_base: "https://api.openai.com/v1",
            model_name: "gpt-4",
            temperature: 0.3,
            max_tokens: 1024,
        },
        noise_reduction_config: {
            similarity_threshold: 0.85,
            time_window_minutes: 30,
            confidence_threshold: 0.8,
            enable_clustering: true,
            enable_suppression: true,
            enable_priority_adjustment: true,
        },
    },
    {
        id: 7,
        config_name: "本地模型配置",
        config_type: "local",
        description: "使用Foundation-Sec-8B本地模型的配置",
        is_active: true,
        is_default: true,
        created_at: "2024-06-01T10:20:30",
        model_config: {
            model_type: "local",
            model_path: "../Foundation-Sec-8B",
            temperature: 0.3,
            max_tokens: 512,
        },
        noise_reduction_config: {
            similarity_threshold: 0.8,
            time_window_minutes: 60,
            confidence_threshold: 0.7,
            enable_clustering: true,
            enable_suppression: true,
            enable_priority_adjustment: true,
        },
    },
    {
        id: 8,
        config_name: "自定义OpenAI配置",
        config_type: "openai",
        description: "使用GPT-4的自定义配置",
        is_active: false,
        is_default: false,
        created_at: "2024-06-02T11:15:00",
        model_config: {
            model_type: "openai",
            api_key: "sk-xxx",
            api_base: "https://api.openai.com/v1",
            model_name: "gpt-4",
            temperature: 0.3,
            max_tokens: 1024,
        },
        noise_reduction_config: {
            similarity_threshold: 0.85,
            time_window_minutes: 30,
            confidence_threshold: 0.8,
            enable_clustering: true,
            enable_suppression: true,
            enable_priority_adjustment: true,
        },
    },
    
];
