import React, { useEffect, useRef, useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Fab, Input, Link, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography, withStyles } from '@material-ui/core';
import Paper from '@material-ui/core/Paper';
import CheckCircleOutlineIcon from '@material-ui/icons/CheckCircleOutline';
import ErrorOutlineIcon from '@material-ui/icons/ErrorOutline';
import DataAccessManageDelete from './delete';
import DataAccessManageDetail from './detail';
import UpdateLogSource from './updateLogSource';
import { rows } from './mock';
const StyledTableCell = withStyles((theme) => ({
    head: {
      backgroundColor: '#F5F5F5',
      color: theme.palette.common.black,
      textAlign: 'center',
    },
    body: {
      fontSize: 14,
      textAlign: 'center',
    },
  }))(TableCell)
interface LogSourceTableProps {
    page: number;
    rowsPerPage: number;
}
export default function LogSourceTable({ page, rowsPerPage }: LogSourceTableProps) {
  const displayedRows = rows.slice(
      (page - 1) * rowsPerPage,
      page * rowsPerPage
      );
  return (
    <>
    <TableContainer component={Paper} style={{marginTop: 20}}>
                      <Table aria-label="simple table">
                        <TableHead>
                          <TableRow>
                             <StyledTableCell style={{ width: 400 }}>日志源名称</StyledTableCell>
                             <StyledTableCell>类型</StyledTableCell>
                             <StyledTableCell>状态</StyledTableCell>
                             <StyledTableCell>最后活跃</StyledTableCell>
                             <StyledTableCell>操作</StyledTableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                             {displayedRows.map((row) => (
                                <TableRow >
                                  <TableCell align="center">{row.src_name}</TableCell>
                                  <TableCell align="center">
                                     <span style={{width: 60,height: 30,display: 'inline-flex',justifyContent: 'center',alignItems: 'center',color: row.src_type_label === '防火墙' ? '#4E79A7' : row.src_type_label === 'IDS' ? '#F28E2B' : '#59A14F',backgroundColor: row.src_type_label === '防火墙' ? '#B4C6DA' : row.src_type_label === 'IDS' ? '#F9CFA5' : '#B9D7B5'}}>
                                       {row.src_type_label}
                                     </span>
                                  </TableCell>
                                  <TableCell align="center" style={{color: row.src_status_label === '正常' ? '#4CAF50' : '#FFD476',}} >
                                     <span  style={{display: 'inline-flex',alignItems: 'center',gap: '4px'}}>{row.src_status_label === '正常' ? <CheckCircleOutlineIcon/> : <ErrorOutlineIcon/>}{row.src_status_label}</span>
                                  </TableCell>
                                  <TableCell align="center">{row.last_active_time}</TableCell>
                                  <TableCell align="center">
                                    <UpdateLogSource row={row}/>
                                    <DataAccessManageDetail row={row}/>
                                    <DataAccessManageDelete name={row.src_name}/>
                                  </TableCell>
                                </TableRow>))
                             }
                        </TableBody>
                      </Table>
                  </TableContainer>
    </>
    )}