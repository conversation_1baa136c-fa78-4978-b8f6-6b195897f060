import React, { useState, useEffect } from 'react'
import axios from 'axios'
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Paper,
  LinearProgress,
  Grid,
  Card,
  CardContent,
  Slider,
  Radio,
  RadioGroup,
  FormControlLabel,
  Box,
} from '@material-ui/core'
import { makeStyles } from '@material-ui/core/styles'
import ReactECharts from 'echarts-for-react'
import dayjs from 'dayjs'
import { useNavigate } from 'react-router-dom'
import { useLocation } from 'react-router-dom'
import apiClient from '../apis/apiClient'
const useStyles = makeStyles((theme) => ({
  root: {
    minHeight: '100vh',
    background: '#f5fbf7',
    paddingBottom: theme.spacing(4),
  },
  appBar: {
    background: '#4caf50',
  },
  pageContainer: {
    width: '54%',
    maxWidth: 'none',
    margin: '0 auto',
  },
  section: {
    padding: theme.spacing(3),
    marginBottom: theme.spacing(3),
  },
  sectionTitle: {
    fontWeight: 'bold',
    color: '#388e3c',
    marginBottom: theme.spacing(2),
  },
  progressBar: {
    height: 10,
    borderRadius: 5,
    marginTop: theme.spacing(1),
  },
  statCard: {
    textAlign: 'center',
    padding: theme.spacing(2, 0),
  },
  saveBtn: {
    background: '#4caf50',
    color: '#fff',
    marginRight: theme.spacing(2),
    '&:hover': {
      background: '#388e3c',
    },
  },

  progressLabel: {
    position: 'absolute',
    top: -28,
    transform: 'translateX(-50%)',
    background: '#e8f5e9',
    color: '#388e3c',
    padding: '2px 12px',
    borderRadius: 12,
    fontSize: 14,
    fontWeight: 500,
    whiteSpace: 'nowrap',
    transition: 'left 0.3s',
  },
  progressPercent: {
    position: 'absolute',
    top: 18,
    transform: 'translateX(-50%)',
    color: '#888',
    fontSize: 15,
    transition: 'left 0.3s',
    whiteSpace: 'nowrap',
  },
  sliderRoot: {
    color: '#e0e0e0',
    height: 8,
    padding: '13px 0',
  },
  thumb: {
    height: 24,
    width: 24,
    backgroundColor: '#4caf50',
    marginTop: -8,
    marginLeft: -12,
    boxShadow: '0 2px 6px 0 rgba(0,0,0,0.2)',
    '&:focus, &:hover, &$active': {
      boxShadow: '0 2px 6px 0 rgba(76,175,80,0.3)',
    },
  },
  active: {},
  track: {
    height: 8,
    borderRadius: 4,
    color: '#e0e0e0',
  },
  rail: {
    height: 8,
    borderRadius: 4,
    color: '#e0e0e0',
    opacity: 1,
  },
  mark: {
    backgroundColor: '#bdbdbd',
    height: 12,
    width: 2,
    marginTop: -2,
    display: 'none',
  },
  markLabel: {
    top: 36,
    color: '#666',
    fontSize: 16,
  },
}))

const API_BASE = 'http://192.168.12.244:35000' // 后端地址
//const API_BASE = '/api'
const TASK_ID = '1'

const TaskStatusMonitor: React.FC = () => {
  const classes = useStyles()
  const [priority, setPriority] = useState<number>(3)
  const [execMode, setExecMode] = useState<'immediate' | 'scheduled'>(
    'immediate'
  )

  const navigate = useNavigate()
  const location = useLocation()
  const searchParams = new URLSearchParams(location.search)
  const taskId = searchParams.get('taskId') || '1'

  const [status, setStatus] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    apiClient
      .get(`/api/noise-reduction/tasks/${taskId}/status`)
      .then((res) => {
        setStatus(res.data)
        setLoading(false)
      })
      .catch((err) => {
        setError('获取任务状态失败.')
        setLoading(false)
      })
  }, [taskId])

  const [saving, setSaving] = useState(false)
  const [saveError, setSaveError] = useState<string | null>(null)
  const [saveSuccess, setSaveSuccess] = useState(false)

  // 解析数据
  const progress = status?.progress ?? 0
  const statistics = status?.statistics ?? {}
  const startTime = status?.start_time ?? ''
  const endTime = status?.end_time ?? ''
  const statusText =
    status?.status === 'running'
      ? '运行中'
      : status?.status === 'finished'
      ? '已完成'
      : ''

  // 时间格式化
  function formatTime(str: string | null) {
    if (!str) return '--'
    const date = new Date(str)
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  // 降噪效果图表数据
  const barOption = {
    color: ['#4caf50'],
    tooltip: {},
    xAxis: { type: 'category', data: ['原始', '处理后'] },
    yAxis: { type: 'value' },
    series: [
      {
        type: 'bar',
        data: [statistics.total_alerts ?? 0, statistics.processed_alerts ?? 0],
        barWidth: '40%',
      },
    ],
    grid: { left: 40, right: 20, top: 30, bottom: 30 },
  }

  // 统计卡片
  const stats = [
    { label: '总告警数', value: statistics.total_alerts ?? 0 },
    { label: '已处理告警', value: statistics.processed_alerts ?? 0 },
    { label: '抑制告警', value: statistics.suppressed_alerts ?? 0 },
    { label: '合并告警', value: statistics.merged_alerts ?? 0 },
  ]

  return (
    <div className={classes.root}>
      <div className={classes.pageContainer}>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: 16,
            paddingTop: 48,
          }}>
          <Typography variant="h5" className={classes.sectionTitle}>
            任务状态监控
          </Typography>
          <Button
            style={{ color: '#4caf50', fontWeight: 600, fontSize: 16 }}
            onClick={() => navigate('/noiseAnalysisTask')}>
            返回列表
          </Button>
        </div>

        {/* 进度条 */}
        <Paper className={classes.section} style={{ paddingTop: 48 }}>
          {loading ? (
            <Typography>加载中...</Typography>
          ) : error ? (
            <Typography color="error">{error}</Typography>
          ) : (
            <div style={{ position: 'relative', marginBottom: 32 }}>
              {/* 进度标签 */}
              <div
                className={classes.progressLabel}
                style={{ left: `calc(${progress}% )` }}>
                {statusText}
              </div>
              {/* 百分比标签 */}
              <div
                className={classes.progressPercent}
                style={{ left: `calc(${progress}% )` }}>
                {progress}% 完成
              </div>
              {/* 进度条 */}
              <LinearProgress
                variant="determinate"
                value={progress}
                className={classes.progressBar}
              />
            </div>
          )}
        </Paper>

        {/* 时间线 */}
        <Paper className={classes.section} style={{ paddingTop: 48 }}>
          <Typography
            variant="subtitle1"
            className={classes.sectionTitle}
            style={{ fontSize: 28, marginTop: -30 }}>
            时间线
          </Typography>
          <div style={{ position: 'relative', width: '100%', marginTop: 50 }}>
            <div style={{ flexGrow: 1, position: 'relative' }}>
              {/* 绿色线 */}
              <div
                style={{
                  position: 'absolute',
                  left: 0,
                  right: 0,
                  top: '50%',
                  height: 4,
                  background: '#4caf50',
                  transform: 'translateY(-50%)',
                  borderRadius: 2,
                  zIndex: 1,
                }}
              />
              {/* 开始点 */}
              <div
                style={{
                  width: 16,
                  height: 16,
                  borderRadius: '50%',
                  background: '#4caf50',
                  position: 'absolute',
                  left: 0,
                  top: '50%',
                  transform: 'translate(-50%, -50%)',
                  zIndex: 2,
                }}
              />
              {/* 当前进度点 */}
              <div
                style={{
                  width: 16,
                  height: 16,
                  borderRadius: '50%',
                  background: '#4caf50',
                  position: 'absolute',
                  left: `${progress}%`,
                  top: '50%',
                  transform: 'translate(-50%, -50%)',
                  zIndex: 2,
                }}
              />
              {/* 结束点 */}
              <div
                style={{
                  width: 16,
                  height: 16,
                  borderRadius: '50%',
                  background: '#4caf50',
                  position: 'absolute',
                  right: 0,
                  top: '50%',
                  transform: 'translate(50%, -50%)',
                  zIndex: 2,
                }}
              />
            </div>
            <div style={{ position: 'relative', width: '100%' }}>
              {/* ...上面是圆点和线 */}
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginTop: 2,
                  position: 'relative',
                }}>
                {/* 开始 */}
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'flex-start',
                    marginTop: 20,
                    marginLeft: -13,
                    minWidth: 60,
                  }}>
                  <span style={{ fontSize: 14, color: '#222' }}>开始</span>
                  <span style={{ fontSize: 13, color: '#888', marginTop: 2 }}>
                    {formatTime(startTime)}
                  </span>
                </div>
                {/* 结束 */}
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'flex-end',
                    marginTop: 20,
                    minWidth: 80,
                  }}>
                  <span style={{ fontSize: 14, color: '#222' }}>预计结束</span>
                  <span style={{ fontSize: 13, color: '#888', marginTop: 2 }}>
                    {endTime ? formatTime(endTime) : '--'}
                  </span>
                </div>
                {/* 当前（用绝对定位，跟随progress） */}
                {status?.status === 'running' && (
                  <div
                    style={{
                      position: 'absolute',
                      left: `calc(${progress}% - 30px)`,
                      top: -70,
                      width: 60,
                      marginTop: 20,
                      textAlign: 'center',
                      pointerEvents: 'none',
                    }}>
                    <span style={{ fontSize: 14, color: '#222' }}>当前</span>
                    <br />
                    <span style={{ fontSize: 13, color: '#888', marginTop: 2 }}>
                      {dayjs(new Date().toISOString()).format('HH:mm')}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </Paper>

        {/* 统计概览 */}
        <Paper className={classes.section}>
          <Typography
            variant="subtitle1"
            className={classes.sectionTitle}
            style={{ fontSize: 28 }}>
            统计概览
          </Typography>
          <Grid container spacing={2}>
            {stats.map((stat) => (
              <Grid item xs={3} key={stat.label}>
                <Card variant="outlined" className={classes.statCard}>
                  <CardContent>
                    <Typography
                      variant="h6"
                      style={{ color: '#388e3c', fontWeight: 'bold' }}>
                      {stat.value.toLocaleString()}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      {stat.label}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Paper>

        {/* 降噪效果 */}
        <Paper className={classes.section}>
          <Typography
            variant="subtitle1"
            className={classes.sectionTitle}
            style={{ fontSize: 28 }}>
            降噪效果
          </Typography>
          <ReactECharts option={barOption} style={{ height: 200 }} />
        </Paper>

        {/* 操作面板 */}
        <Paper className={classes.section}>
          <Typography
            variant="subtitle1"
            className={classes.sectionTitle}
            style={{ fontSize: 28 }}>
            操作面板
          </Typography>
          <Box marginTop={0} marginBottom={3}>
            <Typography gutterBottom style={{ marginBottom: 4 }}>
              优先级调整
            </Typography>
            <Slider
              value={priority}
              min={1}
              max={5}
              step={1}
              marks={[
                { value: 1, label: '1' },
                { value: 2, label: '2' },
                { value: 3, label: '3' },
                { value: 4, label: '4' },
                { value: 5, label: '5' },
              ]}
              style={{ width: 300 }}
              onChange={(_, v) => setPriority(v as number)}
              valueLabelDisplay="off"
              classes={{
                root: classes.sliderRoot,
                thumb: classes.thumb,
                active: classes.active,
                track: classes.track,
                rail: classes.rail,
                mark: classes.mark,
                markLabel: classes.markLabel,
              }}
            />
          </Box>

          <Box marginBottom={2}>
            <Typography gutterBottom>执行模式</Typography>
            <RadioGroup
              row
              value={execMode}
              onChange={(e) =>
                setExecMode(e.target.value as 'immediate' | 'scheduled')
              }>
              <FormControlLabel
                value="immediate"
                control={<Radio />}
                label="立即执行"
              />
              <FormControlLabel
                value="scheduled"
                control={<Radio />}
                label="计划执行"
              />
            </RadioGroup>
          </Box>
          <Box>
            <Button
              variant="contained"
              className={classes.saveBtn}
              disabled={saving}
              onClick={async () => {
                setSaving(true)
                setSaveError(null)
                setSaveSuccess(false)
                try {
                  await apiClient.post(
                    `/api/noise-reduction/tasks/${taskId}/execute`,
                    {
                      priority,
                      execution_mode: execMode,
                    }
                  )

                  setSaveSuccess(true)
                } catch (e) {
                  setSaveError('保存失败，请重试')
                } finally {
                  setSaving(false)
                }
              }}>
              {saving ? '保存中...' : '保存设置'}
            </Button>
            {saveError && (
              <Typography color="error" style={{ marginTop: 8 }}>
                {saveError}
              </Typography>
            )}
            {saveSuccess && (
              <Typography style={{ color: '#388e3c', marginTop: 8 }}>
                保存成功！
              </Typography>
            )}
          </Box>
        </Paper>
      </div>
    </div>
  )
}

export default TaskStatusMonitor
