import Snackbar from '@material-ui/core/Snackbar';
import ReactDOM from 'react-dom';
import Alert, { Color } from '@material-ui/lab/Alert';

export function showSnackbar(
  message: string,
  severity: Color = "info",
  duration = 3000
) {
  // 创建一个容器
  const div = document.createElement("div");
  document.body.appendChild(div);

  const handleClose = () => {
    ReactDOM.unmountComponentAtNode(div);
    div.remove();
  };

  ReactDOM.render(
    <Snackbar
      open
      autoHideDuration={duration}
      onClose={handleClose}
      anchorOrigin={{ vertical: "top", horizontal: "center" }}
    >
      <Alert onClose={handleClose} severity={severity}>
        {message}
      </Alert>
    </Snackbar>,
    div
  );
}
