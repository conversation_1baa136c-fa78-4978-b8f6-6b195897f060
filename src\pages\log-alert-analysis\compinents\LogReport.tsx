// src/components/LogReport.tsx
import React from 'react';
import { makeStyles } from '@material-ui/core/styles';
import Grid from '@material-ui/core/Grid';
import Paper from '@material-ui/core/Paper';
import Typography from '@material-ui/core/Typography';
import Container from '@material-ui/core/Container';
import Box from '@material-ui/core/Box';

const useStyles = makeStyles((theme) => ({
  section: {
    marginTop: theme.spacing(4),
  },
  paper: {
    padding: theme.spacing(2),
    minWidth: 80,
    textAlign: 'center',
  },
  boldText: {
    fontWeight: 'bold',
  },
  highRiskCard: {
    backgroundColor: '#fff3cd',
    padding: theme.spacing(2),
    marginTop: theme.spacing(2),
  },
}));

const threatLevels = [
  { label: '严重', count: 4, color: '#f8d7da' },
  { label: '高危', count: 3, color: '#fff3cd' },
  { label: '中等', count: 12, color: '#fff8e1' },
  { label: '低危', count: 10, color: '#d4edda' },
];

const highRiskEvents = [
  {
    level: 'HIGH',
    summary:
      '日志显示尝试访问敏感系统文件（/etc/passwd），使用路径遍历攻击（../../../../etc/passwd）。虽然返回 404，但表明存在探测行为。',
    technique: 'Exploit Public-Facing Application',
    confidence: 0.9,
    suggestion:
      "建议：对 URL 进行输入验证，阻止路径遍历模式（如 '../'）；确保 Web 服务器配置安全。",
  },
    {
    level: 'HIGH',
    summary:
      '日志显示尝试访问敏感系统文件（/etc/passwd），使用路径遍历攻击（../../../../etc/passwd）。虽然返回 404，但表明存在探测行为。',
    technique: 'Exploit Public-Facing Application',
    confidence: 0.9,
    suggestion:
      "建议：对 URL 进行输入验证，阻止路径遍历模式（如 '../'）；确保 Web 服务器配置安全。",
  },
    {
    level: 'HIGH',
    summary:
      '日志显示尝试访问敏感系统文件（/etc/passwd），使用路径遍历攻击（../../../../etc/passwd）。虽然返回 404，但表明存在探测行为。',
    technique: 'Exploit Public-Facing Application',
    confidence: 0.9,
    suggestion:
      "建议：对 URL 进行输入验证，阻止路径遍历模式（如 '../'）；确保 Web 服务器配置安全。",
  },
    {
    level: 'HIGH',
    summary:
      '日志显示尝试访问敏感系统文件（/etc/passwd），使用路径遍历攻击（../../../../etc/passwd）。虽然返回 404，但表明存在探测行为。',
    technique: 'Exploit Public-Facing Application',
    confidence: 0.9,
    suggestion:
      "建议：对 URL 进行输入验证，阻止路径遍历模式（如 '../'）；确保 Web 服务器配置安全。",
  },
];

const LogReport: React.FC = () => {
  const classes = useStyles();
  const now = new Date().toLocaleString();

  return (
    <Container>
      {/* 报告标题 */}
      <Typography variant="h5" gutterBottom>
        日志分析摘要报告
      </Typography>

      {/* 报告信息 */}
      <Typography variant="body2" color="textSecondary">
        生成时间：{now}
      </Typography>
      <Typography variant="body2">总分析条数：29</Typography>
      <Typography variant="body2" gutterBottom>
        平均置信度：0.74
      </Typography>

      {/* 威胁等级统计 */}
      <Box className={classes.section}>
        <Typography variant="h6">威胁级别统计</Typography>
        <Grid container spacing={2}>
          {threatLevels.map((item) => (
            <Grid item key={item.label}>
              <Paper
                className={classes.paper}
                style={{ backgroundColor: item.color }}
                elevation={2}
              >
                <Typography className={classes.boldText}>{item.label}</Typography>
                <Typography>{item.count}</Typography>
              </Paper>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* 高危事件 */}
      <Box className={classes.section}>
        <Typography variant="h6">高危事件</Typography>
        {highRiskEvents.map((event, index) => (
          <Paper key={index} className={classes.highRiskCard} elevation={2}>
            <Typography className={classes.boldText}>
              威胁级别：{event.level}
            </Typography>
            <Typography>摘要：{event.summary}</Typography>
            <Typography>攻击技术：{event.technique}</Typography>
            <Typography>置信度：{event.confidence}</Typography>
            <Typography>建议：{event.suggestion}</Typography>
          </Paper>
        ))}
      </Box>
    </Container>
  );
};

export default LogReport;
