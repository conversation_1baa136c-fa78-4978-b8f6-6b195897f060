import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>A<PERSON>, <PERSON>alogContent, DialogTitle, Grid, MenuItem, TextField } from "@material-ui/core";
import React, { useState, useEffect } from "react";
import { showSnackbar } from "../points-manage/component/myMessageBar";
import { time } from "echarts";

interface ILogData {
  row1:{
    timestamp: string
    log_name: string
    alert_signature: string
    severity: number
    src_ip: string
    src_port: string
    dst_ip: string
    dst_port: string
    protocol: number
    category: string
    payload: string
    created_at: string
    updated_at: string
    geo_location:string
  }
}

export default function UpdateLogFile({row1}: ILogData) {
    const defaultForm = {
    log_name: row1.log_name,
    timestamp: row1.timestamp,
    alert_signature: row1.alert_signature,
    severity: row1.severity,
    src_ip: row1.src_ip,
    src_port: row1.src_port,
    dst_ip: row1.dst_ip,
    dst_port: row1.dst_port,
    protocol: row1.protocol,
    category: row1.category,
    payload: row1.payload,
    geo_location: row1.geo_location
    };
    const [open, setOpen] = useState(false);
    const onClose = () => {setOpen(false)};
    const onSubmit = (data: any) => {
        console.log(data);
        showSnackbar("更新成功", "success");
        setOpen(false);
    };
    const [form, setForm] = useState(defaultForm);
    useEffect(() => {
        if (!open) setForm(defaultForm);
    }, [open]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setForm((prev) => ({ ...prev, [name]: value }));
    };

    const handleConfirm = () => {
        if (!form.timestamp ||!form.log_name ||!form.alert_signature ||!form.severity ||!form.src_ip ||!form.src_port ||!form.dst_ip ||!form.dst_port ||!form.protocol ||!form.category ||!form.payload ||!form.geo_location) {
              showSnackbar("请填写必填项", "error");
              return;
            }
        let submitData = {
            ...form,
        };
        onSubmit(submitData);
    };

    return (
        <>
        <Button style={{border: '1px solid #E0E0E0',marginRight: 10,fontSize: '16px'}} onClick={() => setOpen(true)}>配置</Button>
        <Dialog open={open} onClose={onClose}  maxWidth="sm" fullWidth>
            <DialogTitle>更新日志</DialogTitle>
            <DialogContent>
                <Grid container spacing={2}>
                    <Grid item xs={6}>
                        <TextField label="日志名称" name="log_name" value={form.log_name} onChange={handleChange} margin="dense" required  fullWidth />
                    </Grid> 
                    <Grid item xs={6}>
                        <TextField label="告警签名名称" name="alert_signature" value={form.alert_signature} onChange={handleChange} margin="dense" required  fullWidth />
                    </Grid> 
                    <Grid item xs={6}>
                        <TextField select label="告警等级" name="severity" value={form.severity} onChange={handleChange} margin="dense" required  fullWidth >
                            <MenuItem value="1">低</MenuItem>
                            <MenuItem value="2">中</MenuItem>
                            <MenuItem value="3">高</MenuItem>
                        </TextField>
                    </Grid> 
                     <Grid item xs={6}>
                        <TextField select label="协议类型" name="protocol" value={form.protocol} onChange={handleChange} margin="dense" required  fullWidth >
                            <MenuItem value="1">TCP</MenuItem>
                            <MenuItem value="2">UDP</MenuItem>
                        </TextField>
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="源IP" name="src_ip" value={form.src_ip} onChange={handleChange} margin="dense" required  fullWidth />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="源端口" name="src_port" value={form.src_port} onChange={handleChange} margin="dense" required  fullWidth />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="目标IP" name="dst_ip" value={form.dst_ip} onChange={handleChange} margin="dense" required  fullWidth />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="目标端口" name="dst_port" value={form.dst_port} onChange={handleChange} margin="dense" required  fullWidth />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="攻击类别" name="category" value={form.category} onChange={handleChange} margin="dense" required  fullWidth />
                    </Grid>
                     <Grid item xs={6}>
                        <TextField label="报文内容摘要" name="payload" value={form.payload} onChange={handleChange} margin="dense" required  fullWidth />
                     </Grid>
                     <Grid item xs={6}>
                        <TextField label="源IP地理位置" name="geo_location" value={form.geo_location} onChange={handleChange} margin="dense" required  fullWidth />
                     </Grid>
                     <Grid item xs={6}>
                        <TextField label="日志时间" name="timestamp" value={form.timestamp} onChange={handleChange} margin="dense" required  fullWidth />
                     </Grid>
                </Grid>
            </DialogContent>
            <DialogActions>
                <Button  onClick={onClose}>取消</Button>
                <Button  onClick={handleConfirm} variant="contained" color="primary">
                    确认
                </Button>
            </DialogActions>
        </Dialog>
        </>
    );
}
