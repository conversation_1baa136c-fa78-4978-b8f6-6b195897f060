// components/ChartDownloadButton.tsx
import React from 'react';
import { Button, makeStyles } from '@material-ui/core';
import DownloadIcon from '@material-ui/icons/CloudDownload'; // MUI v4 没有 Download，推荐使用 CloudDownload

interface ChartDownloadButtonProps {
  chartRef: React.RefObject<any>; // ECharts 实例的 ref
  filename?: string; // 下载文件名
  label?: string; // 按钮上的文字
}

const useStyles = makeStyles({
  button: {
    marginTop: 16,
    backgroundColor: '#4DB6AC',
    color: '#fff',
    textTransform: 'none',
    fontSize: '16px',
    padding: '8px 20px',
    borderRadius: '6px',
    boxShadow: '0px 3px 1px -2px rgba(0,0,0,0.2), 0px 2px 2px 0px rgba(0,0,0,0.14), 0px 1px 5px 0px rgba(0,0,0,0.12)',
    '&:hover': {
      backgroundColor: '#00897B',
    },
  },
  icon: {
    marginRight: 8,
  },
});

const ChartDownloadButton: React.FC<ChartDownloadButtonProps> = ({
  chartRef,
  filename = 'chart',
  label = '下载图表',
}) => {
  const classes = useStyles();

  const handleDownload = () => {
    const echartsInstance = chartRef.current?.getEchartsInstance?.();
    if (echartsInstance) {
      const img = echartsInstance.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff',
      });

      const link = document.createElement('a');
      link.href = img;
      link.download = `${filename}.png`;
      link.click();
    }
  };

  return (
    <Button
      onClick={handleDownload}
      variant="contained"
      className={classes.button}
    >
      <DownloadIcon className={classes.icon} />
      {label}
    </Button>
  );
};

export default ChartDownloadButton;
