import React, { useState } from 'react';
import { makeStyles, createStyles, Theme } from '@material-ui/core/styles';
import Button from '@material-ui/core/Button';
import IconButton from '@material-ui/core/IconButton';
import PhotoCamera from '@material-ui/icons/PhotoCamera';
import { Dialog, DialogActions, DialogContent, DialogTitle, Grid, MenuItem, TextField, Typography } from '@material-ui/core';
import { showSnackbar } from '../points-manage/component/myMessageBar';
import { DropzoneArea } from 'material-ui-dropzone';
const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    input: {
      display: 'none',
    },
  previewChip: {
    minWidth: 160,
    maxWidth: 210
  },
  customDropZone: {
    // minHeight: 80,      // 最小高度 80px
    // maxHeight: 200,     // 最大高度 200px
    height: "100px !important",
    margin: '0 auto',   // 居中（可选）
    border: '2px dashed #999',
  },
  customParagraph: {
    fontSize: '25px',
    color: '#666',
  },
  }),
);
export default function AddLogFile() {
    const classes = useStyles();
    const [open, setOpen] = useState(false);
    const onClose = () => {setOpen(false)};
        const onSubmit = (data: any) => {
            console.log(data);
            showSnackbar("添加成功", "success");
            setOpen(false);
        };
  const [files, setFiles] = useState<File[]>([]);

   const handleUpload = async () => {
    if (files.length === 0) {
      showSnackbar("请先选择文件", "warning");
      return;
    }

    const formData = new FormData();
    files.forEach((file) => {
      formData.append('files', file); // 后端接收字段名根据接口改
    });

    try {
      // const resp = await fetch('/api/uploadLogs', { 
      //   method: 'POST',
      //   body: formData,
      // });
      // if (!resp.ok) throw new Error(`上传失败：${resp.statusText}`);
      console.log(formData)
      console.log(files)
      showSnackbar("上传成功", "success");
      setOpen(false);
      setFiles([]);  // 清空选中文件
    } catch (err: any) {
      console.error(err);
      showSnackbar(err.message || "上传出错", "error");
    }
  };

    return (
        <>
         <Button style={{color: '#4CAF50',border: '1px solid #E0E0E0',borderRadius: 6,marginRight: 20,fontSize: '20px'}} onClick={() => {setOpen(true)}}>上传日志文件</Button>
      
            <Dialog open={open} onClose={onClose}  maxWidth="sm" fullWidth>
            <DialogTitle>上传日志文件</DialogTitle>
            <DialogContent>
                  <div className="myUploader" style={{width: '95%',margin: '0 auto'}}>
                    <DropzoneArea 
                      showPreviews={true} 
                      showPreviewsInDropzone={false} 
                      useChipsForPreview
                      dropzoneClass={classes.customDropZone}
                      dropzoneParagraphClass={classes.customParagraph}
                      previewGridProps={{container: { spacing: 1, direction: 'row' }}}
                      previewChipProps={{classes: { root: classes.previewChip } }}
                      dropzoneText="请将文件拖拽到此处，或点击此处上传"
                      previewText="已选择文件："
                      showAlerts={false}
                      filesLimit={6}
                      maxFileSize={50 * 1024 * 1024}
                      onChange={(uploadedFiles) => {
                        setFiles(uploadedFiles);
                      }}
                      />
                  </div>
            </DialogContent>
            <DialogActions>
                <Button  onClick={onClose}>取消</Button>
                <Button  variant="contained" color="primary"  onClick={handleUpload} disabled={files.length === 0}  >
                    确认
                </Button>
            </DialogActions>
        </Dialog>
        </>
    );
}
{/* <input
        className={classes.input}
        id="contained-button-file"
        multiple
        type="file"
        onChange={handleFileChange}
      />
      <label htmlFor="contained-button-file">
        <Button  component="span" style={{color: '#4CAF50',border: '1px solid #E0E0E0',borderRadius: 6,marginRight: 20,fontSize: '20px'}}>
          上传日志文件
        </Button>
        
      </label> */}