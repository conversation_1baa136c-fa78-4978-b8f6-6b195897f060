import React, { useState, useEffect } from 'react';
import { Box, Paper } from '@material-ui/core';
import AdvancedFilter from './AdvancedFilter';
import TaskTable from './TaskTable';
import './styles.css';
import apiClient from '../../pages/apis/apiClient';
import { useNavigate } from 'react-router-dom';

// 任务状态类型 - 修改为与API一致
export type TaskStatus = '待处理' | '运行中' | '已完成' | '已取消' | '失败';

// API状态映射
const statusApiMap: Record<TaskStatus, string> = {
  '待处理': 'pending',
  '运行中': 'running',
  '已完成': 'completed',
  '已取消': 'cancelled',
  '失败': 'failed'
};

// 任务类型
export type TaskType = '批量' | '实时' | '计划';

// API任务类型映射
const taskTypeApiMap: Record<TaskType, string> = {
  '批量': 'batch',
  '实时': 'realtime',
  '计划': 'scheduled'
};

// 输入源类型
export type InputSource = '文件' | '文本' | '数据库' | '蜜罐';

// API输入源映射
const inputSourceApiMap: Record<InputSource, string> = {
  '文件': 'file',
  '文本': 'text',
  '数据库': 'database',
  '蜜罐': 'honeypot'
};

// 任务数据类型
export interface TaskData {
  id: number;
  taskName: string;
  type: TaskType;
  status: TaskStatus;
  progress: number;
  inputSource: InputSource;
  fileInfo: string;
  createTime: string;
}

// API返回的任务数据类型
interface ApiTaskData {
  id: number;
  task_name: string;
  task_type: string;
  status: string;
  progress: number;
  input_source: string;
  file_info?: {
    original_filename: string;
    file_size: number;
    file_size_readable: string;
    file_type: string;
  };
  created_at: string;
  total_alerts?: number;
  processed_alerts?: number;
}

// API返回的分页数据
interface ApiPagination {
  total: number;
  pages: number;
  current_page: number;
  per_page: number;
  has_next: boolean;
  has_prev: boolean;
}

// API响应结构
interface ApiResponse {
  code: number;
  message: string;
  tasks: ApiTaskData[];
  pagination: ApiPagination;
}

// 筛选条件类型
export interface FilterConditions {
  status: TaskStatus[];
  taskTypes: TaskType[];
  inputSources: InputSource[];
  channelId?: string; // 对应 honeypot_id
  nodeId?: string;    // 对应 node_id
  creatorName?: string; // 对应 created_by
  sortField: string;    // 对应 sort_by
  sortDirection: 'asc' | 'desc'; // 对应 sort_order
}

// 将API返回的任务数据转换为前端使用的格式
const convertApiTaskToTaskData = (apiTask: ApiTaskData): TaskData => {
  // 转换任务类型
  const type: TaskType = 
    apiTask.task_type === 'batch' ? '批量' :
    apiTask.task_type === 'realtime' ? '实时' : '计划';
  
  // 转换状态
  const status: TaskStatus = 
    apiTask.status === 'pending' ? '待处理' :
    apiTask.status === 'running' ? '运行中' :
    apiTask.status === 'completed' ? '已完成' :
    apiTask.status === 'cancelled' ? '已取消' : '失败';
  
  // 转换输入源
  const inputSource: InputSource = 
    apiTask.input_source === 'file' ? '文件' :
    apiTask.input_source === 'text' ? '文本' :
    apiTask.input_source === 'database' ? '数据库' : '蜜罐';

  // 文件信息
  let fileInfo = '';
  if (apiTask.file_info) {
    fileInfo = `${apiTask.file_info.original_filename} (${apiTask.file_info.file_size_readable})`;
  } else if (apiTask.input_source === 'honeypot') {
    fileInfo = 'Honeypot';
  } else if (apiTask.input_source === 'database') {
    fileInfo = 'Database';
  } else {
    fileInfo = 'Unknown';
  }

  // 格式化创建时间为 "YYYY-MM-DD HH:MM" 格式
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  };

  return {
    id: apiTask.id,
    taskName: apiTask.task_name,
    type,
    status,
    progress: apiTask.progress || 0,
    inputSource,
    fileInfo,
    createTime: formatDate(apiTask.created_at)
  };
};

const AdvancedFilterPage: React.FC = () => {
  // 原始数据
  const [allTasks, setAllTasks] = useState<TaskData[]>([]);
  
  // 筛选后的数据
  const [filteredTasks, setFilteredTasks] = useState<TaskData[]>([]);
  
  // 当前页码
  const [currentPage, setCurrentPage] = useState(1);

  // 总页数
  const [totalPages, setTotalPages] = useState(1);
  
  // 加载状态
  const [loading, setLoading] = useState(false);
  
  // 默认筛选条件
  const [filterConditions, setFilterConditions] = useState<FilterConditions>({
    status: [],
    taskTypes: [],
    inputSources: [],
    sortField: 'created_at',
    sortDirection: 'desc'
  });

  const navigate = useNavigate();

  // 从API获取任务列表
  const fetchTasks = async (page: number = 1, conditions = filterConditions) => {
    setLoading(true);
    try {
      // 构建查询参数
      const params: Record<string, string | number> = {
        page,
        per_page: 5 // 每页显示5条数据
      };

      // 添加状态筛选
      if (conditions.status.length > 0) {
        // 将多个状态转换为逗号分隔的字符串
        params.status = conditions.status
          .map(status => statusApiMap[status])
          .join(',');
      }

      // 添加任务类型筛选
      if (conditions.taskTypes.length > 0) {
        params.task_type = conditions.taskTypes
          .map(type => taskTypeApiMap[type])
          .join(',');
      }

      // 添加输入源筛选
      if (conditions.inputSources.length > 0) {
        params.input_source = conditions.inputSources
          .map(source => inputSourceApiMap[source])
          .join(',');
      }

      // 添加蜜罐ID筛选
      if (conditions.channelId) {
        params.honeypot_id = parseInt(conditions.channelId);
      }

      // 添加节点ID筛选
      if (conditions.nodeId) {
        params.node_id = parseInt(conditions.nodeId);
      }

      // 添加创建者筛选
      if (conditions.creatorName) {
        params.created_by = conditions.creatorName;
      }

      // 添加排序字段
      params.sort_by = conditions.sortField;

      // 添加排序方向
      params.sort_order = conditions.sortDirection;

      console.log('筛选参数:', params);

      // 发送请求
      const response = await apiClient.get<ApiResponse>('/api/noise-reduction/tasks/', { params });
      
      // 检查响应状态
      if (response.data.code === 200) {
        // 转换API返回的数据格式
        const tasks = response.data.tasks.map(convertApiTaskToTaskData);
        setAllTasks(tasks);
        setFilteredTasks(tasks);
        
        // 更新分页信息
        setCurrentPage(response.data.pagination.current_page);
        setTotalPages(response.data.pagination.pages);
      } else {
        console.error('API返回错误:', response.data.message);
        setAllTasks([]);
        setFilteredTasks([]);
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
      setAllTasks([]);
      setFilteredTasks([]);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取任务列表
  useEffect(() => {
    fetchTasks();
  }, []);

  // 处理筛选条件变化
  const handleFilterChange = (newConditions: FilterConditions) => {
    // 先更新状态
    setFilterConditions(newConditions);
    // 然后立即使用新的条件获取数据，而不是使用状态中的值
    fetchTasks(1, newConditions);
  };

  // 处理页码变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchTasks(page);
  };

  // 处理重置筛选条件
  const handleReset = () => {
    // 重置为空的筛选条件
    const resetConditions = {
      status: [], // 空数组表示不筛选任何状态
      taskTypes: [], // 空数组表示不筛选任何类型
      inputSources: [], // 空数组表示不筛选任何输入源
      channelId: '', // 清空渠道ID
      nodeId: '', // 清空节点ID
      creatorName: '', // 清空创建者
      sortField: 'created_at', // 保持默认排序字段
      sortDirection: 'desc' as 'desc' // 保持默认排序方向
    };
    
    setFilterConditions(resetConditions);
    fetchTasks(1); // 重置到第一页并获取数据
  };

  // 处理任务操作（详情、启动、停止）
  const handleTaskAction = async (id: number, action: 'detail' | 'start' | 'stop') => {
    console.log(`执行任务 ${id} 的 ${action} 操作`);
    if (action === 'detail') {
      navigate(`/noiseAnalysisTask/alarmDetail/${id}`);
    } else if (action === 'start') {
      try {
        await apiClient.post(`/api/noise-reduction/tasks/${id}/execute`);
        console.log(`任务 ${id} 启动成功`);
        // 启动成功后刷新任务列表
        fetchTasks(currentPage);
      } catch (error) {
        console.error(`任务 ${id} 启动失败:`, error);
      }
    }
  };

  return (
    <Box className="advanced-filter-wrapper">
      <Box className="advanced-filter-header">
        <h2>高级筛选</h2>
      </Box>
      <AdvancedFilter 
        conditions={filterConditions} 
        onFilterChange={handleFilterChange} 
        onReset={handleReset}
      />
      
      <Box mt={3}>
        <TaskTable 
          tasks={filteredTasks} 
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          onTaskAction={handleTaskAction}
          loading={loading}
        />
      </Box>
    </Box>
  );
};

export default AdvancedFilterPage; 