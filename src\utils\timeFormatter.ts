/**
 * 时间格式化工具函数
 */

/**
 * 格式化时间戳为中文本地化格式
 * @param timestamp 时间戳字符串或Date对象
 * @param fallback 格式化失败时的回退值，默认为原始值
 * @returns 格式化后的时间字符串
 */
export const formatTimestamp = (timestamp: string | Date, fallback?: string): string => {
  try {
    const date = new Date(timestamp);
    if (!isNaN(date.getTime())) {
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });
    }
    return fallback !== undefined ? fallback : String(timestamp);
  } catch (error) {
    return fallback !== undefined ? fallback : String(timestamp);
  }
};

/**
 * 格式化时间戳为日期格式（不包含时间）
 * @param timestamp 时间戳字符串或Date对象
 * @param fallback 格式化失败时的回退值，默认为原始值
 * @returns 格式化后的日期字符串
 */
export const formatDate = (timestamp: string | Date, fallback?: string): string => {
  try {
    const date = new Date(timestamp);
    if (!isNaN(date.getTime())) {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    }
    return fallback !== undefined ? fallback : String(timestamp);
  } catch (error) {
    return fallback !== undefined ? fallback : String(timestamp);
  }
};

/**
 * 格式化时间戳为时间格式（不包含日期）
 * @param timestamp 时间戳字符串或Date对象
 * @param fallback 格式化失败时的回退值，默认为原始值
 * @returns 格式化后的时间字符串
 */
export const formatTime = (timestamp: string | Date, fallback?: string): string => {
  try {
    const date = new Date(timestamp);
    if (!isNaN(date.getTime())) {
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });
    }
    return fallback !== undefined ? fallback : String(timestamp);
  } catch (error) {
    return fallback !== undefined ? fallback : String(timestamp);
  }
};

/**
 * 格式化相对时间（如：2小时前、3天前等）
 * @param timestamp 时间戳字符串或Date对象
 * @param fallback 格式化失败时的回退值，默认为原始值
 * @returns 格式化后的相对时间字符串
 */
export const formatRelativeTime = (timestamp: string | Date, fallback?: string): string => {
  try {
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) {
      return fallback !== undefined ? fallback : String(timestamp);
    }

    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffSeconds < 60) {
      return '刚刚';
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      // 超过一周显示具体日期
      return formatTimestamp(timestamp, fallback);
    }
  } catch (error) {
    return fallback !== undefined ? fallback : String(timestamp);
  }
};
