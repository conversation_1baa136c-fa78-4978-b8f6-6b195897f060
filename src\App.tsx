import React from 'react'

import { BrowserRouter as Router, Route, Routes } from 'react-router-dom'
import { ThemeProvider } from '@material-ui/core/styles'
import { CssBaseline } from '@material-ui/core'
import MainLayout from './components/Layout/MainLayout'
import theme from './theme'
import HomePage from './pages/home'
import HoneyHome from './pages/honey-home'
import AttackList from './pages/attack-list'
import AttackLogs from './pages/attack-logs'
import HoneyPotsOverview from './pages/honeypots-overview'
import HoneyPotsManage from './pages/honeypots-manage'
import PointsManage from './pages/points-manage'
import HoneypotManagement from './components/HoneypotManagement/HoneypotManagement'
import DataAccessManage from './pages/data-access-manage'
import NoiseModelEngine from './pages/noise-model-engine'
// import ReportGenerate from './pages/report-generate'
// import LogAlertAnalysis from './pages/log-alert-analysis'
import LogAlertAnalysisTask from './pages/loganalyse-task-manage'
// import LogAnalyseModel from './pages/log-analyse-model'
import LogAnalysisModel from './pages/log-analyse-model'
import AlertAnalysisPage from './pages/alert-analysis/AlertAnalysisPage'
import DataSourceSelection from './pages/alert-analysis/DataSourceSelection'
import AlarmDetail from './pages/advanced-filter/component/AlarmDetails'
import AlertRulesManagement from './pages/alter-rules'

import TaskStatusMonitor from './pages/alert-analysis/StatusMonitor'
import AlertMonitor from './pages/alert-monitor'

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <MainLayout>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/honeyHome" element={<HoneyHome />} />
            <Route path="/attackList" element={<AttackList />} />
            <Route path="/attackLogs" element={<AttackLogs />} />
            <Route path="/honeypotsOverview" element={<HoneyPotsOverview />} />
            <Route path="/honeypotsManage" element={<HoneyPotsManage />} />
            <Route path="/pointsManage" element={<PointsManage />} />
            <Route path="/honey" element={<HoneypotManagement />} />
            <Route path="/dataAccessManage" element={<DataAccessManage />} />
            <Route path="/AlertMonitor" element={<AlertMonitor />} />
            <Route path="/noiseModelEngine" element={<NoiseModelEngine />} />
            <Route path="/noiseAnalysisTask/select" element={<DataSourceSelection />} />
            <Route path="/noiseAnalysisTask" element={<AlertAnalysisPage />} />
            <Route path="/noiseAnalysisTask/alarmDetail/:taskId" element={<AlarmDetail />} />
            <Route path="/alertRulesManagement" element={<AlertRulesManagement />} />
            <Route path="/logAnalysisModel" element={<LogAnalysisModel />} />
            {/* <Route path="/logAlertAnalysis" element={<LogAlertAnalysis />} /> */}
            <Route
              path="/logAlertAnalysisTask"
              element={<LogAlertAnalysisTask />}
            />
            {/* <Route path="/alertAnalysis" element={<AlertAnalysisPage />} /> */}
            <Route path="/statusMonitor" element={<TaskStatusMonitor />} />
          </Routes>
        </MainLayout>
      </Router>
    </ThemeProvider>
  )
}

export default App
