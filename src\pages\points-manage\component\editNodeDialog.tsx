import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>A<PERSON>, <PERSON>alog<PERSON>ontent, DialogT<PERSON>le, Grid, MenuItem, TextField } from "@material-ui/core";
import React, { useState, useEffect } from "react";
import styles from "../less/editNodeDialog.module.less";
interface EditNodeDialogProps {
    open: boolean;
    onClose: () => void;
    onSubmit: (data: any) => void;
    initialData: any | null;
}

const defaultForm = {
    node_name: null,
    ip_address: null,
    mac_address: null,
    node_status: "online",
    node_type: null,
    deployment_location: null,
    operating_system: null,
    architecture: null,
    timezone: "Asia/Shanghai",
    subnet_mask: null,
    dns_servers: null,
    cpu: null,
    ram: null,
    department: null,
    deployed_at: null,
};

function parseHardwareConfig(hardware_config: string | null | undefined) {
    if (!hardware_config) return { cpu: null, ram: null };
    try {
        const obj = JSON.parse(hardware_config);
        return {
            cpu: obj.cpu ?? null,
            ram: obj.ram ?? null,
        };
    } catch {
        return { cpu: null, ram: null };
    }
}

export default function EditNodeDialog(props: EditNodeDialogProps) {
    const { open, onClose, onSubmit, initialData } = props;
    const [form, setForm] = useState(defaultForm);

    useEffect(() => {
        if (open && initialData) {
            console.log(initialData);
            setForm({
                node_name: initialData.node_info?.node_name ?? null,
                ip_address: initialData.host_info?.ip_address ?? null,
                mac_address: initialData.host_info?.mac_address ?? null,
                node_status: initialData.node_info?.node_status ?? "online",
                node_type: initialData.node_info?.node_type ?? null,
                deployment_location: initialData.node_info?.deployment_location ?? null,
                operating_system: initialData.host_info?.operating_system ?? null,
                architecture: initialData.host_info?.architecture ?? null,
                timezone: initialData.host_info?.timezone ?? "Asia/Shanghai",
                subnet_mask: initialData.host_info?.subnet_mask ?? null,
                dns_servers: initialData.host_info?.dns_servers ?? null,
                ...parseHardwareConfig(initialData.host_info?.hardware_config),
                department: initialData.node_info.department ?? null,
                deployed_at: initialData.node_info?.deployed_at ?? null,
            });
        }
        if (!open) setForm(defaultForm);
    }, [open, initialData]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setForm((prev) => ({ ...prev, [name]: value === "" ? null : value }));
    };

    const handleConfirm = () => {
        let submitData = {
            ...form,
            hardware_config: JSON.stringify({ cpu: form.cpu, ram: form.ram }),
            id: initialData?.node_info?.id,
        };
        onSubmit(submitData);
    };

    return (
        <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
            <DialogTitle>编辑节点</DialogTitle>
            <DialogContent>
                <Grid container spacing={2}>
                    <Grid item xs={6}>
                        <TextField label="节点名称" name="node_name" value={form.node_name} onChange={handleChange} margin="dense" fullWidth />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="IP地址" name="ip_address" value={form.ip_address} onChange={handleChange} margin="dense" fullWidth />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="MAC地址" name="mac_address" value={form.mac_address} onChange={handleChange} margin="dense" fullWidth />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField
                            select
                            label="节点状态"
                            name="node_status"
                            value={form.node_status}
                            onChange={handleChange}
                            margin="dense"
                            fullWidth
                        >
                            <MenuItem value="online">在线</MenuItem>
                            <MenuItem value="offline">离线</MenuItem>
                        </TextField>
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="节点类型" name="node_type" value={form.node_type} onChange={handleChange} margin="dense" fullWidth />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="部署位置" name="deployment_location" value={form.deployment_location} onChange={handleChange} fullWidth margin="dense" />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField
                            label="部署时间"
                            name="deployed_at"
                            type="datetime-local"
                            value={form.deployed_at}
                            onChange={handleChange}
                            margin="dense"
                            fullWidth
                            InputLabelProps={{
                                shrink: true,
                            }}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="操作系统" name="operating_system" value={form.operating_system} onChange={handleChange} fullWidth margin="dense" />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="架构" name="architecture" value={form.architecture} onChange={handleChange} fullWidth margin="dense" />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="时区" name="timezone" value={form.timezone} onChange={handleChange} margin="dense" fullWidth placeholder="如 Asia/Shanghai" />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="子网掩码" name="subnet_mask" value={form.subnet_mask} onChange={handleChange} margin="dense" fullWidth placeholder="如 255.255.255.0"/>
                    </Grid>
                    <Grid item xs={6}>
                        <TextField
                            label="DNS服务器(逗号分隔)"
                            name="dns_servers"
                            value={form.dns_servers}
                            onChange={handleChange}
                            margin="dense"
                            fullWidth
                            placeholder='如 ["8.8.8.8","1.1.1.1"]'
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField
                            label="CPU核心数"
                            name="cpu"
                            type="number"
                            value={form.cpu}
                            onChange={handleChange}
                            margin="dense"
                            placeholder="如 4"
                            fullWidth
                            inputProps={{ min: 1, step: 1 }}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField
                            label="内存大小(GB)"
                            name="ram"
                            type="number"
                            value={form.ram}
                            onChange={handleChange}
                            margin="dense"
                            placeholder="如 1.5"
                            fullWidth
                            inputProps={{ min: 0.1, step: 0.1 }}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="所属部门" name="department" value={form.department} onChange={handleChange} fullWidth margin="dense" />
                    </Grid>
                </Grid>
            </DialogContent>
            <DialogActions>
                <Button className={styles.noFocus} onClick={onClose}>取消</Button>
                <Button className={styles.noFocus} onClick={handleConfirm} variant="contained" color="primary">
                    确认
                </Button>
            </DialogActions>
        </Dialog>
    );
}
