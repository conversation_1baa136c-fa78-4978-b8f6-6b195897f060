import React, { useEffect, useState } from 'react';
import { Grid, Paper, Typography, Box } from '@material-ui/core';
import ReactEcharts from 'echarts-for-react';

interface Props {
  honeypotCount: number;
  rollingIPs: string[];
  complianceOption: any;
  riskTrendOption: any;
  classes: any;
}

const IP_ROW_HEIGHT = 24;    // 单行高度
const VISIBLE_ROWS = 3;      // 可见行数

const DashboardCards: React.FC<Props> = ({
  honeypotCount,
  rollingIPs,
  complianceOption,
  riskTrendOption,
  classes
}) => {
    const IP_ROW_HEIGHT = 24;
const VISIBLE_ROWS = 3;
  // 滚动动画索引
  const [rollingIndex, setRollingIndex] = useState(0);

  // 用于循环滚动的完整IP列表
  const ipList = rollingIPs.length > 0 ? rollingIPs : [];

  // 定时滚动
  useEffect(() => {
    if (ipList.length <= VISIBLE_ROWS) return; // 不足行数不滚动
    const timer = setInterval(() => {
      setRollingIndex(idx => (idx + 1) % ipList.length);
    }, 2000);
    return () => clearInterval(timer);
  }, [ipList.length]);

  // 拼接多几行以实现无缝滚动
  const displayList = ipList.concat(ipList.slice(0, VISIBLE_ROWS));

  return (
    <Grid container spacing={2} >
      {/* 今日告警总数 */}
      <Grid item xs={12} md={3}>
         <Paper className={`${classes.card} ${classes.cardHover}`}>
          <div className={classes.metricHeader}>
            <Typography variant="subtitle1">今日告警总数</Typography>
            <span className={`${classes.detailLabel} ${classes.high}`}>↑12%</span>
          </div>
          <div className={classes.metricValue}>1,248</div>
          <Grid container>
            <Grid item xs={4}>
              <div className={`${classes.detailLabel} ${classes.high}`}>高危</div>
              <Typography align="center">86</Typography>
            </Grid>
            <Grid item xs={4}>
              <div className={`${classes.detailLabel} ${classes.medium}`}>中危</div>
              <Typography align="center">342</Typography>
            </Grid>
            <Grid item xs={4}>
              <div className={`${classes.detailLabel} ${classes.low}`}>低危</div>
              <Typography align="center">820</Typography>
            </Grid>
          </Grid>
        </Paper>
      </Grid>

      {/* 蜜罐诱捕攻击（带滚动动画） */}
      <Grid item xs={12} md={3}>
        <Paper className={`${classes.card} ${classes.cardHover}`}>
          <div className={classes.metricHeader}>
            <Typography variant="subtitle1">蜜罐诱捕攻击</Typography>
            <span className={`${classes.detailLabel} ${classes.high}`}>↑8%</span>
          </div>
          <div className={classes.metricValue}>{honeypotCount.toLocaleString()}</div>
          <Typography variant="body2" color="textSecondary">实时攻击次数</Typography>
         <Box mt={2} className={classes.rollingIpBox}>
  <div
    className={classes.rollingIpList}
    style={{
      transform: `translateY(-${rollingIndex * IP_ROW_HEIGHT}px)`
    }}
  >
    {displayList.map((ip, i) => (
      <Typography
        key={i}
        variant="body2"
        style={{
          height: IP_ROW_HEIGHT,
          lineHeight: `${IP_ROW_HEIGHT}px`,
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis'
        }}
      >
        {ip}
      </Typography>
    ))}
  </div>
</Box>

        </Paper>
      </Grid>

      {/* 资产配置合规率 */}
      <Grid item xs={12} md={3}>
        <Paper className={`${classes.card} ${classes.cardHover}`}>
          <div className={classes.metricHeader}>
            <Typography variant="subtitle1">资产配置合规率</Typography>
            <span className={`${classes.detailLabel} ${classes.low}`}>↓2%</span>
          </div>
          <Box style={{ height: 150 }}>
            <ReactEcharts option={complianceOption} style={{ height: 180 }} />
          </Box>
          <Typography variant="body2" color="textSecondary">基线标准: 95%</Typography>
        </Paper>
      </Grid>

      {/* 风险评分趋势 */}
      <Grid item xs={12} md={3}>
       <Paper className={`${classes.card} ${classes.cardHover}`}>
  <div className={classes.metricHeader}>
    <Typography variant="subtitle1">资产配置合规率</Typography>
  </div>
  <Grid container>
    <Grid item xs={6}>
      <Typography variant="body2" align="center" color="textSecondary">今日</Typography>
      <Typography align="center" className={classes.scoreRed} style={{ fontSize: 32, fontWeight: 700 }}>78</Typography>
    </Grid>
    <Grid item xs={6}>
      <Typography variant="body2" align="center" color="textSecondary">昨日</Typography>
      <Typography align="center" className={classes.scoreOrange} style={{ fontSize: 32, fontWeight: 700 }}>65</Typography>
    </Grid>
  </Grid>
  <Box style={{ height: 60, marginTop: 8 }}>
    <ReactEcharts option={riskTrendOption} style={{ height: 60 }} />
  </Box>
</Paper>

      </Grid>
    </Grid>
  );
};

export default DashboardCards;
