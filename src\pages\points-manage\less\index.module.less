.tableHead {
    background-color: #e4e5e8;

    // 注意：MUI TableHead 里 th 需要用 :global
    :global(th) {
        color: #292525;
        border-top-left-radius: 0;
        border-top-right-radius: 0;
        font-weight: bold;
        text-align: center;
        height: 40px;
        padding: 0;
    }
}

.tableBox {
    padding: 10px 10px;
}

.myTable {
    border: 1px solid #d7d5d5;
}

.tableBody {
    :global(td) {
        text-align: center;
    }
}

.tableCell {
    text-align: center;
    background-color: #f9fafc;
    height: 55px;
    padding: 0;
}

.rowHover {
    :global(td) {
        transition: background 0.2s;
    }

    &:hover :global(td) {
        background-color: #c9c9ca !important;
        cursor: pointer;
    }
}

.rowExpanded {
    :global(td:first-child) {
        background-color: #f6f1f2;
        position: relative;
        /* 使伪元素相对于这个单元格定位 */
    }

    :global(td:first-child)::before {
        content: '';
        position: absolute;
        left: -1px;
        top: 0;
        bottom: 0;
        width: 2px;
        /* 边框宽度 */
        background-color: orange;
        /* 边框颜色 */
    }

    :global(td) {
        background-color: #f6f1f2;
    }
}

.statusOnline {
    color: green;
    font-weight: 500;
}

.statusOffline {
    color: red;
    font-weight: 500;
}

.editBtn {
    color: #1890ff;
    cursor: pointer;
    margin-right: 16px;

    &:hover {
        text-decoration: underline;
    }
}

.actionBtn {
    color: #1890ff;
    cursor: pointer;
    margin-right: 16px;

    &:hover {
        text-decoration: underline;
    }
}

.deleteBtn {
    color: red;
    cursor: pointer;

    &:hover {
        text-decoration: underline;
    }
}

.toolBar {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 15px;
    padding: 0px 0px 10px 10px;
}


.container {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 15px;
    min-height: 0;
}

.search_card {
    // flex: 0 0 12vh;
    flex-shrink: 0;
    margin-bottom: 10px;
    border-radius: 5px;
    border: 1px solid #d7d5d5;
    background-color: white;
    padding: 10px 10px;
    box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.table_card {
    width: 100%;
    flex: 1 1 0;
    min-width: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid #d7d5d5;
    background-color: #fff;
    border-radius: 5px;
}

.tableContainer {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.tableContainer {
    height: 100%;
    display: flex;
    justify-content: space-between;
}

.tableBox {
    flex: 1 1 0;
    min-height: 0;
    overflow-y: auto;
}

.customButton > span {
    align-items: center;
}

.search_select_bar {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
}

.search_select_bar_left {
    display: flex;
}

.search_select_bar_left_item {
    display: flex;
    align-items: center;
}

.search_select_bar_right {
    display: flex;
    gap: 10px;
}

.search_select_reload_button {
    border: 1px solid #c4c4c4 !important;
    padding: 4px 15px !important;
}

.search_input {
    height: 40px;
    width: 100%;
    // outline: none !important;
    border: 1px solid #cfcccc !important;
    border-radius: 5px;
    align-items: center;
    padding: 0px 8px;
}

.search_input::after {
    border: none !important;
}

.search_input::before {
    border: none !important;
}

.selectEmpty::after {
    border: none !important;
}

.selectEmpty::before {
    border: none !important;
}

.selectEmpty:active {
    background-color: none !important;
}

.selectEmpty option {
    align-items: center;
    text-align: center;
}


.noFocus:focus {
    outline: none !important;
    box-shadow: none !important;
}

.attack_list_pagination {
  height: 6vh;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.attack_list_pagination_div {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 15px;
}
