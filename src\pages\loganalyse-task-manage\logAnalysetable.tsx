// src/pages/HoneyPotTable.tsx
import React from 'react';
import {
  Table, TableBody, TableContainer, TableHead, TableRow,
  Paper, makeStyles, withStyles, TableCell,
  CircularProgress, LinearProgress, Button
} from '@material-ui/core';
import Pagination from '@material-ui/lab/Pagination';

const useStyles = makeStyles((theme) => ({
  toolbar: {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: theme.spacing(2),
  },
  progressWrapper: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    minWidth: 120, // 添加最小宽度确保一致性
  },
  statusLabel: {
    padding: '2px 8px',
    borderRadius: 4,
    color: '#fff',
    fontSize: 12,
    display: 'inline-block',
  },
  // 新增进度条容器样式
  progressContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: 4,
    minWidth: 100, // 确保容器有固定宽度
  },
  // 新增进度条样式
  progressBar: {
    width: 80, // 统一宽度，比之前的60px稍大
    height: 8,
    borderRadius: 4,
    backgroundColor: '#f0f0f0', // 背景色
  },
  // 新增百分比文字样式
  progressText: {
    fontSize: 12,
    color: '#666',
    fontWeight: 500,
    minWidth: 35, // 确保文字容器有固定宽度
    textAlign: 'center',
  },
}));
type ConfigType = {
  id: number;
  config_name: string;
  config_type: string;
};

type FormDataType = {
  id: number;
  task_name: string;
  status: string;
  config_id: number;
  progress: number;
  config_name?: string;
  threats_detected?: number;
  config?: ConfigType;
  created_at: string;
  updated_at: string;
};

interface LogAnalyseTableProps {
  data: FormDataType[];
  page: number;
  pageSize: number;
  total: number;
  loading?: boolean;
  onPageChange: (page: number) => void;
  onRowsPerPageChange: (size: number) => void;
  onEdit: (rowData: FormDataType) => void;
  onAction: (rowData: FormDataType) => void; 
}

export default function LogAnalyseTable(props: LogAnalyseTableProps) {
  const {
    data = [],
    page,
    pageSize,
    total,
    onPageChange,
    onRowsPerPageChange,
    onEdit,
    onAction,
    loading
  } = props;

  const classes = useStyles();

  const renderCell = (value: any) => {
    return value !== null && value !== undefined && value !== '' ? value : '/';
  };

  const StyledTableRow = withStyles(() => ({
    root: {
      height: 52,
      '&:nth-of-type(odd)': {
        backgroundColor: '#f9f9f9',
      },
    },
  }))(TableRow);

  const StyledTableCell = withStyles(() => ({
    root: {
      paddingTop: 6,
      paddingBottom: 6,
      lineHeight: '1.4rem',
      textAlign: 'center',
    },
    head: {
      backgroundColor: '#babcbb',
      color: '#fff',
      textAlign: 'center',
    },
  }))(TableCell);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return '#4caf50'; // green
      case 'running':
        return '#2196f3'; // blue
      case 'pending':
        return '#ff9800'; // orange
      default:
        return '#9e9e9e'; // grey
    }
  };

  const renderStatus = (status: string) => {
    const textMap: Record<string, string> = {
      completed: '已完成',
      running: '运行中',
      pending: '待处理',
      failed:'已失败',
      cancelled: '已取消',
    };
    return (
      <span
        className={classes.statusLabel}
        style={{ backgroundColor: getStatusColor(status) }}
      >
        {textMap[status] || status}
      </span>
    );
  };

  const formatDate = (isoString: string) => {
    if (!isoString) return '-';
    const date = new Date(isoString);
    return date.toLocaleString('zh-CN', { hour12: false });
  };

  return (
    <TableContainer
      component={Paper}
      style={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        minHeight: 600,
      }}
    >
      {loading ? (
        <div
          style={{
            flex: 1,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: 300,
          }}
        >
          <CircularProgress size={48} />
        </div>
      ) : (
        <>
          <div style={{ flex: 1, overflowY: 'auto', overflowX: 'hidden' }}>
            <Table >
              <TableHead>
                <StyledTableRow>
                  <StyledTableCell>ID</StyledTableCell>
                  <StyledTableCell>任务名称</StyledTableCell>
                  <StyledTableCell>状态</StyledTableCell>
                  <StyledTableCell>进度</StyledTableCell>
                  <StyledTableCell>检测到成功次数</StyledTableCell>
                  <StyledTableCell>使用配置</StyledTableCell>
                  <StyledTableCell>创建时间</StyledTableCell>
                  <StyledTableCell>操作</StyledTableCell>
                </StyledTableRow>
              </TableHead>
              <TableBody>
                {data.map((row, index) => (
              <StyledTableRow key={row.id}>
                <StyledTableCell>{page * pageSize + index + 1}</StyledTableCell>

                    <StyledTableCell>{renderCell(row.task_name)}</StyledTableCell>
                    <StyledTableCell>{renderStatus(row.status)}</StyledTableCell>
                    <StyledTableCell>
                      <div className={classes.progressWrapper}>
                        <div style={{ width: 80 }}>
                          <LinearProgress
                            variant="determinate"
                            value={row.progress || 0}
                            style={{ 
                              width: '100%', 
                              height: 8, 
                              borderRadius: 4 
                            }}
                          />
                        </div>
                        <span style={{ 
                          minWidth: 35, 
                          textAlign: 'center',
                          fontSize: 12,
                          color: '#666'
                        }}>
                          {row.progress || 0}%
                        </span>
                      </div>
                    </StyledTableCell>
                    <StyledTableCell>{row.threats_detected ?? 0}</StyledTableCell>
                   <StyledTableCell>{row.config_name || '-'}</StyledTableCell>

                    <StyledTableCell>{formatDate(row.created_at)}</StyledTableCell>
                    <StyledTableCell>
                      <Button size="small" variant="outlined" onClick={() => onEdit(row)}>详情</Button>
                     <Button
                        size="small"
                        variant="contained"
                        color={
                            row.status === 'running' ? 'secondary' : 
                            row.status === 'completed' ? 'default' : 'primary'
                          }
                          style={{ 
                            marginLeft: 8,
                            backgroundColor: row.status === 'completed' ? '#e0e0e0' : undefined,
                            color: row.status === 'completed' ? '#9e9e9e' : undefined,
                          }}
                          onClick={() => onAction(row)}
                          disabled={row.status === 'completed'}
                        >
                        {row.status === 'running' ? '取消' : '启动'}
                      </Button>

                    </StyledTableCell>
                  </StyledTableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* 分页区域 */}
          <div className="attack-list-pagination-div">
            <div>共 {total} 条数据</div>
            <Pagination
              className="attack-list-pagination"
              showFirstButton
              count={Math.ceil(total / pageSize)}
              page={page + 1}
              onChange={(event, value) => onPageChange(value - 1)}
            />
          </div>
        </>
      )}
    </TableContainer>
  );
}
