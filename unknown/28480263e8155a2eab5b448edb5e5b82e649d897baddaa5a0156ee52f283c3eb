import { createTheme } from '@material-ui/core/styles'

const defaultTheme = createTheme({
    palette: {
        primary: {
            main: '#4caf50', // 环保绿色
            light: '#80e27e',
            dark: '#087f23',
            contrastText: '#fff',
        },
        secondary: {
            main: '#81c784',
            light: '#b2fab4',
            dark: '#519657',
            contrastText: '#000',
        },
        background: {
            default: '#f5f5f5',
            paper: '#fff',
        },
    },
    typography: {
        fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
        h1: {
            fontWeight: 500,
        },
        h2: {
            fontWeight: 500,
        },
        h3: {
            fontWeight: 500,
        },
    },
    overrides: {
        MuiDrawer: {
            paper: {
                backgroundColor: '#fff',
            },
        },
        MuiButton: {
            root: {
                textTransform: 'none',
                borderRadius: 4,
            },
        },
    },
});

export default defaultTheme;