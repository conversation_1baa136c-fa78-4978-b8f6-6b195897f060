import React, { useEffect, useRef, useState } from 'react'
import { <PERSON>read<PERSON><PERSON>bs, Button, Fab, Input, Link, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography, withStyles } from '@material-ui/core';
import * as echarts from 'echarts';
import FormatIndentIncreaseIcon from '@material-ui/icons/FormatIndentIncrease';
import SearchIcon from '@material-ui/icons/Search';
import { Pagination, PaginationItem } from '@material-ui/lab';
import AddLogSource from './addLogSource';
import AddLogFile from './addLogFile';
import { row1, rows } from './mock';
import LogSourceTable from './logSourceTable';
import LogFileTable from './logFileTable';
export default function DataAccessManage() {
  const [selectedPeriod, setSelectedPeriod] = useState<'实时'|'24小时'|'七天'>('实时');
  const periods: Array<'实时'|'24小时'|'七天'> = ['实时', '24小时', '七天'];
  const chartRef = useRef<HTMLDivElement | null>(null);//创建一个 ref，用来挂载 ECharts 容器
  const [page, setPage] = useState(1); 
  const [activeTable, setActiveTable] = useState<'source' | 'file'>('source');
  const rowsPerPage = 5;

  const xData = (() => {
  if (selectedPeriod === '实时') {
    return Array.from({ length: 13 }, (_, i) => {
      const h = i * 2;
      return (h < 10 ? '0' + h : h) + ':00';
    });
  }
  if (selectedPeriod === '24小时') {
    return Array.from({ length: 25 }, (_, i) => {
      const h = i;
      return (h < 10 ? '0' + h : h) + ':00';
    });
  }
  // 七天
  return Array.from({ length: 7 }, (_, i) => `Day ${i + 1}`);
  })();
  useEffect(() => {
    if (!chartRef.current) return; //确保 ref.current 已经指向了真实 DOM
    const myChart = echarts.init(chartRef.current);//初始化图表
    myChart.setOption({ //设置一个简单示例配置
    grid: {left:'2%',right:'2%',top:'12%',bottom:'8%',containLabel: true},
    xAxis: {
    type: 'category',
    boundaryGap: false,
    data: xData
   },
    yAxis: {type: 'value',min: 0,max: 250,interval: 50},
    series: [{
      name: '防火墙日志',type: 'line',smooth: true,lineStyle: {width: 4,color: '#4E79A7'},itemStyle: {color: '#4E79A7'},
      data: [22, 111, 60, 44, 100, 55, 140, 22, 180, 200, 22, 21, 250,2, 50, 4, 90, 2, 250, 3, 4, 250, 210, 250, 2,],
      areaStyle: {color: '#B4C6DA'}
    },
    {
      name: 'IDS日志',type: 'line',smooth: true,lineStyle: {width: 4,color: '#F28E2B'},itemStyle: {color: '#F28E2B'},
      data: [2, 50, 4, 90, 2, 250, 3, 4, 250, 210, 250, 2, 1,10, 30, 50, 70, 250, 10, 130, 150, 170, 2, 210, 230, 240],
      areaStyle: {color: '#F9CFA5'}
    },
    {
      name: '服务器日志',type: 'line',smooth: true,lineStyle: {width: 4,color: '#59A14F'},itemStyle: {color: '#59A14F'},
      data: [10, 30, 50, 70, 250, 10, 130, 150, 170, 2, 210, 230, 240,22, 111, 60, 44, 100, 55, 140, 22, 180, 200, 22, 21, 250],
      areaStyle: {color: '#B9D7B5'}
    }]
    });
    //清理：组件卸载时销毁图表实例
    const handleResize = () => myChart.resize();
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      myChart.dispose();
    };
  }, [selectedPeriod]);

  return (
    <div style={{ width: '100%', background: 'white',height: '100%'}}>
    <div style={{ width: '100%', background: 'white'}}>

       <div style={{display: 'flex',alignItems: 'center',paddingTop: 20,paddingLeft: 20,color: '#397B44'}}>
         <FormatIndentIncreaseIcon style={{ fontSize: 40, marginRight: 8 }}/>
         <Typography variant="h4" style={{ fontWeight: 'bold' }}>数据接入管理</Typography>
       </div>

       <Breadcrumbs aria-label="breadcrumb" separator=">" style={{paddingLeft: '68px',paddingTop: '10px'}}>
         <Link color="inherit"  >数据管理</Link><Typography color="inherit">数据接入管理</Typography>
       </Breadcrumbs>

       <div style={{border: '1px solid #E0E0E0',boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',margin: '20px',borderRadius: '6px'}}>
          <div style={{display: 'flex',justifyContent: 'space-between',padding: '20px',borderBottom: '1px solid #E0E0E0',alignItems: 'center'}}>
            <div style={{fontSize: '25px',fontWeight: 'bold',color: '#397B44'}}>实时流量监控</div>
            <div>
                {periods.map(period => (
                  <Button
                    key={period}
                    onClick={() => setSelectedPeriod(period)}
                    style={{marginLeft: 10,color: selectedPeriod === period ? '#397B44' : '#888',border: `1px solid ${selectedPeriod === period ? '#397B44' : '#CCC'}`,borderRadius: 6}}
                  >
                    {period}
                  </Button>
                ))}
            </div>
          </div>
         <div style={{display: 'flex',justifyContent: 'center',alignItems: 'center',marginTop: 20,fontSize: '18px'}}>
            <span style={{backgroundColor: '#B4C6DA',margin:'0px 10px',padding: '10px 30px',border: '5px solid #4E79A7'}}/>防火墙日志
            <span style={{backgroundColor: '#F9CFA5',margin:'0px 10px',padding: '10px 30px',border: '5px solid #F28E2B'}}/>IDS日志
            <span style={{backgroundColor: '#B9D7B5',margin:'0px 10px',padding: '10px 30px',border: '5px solid #59A14F'}}/>服务器日志
         </div>
         <div ref={chartRef} style={{width: '100%',  height: '400px'}}/>
       </div>

       <div style={{margin: 20,padding: '20px',borderRadius: '6px'}}>
         <div style={{display: 'flex',justifyContent: 'space-between',alignItems: 'center',marginBottom: 20}}>
            <div style={{fontSize: '25px',fontWeight: 'bold',color: '#397B44'}}>日志源管理</div>
            <div>
              <Input placeholder="搜索日志源" style={{marginRight: 10,border: '1px solid #E0E0E0',borderRadius: 6,padding: '6px 12px',fontSize: '16px'}}/>
              <Button  startIcon={<SearchIcon />} style={{backgroundColor: '#E0E0E0',color: '#397B44',borderRadius: 6,fontSize: '20px',marginRight: 20}}>搜索</Button>
              <AddLogSource/>
              <AddLogFile/>
              
            </div>
         </div>
         <div style={{marginBottom: 20,paddingBottom: 20}}>
              <Button style={{color: '#4CAF50',border: '1px solid #E0E0E0',marginRight: 20,fontSize: '20px'}} onClick={() => {setActiveTable('source'), setPage(1)}} variant={activeTable === 'source' ? 'contained' : 'outlined'}>日志源</Button>
              <Button style={{color: '#4CAF50',border: '1px solid #E0E0E0',marginRight: 20,fontSize: '20px'}}  onClick={() => {setActiveTable('file'), setPage(1) }} variant={activeTable === 'file' ? 'contained' : 'outlined'}>日志文件</Button>
              {activeTable === 'source' && (<LogSourceTable page={page} rowsPerPage={rowsPerPage} />)}
              {activeTable === 'file' && (<LogFileTable page={page} rowsPerPage={rowsPerPage} />)}
              <div style={{display: 'flex',justifyContent: 'space-between',alignItems: 'center',marginTop: 20,marginBottom: 40}}>
                 <Pagination count={activeTable === 'file' ? Math.ceil(row1.length / rowsPerPage) : Math.ceil(rows.length / rowsPerPage)} page={page} onChange={(_, value) => setPage(value)} style={{marginTop: 20}} variant="outlined" shape="rounded" renderItem={(item) => (
                    <PaginationItem {...item} style={ item.selected ? { color: 'white', backgroundColor: '#4CAF50',borderColor: '#4CAF50' } : undefined } />)}/>
                 <div style={{fontSize: '18px'}}>显示{page * rowsPerPage -rowsPerPage+ 1}-{page * rowsPerPage}条 , 共{activeTable === 'file' ? row1.length : rows.length}条</div>
              </div>
         </div>
       </div>
    </div>
    </div>
  )
}