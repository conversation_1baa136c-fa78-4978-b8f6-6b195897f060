import { Button, <PERSON>alog, <PERSON>alogA<PERSON>, DialogContent, DialogTitle, TextField } from "@material-ui/core";
import { NodeItem } from "../interface/interface"
import styles from "../less/expendCard.module.less"
import { useEffect, useState } from "react";
import AddHoneypotDialog from "./addHoneypotDialog";
import { showSnackbar } from "./myMessageBar";
import apiClient from "../../../pages/apis/apiClient";
interface props {
    nodeItem: NodeItem;
    honeypots: any[];
    honeypotLoading: boolean;
    handleGetData: Function;
    refreshHoneypots: () => void;
}


type EditField =
    | { field: "node_name", label: string, value: string }
    | { field: "deployment_location", label: string, value: string }
export default function ExpandCard({ nodeItem, handleGetData, honeypotLoading, honeypots, refreshHoneypots }: props) {
    // 控制模态框显示与否
    const [open, setOpen] = useState(false)
    // 当前编辑的字段
    const [editField, setEditField] = useState<EditField | null>(null)
    // 输入框内容
    const [editValue, setEditValue] = useState("")

    useEffect(() => {
    }, [])
    // 打开模态框
    const handleEditClick = (field: EditField) => {
        setEditField(field)
        setEditValue(field.value)
        setOpen(true)
    }

    // 关闭模态框
    const handleClose = () => {
        setOpen(false)
        setEditField(null)
        setEditValue("")
    }

    // 提交（这里仅演示，实际需调用接口或父组件回调）
    const handleSubmit = (formData: any) => {
        try {
            // 合并数据
            let payload: any = {
                ...nodeItem.node_info,
                ...nodeItem.host_info,
                [editField!.field]: editValue,
                node_id: nodeItem.node_info.id
            };

            // 删除 updated_at 和 deployed_at 字段
            if (payload.updated_at === "" || payload.updated_at === null) delete payload.updated_at;
            if (payload.deployed_at === "" || payload.deployed_at === null) delete payload.deployed_at;
            // 这里替换为你的真实接口地址
            apiClient.post('/api/update_node', {
                ...payload
            }).then(res => {
                if (res.data.success) {
                    showSnackbar("节点更新成功！", "success");
                    handleGetData();
                    handleClose();
                } else {
                    showSnackbar("节点更新失败！", "error");
                }
            }, err => {
                showSnackbar(err?.response?.data?.message || err?.message || "网络错误！", "error");
            });
            // TODO: 刷新节点列表
        } catch (e) {
            alert('节点更新失败');
        }
    };

    // ...原有状态
    const [addDialogOpen, setAddDialogOpen] = useState(false);

    // 提交到后端
    const handleAddHoneypot = (data: any) => {
        apiClient.post("/api/create_honeypot", data, {
            headers: { "Content-Type": "application/json" }
        })
            .then(res => {
                const result = res.data;
                if (result.success) {
                    showSnackbar("添加成功！", "success");
                    setAddDialogOpen(false);
                    refreshHoneypots();
                } else {
                    showSnackbar(result.message || "添加失败！", "error");
                }
            })
            .catch(error => {
                showSnackbar(error?.response?.data?.message || error?.message || "网络错误！", "error");
            });
    };

    function renderCell(value: any) {
        return value && value.toString().trim().length > 0 ? value : '－';
    }
    function formatDateTime(isoStr: any) {
        if (isoStr === null || isoStr.trim() === "") return null;
        if (!isoStr) return null;
        const date = new Date(isoStr);
        if (isNaN(date.getTime())) return isoStr; // 解析失败，返回原字符串

        const pad = (n: number) => n.toString().padStart(2, '0');
        return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} `
            + `${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
    }
    return (
        <div className={styles.expend_card}>
            <div className={styles.point_info}>
                <div className={styles.message_title}>
                    <div>
                        节点信息
                    </div>
                </div>
                <div style={{ marginLeft: "20px" }}>
                    <div className={styles.message_item}>
                        <div>
                            节点名称：
                        </div>
                        <div className={styles.have_edit}>
                            {renderCell(nodeItem.node_info.node_name)}
                            {/* <div>
                                <EditIcon className={styles.edit_icon}
                                    onClick={() => handleEditClick({
                                        field: "node_name",
                                        label: "节点名称",
                                        value: nodeItem.node_info.node_name
                                    })}
                                />
                            </div> */}
                        </div>
                    </div>
                    <div className={styles.message_item}>
                        <div>
                            节点状态：
                        </div>
                        {
                            renderCell(nodeItem.node_info.node_status) === "－" ?
                                "－" :
                                (<div>
                                    {nodeItem.node_info.node_status === "online" ? "在线" : "离线"}
                                </div>)
                        }

                    </div>
                    <div className={styles.message_item}>
                        <div>
                            创建时间：
                        </div>
                        <div>
                            {renderCell(formatDateTime(nodeItem.node_info.created_at))}
                        </div>
                    </div>
                    <div className={styles.message_item}>
                        <div>
                            更新时间：
                        </div>
                        <div>
                            {renderCell(formatDateTime(nodeItem.node_info.updated_at))}
                        </div>
                    </div>
                    <div className={styles.message_item}>
                        <div>
                            部署时间：
                        </div>
                        <div>
                            {renderCell(formatDateTime(nodeItem.node_info.deployed_at))}
                        </div>
                    </div>
                    <div className={styles.message_item}>
                        <div>
                            部署位置：
                        </div>
                        <div className={styles.have_edit}>
                            {renderCell(nodeItem.node_info.deployment_location)}
                            <div>
                                {/* <EditIcon className={styles.edit_icon}
                                    onClick={() => handleEditClick({
                                        field: "deployment_location",
                                        label: "部署位置",
                                        value: nodeItem.node_info.deployment_location
                                    })}
                                /> */}
                            </div>
                        </div>
                    </div>
                    <div className={styles.message_item}>
                        <div>
                            所属部门：
                        </div>
                        <div className={styles.have_edit}>
                            {renderCell(nodeItem.node_info.department)}
                        </div>
                    </div>
                </div>
            </div>
            <div className={styles.host_info}>
                <div className={styles.message_title}>
                    <div>
                        宿主机信息
                    </div>
                    {/* <div>
                    </div> */}
                </div>
                <div style={{ marginLeft: "20px" }}>
                    <div className={styles.message_item}>
                        <div>
                            操作系统：
                        </div>
                        <div>
                            {renderCell(nodeItem.host_info.operating_system)}
                        </div>
                    </div>
                    <div className={styles.message_item}>
                        <div>
                            指令架构：
                        </div>
                        <div>
                            {renderCell(nodeItem.host_info.architecture)}
                        </div>
                    </div>
                    <div className={styles.message_item}>
                        <div>
                            时区设定：
                        </div>
                        <div>
                            {renderCell(nodeItem.host_info.timezone)}
                        </div>
                    </div>
                    <div className={styles.message_item}>
                        <div>
                            &nbsp;&nbsp;&nbsp;&nbsp;IP地址：
                        </div>
                        <div>
                            {renderCell(nodeItem.host_info.ip_address)}
                        </div>
                    </div>
                    <div className={styles.message_item}>
                        <div>
                            掩码地址：
                        </div>
                        <div>
                            {renderCell(nodeItem.host_info.dns_servers)}
                        </div>
                    </div>
                    <div className={styles.message_item}>
                        <div>
                            DNS地址：
                        </div>
                        <div>
                            {renderCell(nodeItem.host_info.dns_servers)}
                        </div>
                    </div>
                    <div className={styles.message_item}>
                        <div>
                            MAC地址：
                        </div>
                        <div>
                            {renderCell(nodeItem.host_info.mac_address)}
                        </div>
                    </div>
                    <div className={styles.message_item}>
                        <div>
                            硬件配置：
                        </div>
                        <div>
                            {renderCell(nodeItem.host_info.hardware_config)}
                        </div>
                    </div>
                </div>
            </div>
            <div className={styles.honeypot_services_info}>
                <div className={styles.message_title}>
                    <div>
                        蜜罐服务器信息
                    </div>
                    {/* <div>
                    </div> */}
                </div>
                <div style={{ marginLeft: "20px" }}>
                    <div className={styles.message_item}>
                        <div>
                            服务状态：
                        </div>

                        <div className={styles.honeypo_services_status}>
                            {honeypotLoading ? (
                                <div>加载中...</div>
                            ) : (
                                honeypots && honeypots.length > 0 ? (
                                    honeypots.map(hp => (
                                        <div className={styles.honeypo_services_status_item} key={hp.id}>
                                            <div>{renderCell(hp.honeypot_name)}</div>
                                            <div>{renderCell(hp.service_port)}</div>
                                            <div>{renderCell(hp.honeypot_type)}</div>
                                            {
                                                renderCell(hp.honeypot_status) === "－" ? "－" : (
                                                    <div style={{ color: hp.honeypot_status === 'active' ? 'green' : 'red' }}>
                                                        {hp.honeypot_status === 'active' ? '在线' : '离线'}
                                                    </div>
                                                )
                                            }
                                        </div>
                                    ))
                                ) : (
                                    <div>暂无蜜罐服务</div>
                                )
                            )}

                            <div
                                className={styles.add_honeypo_services_bar}
                                onClick={() => setAddDialogOpen(true)}
                            >
                                添加蜜罐服务 +
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {/* 模态框部分 */}
            <Dialog open={open} onClose={handleClose}>
                <DialogTitle>
                    编辑{editField ? `「${editField.label}」` : ""}
                </DialogTitle>
                <DialogContent>
                    <TextField
                        autoFocus
                        margin="dense"
                        fullWidth
                        value={editValue}
                        onChange={e => setEditValue(e.target.value)}
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleClose}>返回</Button>
                    <Button onClick={handleSubmit} variant="contained" color="primary">
                        提交
                    </Button>
                </DialogActions>
            </Dialog>
            {/* 添加蜜罐服务模态框 */}
            <AddHoneypotDialog
                open={addDialogOpen}
                nodeId={nodeItem.node_info.id}
                onClose={() => setAddDialogOpen(false)}
                onSubmit={handleAddHoneypot}
            />
        </div>
    )
}