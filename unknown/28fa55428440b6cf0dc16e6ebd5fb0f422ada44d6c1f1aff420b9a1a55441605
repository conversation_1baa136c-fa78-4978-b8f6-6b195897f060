// src/pages/HoneyPotGraph.tsx
import React from 'react';
import ReactECharts from 'echarts-for-react';

interface Honeypot {
  honeypot_name: string;
  honeypot_type: string;
  honeypot_status: string;
}

interface Node {
  node_name: string;
  node_status: string;
  honeypots: Honeypot[];
}

interface DepartmentData {
  department: string;
  nodes: Node[];
}

interface Props {
  data: DepartmentData[];
}

export default function HoneyPotGraph({ data }: Props) {
const chartData = {
  nodes: [] as { id: string; name: string; symbolSize: number; itemStyle: any }[],
  links: [] as { source: string; target: string }[],
};
const departmentColorMap = [
  '#4CAF50', '#2196F3', '#9C27B0', '#FF5722', '#795548',
  '#607D8B', '#009688', '#3F51B5', '#E91E63', '#8BC34A',
];

const idSet = new Set<string>();

data.forEach((dept, deptIdx) => {
  const deptColor = departmentColorMap[deptIdx % departmentColorMap.length];
  const deptId = `dept-${deptIdx}`;
  
  chartData.nodes.push({
    id: deptId,
    name: dept.department,
    symbolSize: 70,
    itemStyle: { color: deptColor },
  });

  chartData.links.push({
    source: deptId,
    target: 'root',
  });

  dept.nodes.forEach((node, nodeIdx) => {
    const nodeId = `dept-${deptIdx}-node-${nodeIdx}`;

    chartData.nodes.push({
      id: nodeId,
      name: node.node_name,
      symbolSize: 50,
      itemStyle: { color: '#90CAF9' },
    });

    chartData.links.push({
      source: nodeId,
      target: deptId,
    });

    node.honeypots.forEach((hp, hpIdx) => {
      const hpId = `dept-${deptIdx}-node-${nodeIdx}-hp-${hpIdx}`;

      chartData.nodes.push({
        id: hpId,
        name: hp.honeypot_name,
        symbolSize: 40,
        itemStyle: {
          color: hp.honeypot_status === 'active' ? '#F44336' : '#9E9E9E',
        },
      });

      chartData.links.push({
        source: hpId,
        target: nodeId,
      });
    });
  });
});

// 添加根节点
chartData.nodes.unshift({
  id: 'root',
  name: '国家电网',
  symbolSize: 80,
  itemStyle: { color: '#388E3C' },
});

  const option = {
  series: [
    {
      type: 'graph',
      layout: 'force',
      roam: true,
      label: {
        show: true,
        position: 'inside',
        color: '#fff',
        fontSize: 10,
        overflow: 'truncate',
        formatter: '{b}',
      },
      force: {
        repulsion: 1000,
      },
      edgeSymbol: ['none', 'arrow'],
      edgeSymbolSize: 10,
      lineStyle: {
        color: '#999',
        width: 2,
        curveness: 0.2,
      },
      data: chartData.nodes,
      links: chartData.links,
    },
  ],
};


  return <ReactECharts option={option} style={{ height: '100%', width: '100%' }} />;
}
