.attack-list-container {
  width: 100%;
  height: 100%;
  background-color: white;
  padding: 0 20px;
  display: flex;
  flex-direction: column;
}

.attack-list-search-content {
  width: 100%;
  height: 120px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.attack-list-download-btns {
  display: flex;
  flex-direction: row;
  gap: 15px;
}

.attack-list-search-list {
  width: 50%;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  gap: 25px;
}

.attack-list-select {
  width: 20%;
}

.attack-list-pagination {
  height: 12vh;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.attack-list-table {
  height: 90%;
}

.attack-list-loading {
  position: absolute;
  top: 150;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}

.attack-list-pagination-div {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 15px;
}
