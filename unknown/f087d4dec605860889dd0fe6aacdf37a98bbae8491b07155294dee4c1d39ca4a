import { Button, CircularProgress, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, Popover, Typography } from "@material-ui/core";
import { use } from "echarts";
import { useState } from "react";
import { DropzoneArea } from 'material-ui-dropzone';
import { makeStyles, createStyles, Theme } from '@material-ui/core/styles';
import { showSnackbar } from "../points-manage/component/myMessageBar";
import apiClient from "../apis/apiClient";
const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    input: {
      display: 'none',
    },
  previewChip: {
    minWidth: 160,
    maxWidth: 210
  },
  customDropZone: {
    // minHeight: 80,      // 最小高度 80px
    // maxHeight: 200,     // 最大高度 200px
    height: "100px !important",
    margin: '0 auto',   // 居中（可选）
    border: '2px dashed #999',
  },
  customParagraph: {
    fontSize: '20px',
    color: '#666',
  },
  typography: {
      padding: theme.spacing(2),
  },
  }),
);
const helpText = `
🤖 SGCC蜜罐AI助手使用指南
🔧 主要功能：
• 📊 日志分析 - 智能分析安全日志，检测威胁和异常
• 🔇 告警降噪 - 智能聚合相似告警，减少噪音
• 🎯 攻击溯源 - 分析攻击链路，追踪攻击来源
• 💬 智能对话 - 支持上下文对话和问答

📁 文件支持：
• 支持格式：.txt, .log, .json, .csv, .tsv
• 最大大小：10MB
• 支持批量上传多个文件

🚀 快速使用：
1. 上传文件到文件区域
2. 点击对应功能按钮开始分析
3. 等待分析完成并查看结果

💡 提示：我可以记住对话历史，支持连续对话和结果关联分析
`;



export default function FileUpload() {
  const classes = useStyles();
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [buttonSelect, setButtonSelect] = useState(1)
  const [helpOpen, setHelpOpen]=useState(false)
  const [files, setFiles]=useState<File[]>([])
  const [dropzoneKey, setDropzoneKey] = useState(0);
  const [helpTextContent, setHelpTextContent] = useState("");
  const [loading, setLoading] = useState(false);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleOnclick2 =async () => {
    if(files.length === 0){
        showSnackbar("请先上传文件", "error");
    } else {
        try {
            setLoading(true)
            const formData = new FormData();
            files.forEach(file => formData.append('files', file))
            formData.append('config', `{"model_type":"local","analysis_mode":"comprehensive","output_format":"json","include_suggestions":true,"enable_mitre_mapping":true,"enable_threat_intelligence":true,"max_events":10000,"openai_api_key":"EMPTY","openai_model":"Foundation-Sec-8B","openai_base_url":"http://**************:6232/v1"}`)
            formData.append('analysis_type', 'log_analysis')
            const res = await apiClient.post('/api/log-analysis/analyze', 
                formData
            );
            console.log(res)
            let formatted = ''
            const result =res.data.result
            if (result.summary) {
            formatted += `📊 分析概要：\n${result.summary}\n\n`
            }

            // 显示整体风险等级
            if (result.overall_risk) {
              const levelEmoji = result.overall_risk === 'high' ? '🔴' :
                result.overall_risk === 'medium' ? '🟡' : '🟢'
              formatted += `${levelEmoji} 整体威胁等级：${result.overall_risk.toUpperCase()}\n\n`
            }
        
            // 显示每个文件的分析结果
            if (result.files && result.files.length > 0) {
              formatted += `� 文件分析详情：\n`
              result.files.forEach((file, index) => {
                formatted += `\n${index + 1}. 📄 ${file.filename}\n`
                formatted += `   • 文件大小: ${file.file_size} 字节\n`
                formatted += `   • 分析行数: ${file.lines_analyzed || 0} 行\n`

                if (file.threats_detected !== undefined) {
                  formatted += `   • 检测威胁: ${file.threats_detected} 个\n`
                }
            
                if (file.risk_level) {
                  const riskEmoji = file.risk_level === 'high' ? '🔴' :
                    file.risk_level === 'medium' ? '🟡' : '🟢'
                  formatted += `   • 风险等级: ${riskEmoji} ${file.risk_level.toUpperCase()}\n`
                }
            
                if (file.threat_details && file.threat_details.length > 0) {
                  formatted += `   • 威胁详情:\n`
                  file.threat_details.forEach(threat => {
                    formatted += `     ⚠️ ${threat}\n`
                  })
                }
            
                if (file.error) {
                  formatted += `   ❌ 错误: ${file.error}\n`
                }
              })
            }
            console.log(formatted)
            setHelpTextContent(formatted)
            setButtonSelect(2)
            setHelpOpen(true)
        }catch{
            showSnackbar("日志分析文件分析失败", "error");
        }finally{
            setLoading(false)
        }
    }
  }

  const handleOnclick3 =async () => {
     if(files.length === 0){
        showSnackbar("请先上传文件", "error");
    } else {
     try {
        setLoading(true)
        const formData = new FormData();
        files.forEach(file => formData.append('files', file))
        formData.append('config', `{"model_type":"local","analysis_mode":"comprehensive","output_format":"json","include_suggestions":true,"enable_mitre_mapping":true,"enable_threat_intelligence":true,"max_events":10000,"openai_api_key":"EMPTY","openai_model":"Foundation-Sec-8B","openai_base_url":"http://**************:6232/v1"}`)
        formData.append('analysis_type', 'noise_reduction')
        const res = await apiClient.post('/api/noise-reduction/reduce', 
            formData
        );
        console.log(res)
        let formatted = ''
        const result =res.data.result
        if (result.summary) {
        formatted += `📊 降噪概要：\n${result.summary}\n\n`
        }
        // 显示整体降噪效果
        if (result.total_reduction_rate) {
        formatted += `📈 整体降噪效果：\n• 降噪率：${result.total_reduction_rate}\n\n`
        }
        // 显示每个文件的降噪结果
        if (result.files && result.files.length > 0) {
        formatted += `📁 文件降噪详情：\n`
        result.files.forEach((file, index) => {
          formatted += `\n${index + 1}. 📄 ${file.filename}\n`
          formatted += `   • 文件大小: ${file.file_size} 字节\n`

          if (file.original_alerts !== undefined) {
            formatted += `   • 原始告警: ${file.original_alerts} 条\n`
          }

          if (file.unique_alerts !== undefined) {
            formatted += `   • 唯一告警: ${file.unique_alerts} 条\n`
          }

          if (file.duplicates_removed !== undefined) {
            formatted += `   • 移除重复: ${file.duplicates_removed} 条\n`
          }

          if (file.noise_reduction_rate) {
            formatted += `   • 降噪率: ${file.noise_reduction_rate}\n`
          }

          if (file.alert_breakdown) {
            formatted += `   • 告警分级:\n`
            if (file.alert_breakdown.critical > 0) {
              formatted += `     🔴 严重: ${file.alert_breakdown.critical} 条\n`
            }
            if (file.alert_breakdown.warning > 0) {
              formatted += `     🟡 警告: ${file.alert_breakdown.warning} 条\n`
            }
            if (file.alert_breakdown.info > 0) {
              formatted += `     ℹ️ 信息: ${file.alert_breakdown.info} 条\n`
            }
          }

          if (file.error) {
            formatted += `   ❌ 错误: ${file.error}\n`
          }
        })
        }
        console.log(formatted)
        setHelpTextContent(formatted)
        setButtonSelect(3)
        setHelpOpen(true)
     } catch {
        showSnackbar("告警降噪文件分析失败", "error");
     } finally {
        setLoading(false)
     }
    }   
  }

  const handleOnclick4 =async () => {
     if(files.length === 0){
        showSnackbar("请先上传文件", "error");
    } else {
        try{
            setLoading(true)
            const formData = new FormData();
            files.forEach(file => formData.append('files', file))
            formData.append('config', `{"model_type":"local","analysis_mode":"comprehensive","output_format":"json","include_suggestions":true,"enable_mitre_mapping":true,"enable_threat_intelligence":true,"max_events":10000,"openai_api_key":"EMPTY","openai_model":"Foundation-Sec-8B","openai_base_url":"http://**************:6232/v1"}`)
            formData.append('analysis_type', 'attack-trace')
            const res = await apiClient.post('/api/attack-trace/analyze', 
                formData
            );
            console.log(res)
            let formatted = ''
            const result =res.data.result
            if (result.summary) {
              formatted += `📊 溯源概要：\n${result.summary}\n\n`
            }

            // 显示整体攻击分析
            if (result.unique_ips_count !== undefined) {
              formatted += `🌐 发现 ${result.unique_ips_count} 个唯一攻击源IP\n`
            }

            if (result.total_attack_patterns !== undefined) {
              formatted += `⚔️ 检测到 ${result.total_attack_patterns} 种攻击模式\n\n`
            }

            // 显示整体风险等级
            if (result.overall_risk) {
              const levelEmoji = result.overall_risk === 'high' ? '🔴' :
                result.overall_risk === 'medium' ? '🟡' : '🟢'
              formatted += `${levelEmoji} 整体风险等级：${result.overall_risk.toUpperCase()}\n\n`
            }

            // 显示每个文件的溯源结果
            if (result.files && result.files.length > 0) {
              formatted += `� 文件溯源详情：\n`
              result.files.forEach((file, index) => {
                formatted += `\n${index + 1}. 📄 ${file.filename}\n`
                formatted += `   • 文件大小: ${file.file_size} 字节\n`
                formatted += `   • 分析行数: ${file.lines_analyzed || 0} 行\n`

                if (file.unique_ips !== undefined) {
                  formatted += `   • 唯一IP: ${file.unique_ips} 个\n`
                }

                if (file.attack_patterns_found !== undefined) {
                  formatted += `   • 攻击模式: ${file.attack_patterns_found} 种\n`
                }

                if (file.suspicious_activities !== undefined) {
                  formatted += `   • 可疑活动: ${file.suspicious_activities} 个\n`
                }

                if (file.risk_assessment) {
                  const riskEmoji = file.risk_assessment === 'high' ? '🔴' :
                    file.risk_assessment === 'medium' ? '🟡' : '🟢'
                  formatted += `   • 风险评估: ${riskEmoji} ${file.risk_assessment.toUpperCase()}\n`
                }

                if (file.top_ips && file.top_ips.length > 0) {
                  formatted += `   • 主要攻击源IP:\n`
                  file.top_ips.slice(0, 5).forEach(ip => {
                    formatted += `     🌐 ${ip}\n`
                  })
                }

                if (file.attack_samples && file.attack_samples.length > 0) {
                  formatted += `   • 攻击样本:\n`
                  file.attack_samples.slice(0, 3).forEach(sample => {
                    formatted += `     ⚠️ ${sample}\n`
                  })
                }

                if (file.error) {
                  formatted += `   ❌ 错误: ${file.error}\n`
                }
            })
            setHelpTextContent(formatted)
            setButtonSelect(4)
            setHelpOpen(true)
      }
        }catch{
            showSnackbar("攻击溯源文件分析失败", "error");
        }finally{
            setLoading(false)
        }
    }
  }

  const handleOnclick5 = () => {
    setHelpOpen(true)
    setButtonSelect(5)
  }

  const handleOnclick6 = () => {
    setFiles([]);
    showSnackbar("文件已清空", "success");
    console.log(files)
    setButtonSelect(6)
  } 

  return (
    <>   {loading && (
      <div style={{
        position: 'fixed',
        top: 0, left: 0, right: 0, bottom: 0,
        // backgroundColor: 'rgba(255, 255, 255, 0.6)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 9999
      }}>
        <CircularProgress size={50} color="primary" />
      </div>
    )}
        <Button onClick={()=>{setHelpOpen(true),setButtonSelect(1)}}> ❓ 帮助</Button>
        <Button onClick={()=>handleOnclick2()}> 📊 日志分析</Button>
        <Button onClick={()=>handleOnclick3()}> 🔇 告警降噪</Button>
        <Button onClick={()=>handleOnclick4()}> 🎯 攻击溯源</Button>
        <Button onClick={()=>handleOnclick5()}> 📋 文件上传</Button>
        <Button onClick={()=>handleOnclick6()}> 🔄 清空文件</Button>
        <Button onClick={handleClick}>✅已选择文件</Button>
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={()=> setAnchorEl(null)}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
      >
        <div className={classes.typography}>
    <Typography variant="subtitle1">已选择文件：</Typography>
    {files.length === 0 ? (
      <Typography color="textSecondary">（暂无文件）</Typography>
    ) : (
      files.map((file, index) => (
        <Typography key={index} variant="body2">📄 {file.name}</Typography>
      ))
    )}
  </div>
      </Popover>
        <Dialog open={helpOpen}>
            <DialogTitle>
                {buttonSelect === 1?"帮助":buttonSelect === 2?"日志分析":buttonSelect === 3?"告警降噪":buttonSelect === 4?"攻击溯源":buttonSelect === 5?"文件上传":buttonSelect === 6?"清空文件":""}
            </DialogTitle>
            <DialogContent style={{ height: '500px', overflow: 'auto',width:"500px" }}>
              <DialogContentText style={{ whiteSpace: 'pre-line',color:"black" }}>
                {buttonSelect === 1?helpText:
                 buttonSelect === 2?helpTextContent:
                 buttonSelect === 3?helpTextContent:
                 buttonSelect === 4?helpTextContent:
                 buttonSelect === 5?<div className="myUploader" style={{width: '95%',margin: '0 auto'}}>
                                     <DropzoneArea 
                                       showPreviews={true}
                                       acceptedFiles={['.txt', '.log', '.json', '.csv', '.tsv']}
                                       showPreviewsInDropzone={false} 
                                       useChipsForPreview
                                       dropzoneClass={classes.customDropZone}
                                       dropzoneParagraphClass={classes.customParagraph}
                                       previewGridProps={{container: { spacing: 1, direction: 'row' }}}
                                       previewChipProps={{classes: { root: classes.previewChip } }}
                                       dropzoneText="请将文件拖拽到此处，或点击此处上传支持: .txt, .log, .json, .csv, .tsv (最大10MB)"
                                       previewText="已选择文件："
                                       showAlerts={false}
                                       filesLimit={6}
                                       maxFileSize={10 * 1024 * 1024}
                                       onChange={(uploadedFiles) => {
                                          setFiles(prev => [...prev, ...uploadedFiles]);
                                        }
                                       }
                                       />
                                   </div>:""
                //  buttonSelect === 6?:""
                 }
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              {buttonSelect === 5?
              <Button  color="primary" onClick={() => {setHelpOpen(false),setDropzoneKey(0),console.log(files)}}>
                确认
              </Button>:
              <Button color="primary" onClick={() => {setHelpOpen(false),setHelpTextContent("")}}>
                确认
              </Button>}
            </DialogActions>

        </Dialog>
    </>
  )
}