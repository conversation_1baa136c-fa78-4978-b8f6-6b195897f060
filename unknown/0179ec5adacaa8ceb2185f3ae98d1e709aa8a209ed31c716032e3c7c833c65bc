// src/pages/HoneyPotTable.tsx
import React from 'react';
import {
  Table, TableBody, TableCell, TableContainer, TableHead,
  TableRow, Paper, Button, makeStyles, withStyles,
  CircularProgress
} from '@material-ui/core';
import Pagination from '@material-ui/lab/Pagination';
import { ConfigOption } from './config';

const useStyles = makeStyles(() => ({
  tag: {
    display: 'inline-block',
    padding: '2px 8px',
    borderRadius: 12,
    fontSize: 12,
    minWidth: 40,
    textAlign: 'center',
    color: '#000',
  },
  greenTag: {
      backgroundColor: '#A5D6A7', // 更深的绿色
  color: '#1B5E20',
  },
  redTag: {
    backgroundColor: '#ffcdd2',
  },
  typeTag: {
    backgroundColor: '#e3f2fd',
    color: '#000',
  },
  actionButton: {
    color: '#fff',
    fontSize: 12,
    marginRight: 8,
    padding: '4px 12px',
    minWidth: 64,
  },
  viewBtn: {
    backgroundColor: '#4caf50',
  },
  editBtn: {
    backgroundColor: '#81c784',
  },
  deleteBtn: {
    backgroundColor: '#f44336',
  },
}));



interface ConfigTableProps {
  data: ConfigOption[];
  page: number;
  pageSize: number;
  total: number;
  onPageChange: (page: number) => void;
  onRowsPerPageChange: (pageSize: number) => void; 
  onView: (row: ConfigOption) => void;
  onEdit: (row: ConfigOption) => void;
  onDelete: (row: ConfigOption) => void;
  loading: boolean; 
}



export default function AnalyseModelTable({
  data,
  page,
  pageSize,
  total,
  onPageChange,
  onView,
  onEdit,
  loading,
  onDelete
}: ConfigTableProps) {
  const classes = useStyles();

  const StyledTableRow = withStyles(() => ({
    root: {
      height: 52,
      '&:nth-of-type(odd)': {
        backgroundColor: '#f9f9f9',
      },
    },
  }))(TableRow);

  const StyledTableCell = withStyles(() => ({
    root: {
      paddingTop: 8,
      paddingBottom: 8,
      lineHeight: '1.4rem',
      textAlign: 'center',
    },
     head: {
      backgroundColor: '#babcbb',
      color: '#fff',
      textAlign: 'center',
    },
  }))(TableCell);

  const formatDate = (iso: string) => {
    if (!iso) return '-';
    const date = new Date(iso);
    return date.toLocaleString('zh-CN', { hour12: false });
  };

  const renderTypeTag = (type: string) => {
    const colorMap: Record<string, string> = {
      local: '#bbdefb',
      openai: '#d1c4e9',
    };
    return (
      <span
        className={classes.tag}
        style={{ backgroundColor: colorMap[type] || '#e0e0e0' }}
      >
        {type}
      </span>
    );
  };

  const renderStatusTag = (active: boolean) => (
    <span className={`${classes.tag} ${active ? classes.greenTag : classes.redTag}`}>
      {active ? '已激活' : '已停用'}
    </span>
  );

  const renderBooleanTag = (value: boolean) => (
    <span className={`${classes.tag} ${value ? classes.greenTag : classes.redTag}`}>
      {value ? '是' : '否'}
    </span>
  );

  return (
    <TableContainer
      component={Paper}
      style={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        minHeight: 600,
      }}
    >
      <div style={{ flex: 1, overflowY: 'auto', overflowX: 'hidden' }}>
        <Table>
          <TableHead>
            <StyledTableRow>
              <StyledTableCell>ID</StyledTableCell>
              <StyledTableCell>配置名称</StyledTableCell>
              <StyledTableCell>类型</StyledTableCell>
              <StyledTableCell>激活状态</StyledTableCell>
              <StyledTableCell>是否默认</StyledTableCell>
              <StyledTableCell>创建时间</StyledTableCell>
              <StyledTableCell>操作</StyledTableCell>
            </StyledTableRow>
          </TableHead>
         <TableBody style={{ position: 'relative' }}>
  {loading ? (
    // 加载中状态
    <TableRow>
      <TableCell colSpan={7} align="center">
        <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <CircularProgress />
        </div>
      </TableCell>
    </TableRow>
  ) : data.length === 0 ? (
    // 无数据状态
    <TableRow>
      <TableCell colSpan={7} align="center">
        暂无数据
      </TableCell>
    </TableRow>
  ) : (
    // 正常渲染数据
    data.map((row, index) => (
      <StyledTableRow key={row.id}>
        <StyledTableCell>{page * pageSize + index + 1}</StyledTableCell>
        <StyledTableCell>{row.config_name}</StyledTableCell>
        <StyledTableCell>{renderTypeTag(row.config_type)}</StyledTableCell>
        <StyledTableCell>{renderStatusTag(row.is_active)}</StyledTableCell>
        <StyledTableCell>{renderBooleanTag(row.is_default)}</StyledTableCell>
        <StyledTableCell>{formatDate(row.created_at)}</StyledTableCell>
        <StyledTableCell>
          <Button
            className={`${classes.actionButton} ${classes.viewBtn}`}
            onClick={() => onView(row)}
            disabled={loading}
          >
            查看
          </Button>
          <Button
            className={`${classes.actionButton} ${classes.editBtn}`}
            onClick={() => onEdit(row)}
            disabled={loading}
          >
            编辑
          </Button>
          <Button
            className={`${classes.actionButton} ${classes.deleteBtn}`}
            onClick={() => onDelete(row)}
            disabled={loading}
          >
            删除
          </Button>
        </StyledTableCell>
      </StyledTableRow>
    ))
  )}
</TableBody>

        </Table>
      </div>

      {/* 分页 */}
      {/* <div style={{ display: 'flex', justifyContent: 'space-between', padding: 16 }}> */}
      <div className="attack-list-pagination-div">
        <div>共 {total} 条数据</div>
        <Pagination
        className="attack-list-pagination"
          showFirstButton
          count={Math.ceil(total / pageSize)}
          page={page + 1}
          onChange={(event, value) => onPageChange(value - 1)}
        />
      </div>
    </TableContainer>
  );
}
