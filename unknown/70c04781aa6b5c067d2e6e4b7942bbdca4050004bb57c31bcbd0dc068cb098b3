import { Button, Dialog, <PERSON>alogActions, DialogContent, DialogTitle, Grid, TextField } from "@material-ui/core";
import { useState } from "react";

import { FormControl, FormLabel, Stack, Typography } from '@mui/material';

export default function Detail({data}:any) {
  const [open, setOpen] = useState(false);
  const items = [
    { title: 'HIGH',   value: 42,  bg: '#EF9A9A', fg: '#B71C1C' },
    { title: 'MEDIUM', value: 17,  bg: '#FFCC80', fg: '#E65100' },
    { title: 'LOW',    value: 8,   bg: '#FFF59D', fg: '#9E8200' },
    { title: 'INFO',   value: 23,  bg: '#C8E6C9', fg: '#2E7D32' },
  ];
  return <>
    <Button onClick={() => setOpen(true)}>详情</Button>
    <Dialog open={open}   maxWidth="sm" fullWidth >
            <DialogTitle>告警详情</DialogTitle>
            <DialogContent >
                <Grid container spacing={2}>
                    <Grid item xs={12}>
                        <TextField label="告警id" fullWidth InputProps={{readOnly: true}} defaultValue={data.alert_id} />
                    </Grid>
                    <Grid item xs={12}>
                        <TextField  label="告警名称"  fullWidth   InputProps={{readOnly: true}} defaultValue={data.title} />
                    </Grid>
                    <Grid item xs={12}>
                        <TextField label="严重级" fullWidth InputProps={{readOnly: true}} defaultValue={data.severity === "HIGH"? "严重":data.severity === "MEDIUM"? "警告":data.severity === "LOW"? "提示":"信息"} />
                    </Grid>
                    <Grid item xs={12}>
                        <TextField label="描述" InputProps={{readOnly: true}} fullWidth defaultValue={data.description} />
                    </Grid>
                    <Grid item xs={12}>
                        <FormControl fullWidth  variant="standard">
                        <FormLabel sx={{ mb: 0.5, fontWeight: 400,fontSize: 12}}>标签</FormLabel>
                        <Grid container>
                            {[1, 2, 3, 4].map((v, idx) => (
                            <Grid item xs={5} key={idx}>
                                <Typography variant="body2" sx={{ bgcolor: "#EFEFEF", borderRadius: 5, lineHeight: 1.6, fontWeight: 500,margin: "8px 0px",marginRight:"10px"}}>
                                  <span style={{fontWeight:"bold",marginLeft:10}}>{v===1? "主机名":v===2? "区域":v===3? "源IP地址":v===4? "目标IP地址":""}</span>: 
                                  <span>{v===1? data.hostname:v===2? data.geo_country+"_"+data.geo_city:v===3? data.source_ip:v===4? data.destination_ip:""}</span>
                                </Typography>
                            </Grid>
                          ))}
                        </Grid>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12}>
                        <FormControl fullWidth  variant="standard">
                        <FormLabel sx={{ mb: 0.5, fontWeight: 400,fontSize: 12}}>指标</FormLabel>
                        <Grid container>
                            {[1, 2, 3, 4].map((v, idx) => (
                            <Grid item xs={5} key={idx}>
                                <Typography variant="body2" sx={{ bgcolor: "#EFEFEF", borderRadius: 5, lineHeight: 1.6, fontWeight: 500,margin: "8px 0px",marginRight:"10px"}}>
                                  <span style={{fontWeight:"bold",marginLeft:10}}>{v===1? "CPU使用率":v===2? "平均负载":v===3? "内存占用率":v===4? "磁盘IO等待":""}</span>: 
                                  <span>{v===1? "96.2%":v===2? "8.7":v===3? "72.4%":v===4? "12.8%":""}</span>
                                </Typography>
                            </Grid>
                          ))}
                        </Grid>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12}>
                        <TextField  label="时间戳"   fullWidth  InputProps={{readOnly: true}} defaultValue={data.timestamp?.replace('T', ' ')} />
                    </Grid>
                    <Grid item xs={12}>
                        <TextField  label="状态"  fullWidth InputProps={{readOnly: true}} defaultValue={data.status === 1? "新告警":data.status === 2? "已聚合":""} />
                    </Grid>
                </Grid>
            </DialogContent>
            <DialogActions>
                 <Button  onClick={() => setOpen(false)} variant="contained" color="primary">
                     关闭
                 </Button>
            </DialogActions>
        </Dialog>
  </>
}