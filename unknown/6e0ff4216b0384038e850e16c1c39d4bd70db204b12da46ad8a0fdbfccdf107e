import React, { useEffect, useState } from 'react';
import {
  <PERSON>,
  <PERSON>ton,
  Collapse,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  Input,
  InputAdornment,
  NativeSelect,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from '@material-ui/core';
import styles from './less/index.module.less';
import { NodeItem } from './interface/interface';
import ExpendCard from './component/expendCard';
import AddNodeDialog from './component/addNodeDialog';
import { showSnackbar } from './component/myMessageBar';
import { Cached, Search, SearchOutlined } from '@material-ui/icons';
import EditNodeDialog from "./component/editNodeDialog";
import Pagination from '@material-ui/lab/Pagination';
import Add from '@material-ui/icons/Add';
import apiClient from '../apis/apiClient';

export default function MUIExpandTable() {
  const [expandedKey, setExpandedKey] = React.useState<number | null>(null);
  const [data, setData] = useState<NodeItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);
  const [addNodeOpen, setAddNodeOpen] = useState(false);
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [total, setTotal] = useState(0);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [deleteNodeId, setDeleteNodeId] = useState<number | null>(null);
  const [node_status, setNodeStatus] = useState<string | null>(null);
  const [ip_address, setIpAddress] = useState<string | null>(null);
  const [honeypotMap, setHoneypotMap] = useState<Record<number, any[]>>({});
  const [honeypotLoading, setHoneypotLoading] = useState<Record<number, boolean>>({});
  const [editNodeOpen, setEditNodeOpen] = useState(false);
  const [editingNode, setEditingNode] = useState<any | null>(null);

  const handleGetData = (pageParam = page, rowsParam = rowsPerPage, ipParam = ip_address, statusParam = node_status) => {
    setLoading(true);
    apiClient.get('/api/nodes', {
      params: { page: pageParam, per_page: rowsParam, ip_address: ipParam, node_status: statusParam }
    }).then(res => {
      const adapted = adaptNodeList(res.data.data.items);
      setData(adapted);
      setTotal(res.data.data.pagination.total);
      setLoading(false);
    }, err => {
      showSnackbar(err?.response?.data?.message || err?.message || "网络错误！", "error");
    }).catch(() => {
      showSnackbar('获取节点列表失败', 'error');
    }).finally(() => {
      setLoading(false);
    });
  }
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
    handleGetData(newPage, rowsPerPage);
  };
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newRowsPerPage = parseInt(event.target.value, 10);
    setRowsPerPage(newRowsPerPage);
    setPage(0);
    handleGetData(0, newRowsPerPage);
  };
  useEffect(() => {
    handleGetData()
  }, []);
  function adaptNodeList(items: any[]): NodeItem[] {
    return items.map(item => {
      // 这里做字段兼容和转换，缺失的字段给默认值
      return {
        node_info: {
          id: item.node_info?.id ?? 0,
          node_name: item.node_info?.node_name ?? null,
          node_status: item.node_info?.node_status ?? null,
          created_at: item.node_info?.created_at ?? null,
          updated_at: item.node_info?.updated_at ?? null,
          deployed_at: item.node_info?.deployed_at ?? null,
          deployment_location: item.node_info?.deployment_location ?? null,
          node_type: item.node_info?.node_type ?? null,
          traffic: Array.isArray(item.node_info?.traffic) ? item.node_info.traffic : [0, 0, 0, 0, 0, 0, 0],
          department: item.node_info?.department ?? null,
        },
        host_info: {
          operating_system: item.host_info?.operating_system ?? null,
          architecture: item.host_info?.architecture ?? null,
          timezone: item.host_info?.timezone ?? null,
          ip_address: item.host_info?.ip_address ?? null,
          subnet_mask: item.host_info?.subnet_mask ?? null,
          dns_servers: item.host_info?.dns_servers ?? null,
          mac_address: item.host_info?.mac_address ?? null,
          hardware_config: item.host_info?.hardware_config ?? null,
          host_type: item.host_info?.host_type ?? null
        }
      }
    });
  }


  const handleExpandClick = (key: number) => {
    if (expandedKey === key) {
      setExpandedKey(null);
      return;
    }
    setExpandedKey(key);

    // 如果已经请求过，不再重复请求
    if (honeypotMap[key]) return;

    setHoneypotLoading(prev => ({ ...prev, [key]: true }));

    apiClient.get('/api/nodes/honeypots', {
      params: { node_id: key }
    })
      .then(res => {
        if (res.data && res.data.success) {
          setHoneypotMap(prev => ({ ...prev, [key]: res.data.data }));
        } else {
          setHoneypotMap(prev => ({ ...prev, [key]: [] }));
        }
      })
      .catch(e => {
        setHoneypotMap(prev => ({ ...prev, [key]: [] }));
        showSnackbar(e?.response?.data?.message || e?.message || "网络错误！", "error");
      })
      .finally(() => {
        setHoneypotLoading(prev => ({ ...prev, [key]: false }));
      });
  };


  // 新增节点提交
  const handleAddNode = (formData: any) => {
    // 这里替换为你的真实接口地址
    apiClient.post('/api/create_node', formData).then(res => {
      showSnackbar("节点创建成功", 'success')
      handleGetData();
      setAddNodeOpen(false);
    }, err => {
      showSnackbar(err?.response?.data?.message || err?.message || "网络错误！", "error");
    });
  };

  const handleDeleteNode = (node_id: number) => {
    try {
      // 这里替换为你的真实接口地址
      apiClient.post(`/api/delete_node`, { node_id: node_id }).then(res => {
        showSnackbar('节点删除成功', 'success')
        if (total % rowsPerPage === 1 && page > 1) {
          handleGetData(page - 1, rowsPerPage);
          setPage(page - 1);
        } else {
          handleGetData();
        }
      }, err => {
        showSnackbar(err?.response?.data?.message || err?.message || "网络错误！", "error");
      });
      // TODO: 刷新节点列表
    } catch (e) {
      showSnackbar('节点删除失败', 'error')
    }
  };

  const getHoneypotsByNodeId = (nodeId: number) => {
    setHoneypotLoading(prev => ({ ...prev, [nodeId]: true }));

    apiClient.get('/api/nodes/honeypots', {
      params: { node_id: nodeId }
    })
      .then(res => {
        setHoneypotMap(prev => ({ ...prev, [nodeId]: res.data.data || [] }));
      })
      .catch(e => {
        setHoneypotMap(prev => ({ ...prev, [nodeId]: [] }));
        showSnackbar(e?.response?.data?.message || e?.message || "网络错误！", "error");
      })
      .finally(() => {
        setHoneypotLoading(prev => ({ ...prev, [nodeId]: false }));
      });
  };
  const handleEditNode = (formData: any) => {
    apiClient.post('/api/update_node', {
      ...formData,
      node_id: formData.id
    }).then(res => {
      showSnackbar("节点更新成功", 'success');
      handleGetData();
      setEditNodeOpen(false);
      setEditingNode(null);
    }, err => {
      showSnackbar(err?.response?.data?.message || err?.message || "网络错误！", "error");
    });
  };

  function renderCell(value: any) {
    return value && value.toString().trim().length > 0 ? value : '－';
  }
  return (
    <div className={styles.container}>
      <div className={styles.search_card}>
        <div>
          <div>
            <Input
              className={`${styles.search_input} ${styles.noFocus}`}
              id="outlined-adornment-weight"
              startAdornment={
                <InputAdornment position="start">
                  <SearchOutlined />
                </InputAdornment>
              }
              placeholder="搜索节点地址，支持模糊搜索"
              value={ip_address ?? ''}
              onChange={(e) => setIpAddress(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  setPage(0);
                  handleGetData(0, rowsPerPage);
                }
              }}
            />
          </div>
          <div className={styles.search_select_bar}>
            <div className={styles.search_select_bar_left}>
              <div className={styles.search_select_bar_left_item}>
                <div>节点状态：</div>
                <div>
                  <FormControl className={styles.formControl}>
                    <NativeSelect
                      value={node_status ?? ''}
                      onChange={(e) => {
                        setNodeStatus(e.target.value || null);
                        setPage(0);
                      }}
                      name="node_status"
                      className={styles.selectEmpty}
                      inputProps={{ 'aria-label': 'node_status' }}
                    >
                      <option value="">全部</option>
                      <option value="online">在线</option>
                      <option value="offline">离线</option>
                    </NativeSelect>
                  </FormControl>
                </div>
              </div>
            </div>
            <div className={styles.search_select_bar_right}>
              <Button
                className={`${styles.search_select_reload_button} ${styles.noFocus}`}
                onClick={() => {
                  setPage(0);
                  handleGetData(0, rowsPerPage);
                }}
                startIcon={<Search />}
              >
                <span style={{ paddingTop: "1px" }}>
                  搜索
                </span>
              </Button>
              <Button
                className={`${styles.search_select_reload_button} ${styles.noFocus}`}
                onClick={() => {
                  setIpAddress(null);
                  setNodeStatus(null);
                  setPage(0);
                  handleGetData(0, rowsPerPage, null, null);
                }}
                startIcon={<Cached />}
              >
                <span style={{ paddingTop: "1px" }}>
                  重置
                </span>
              </Button>
            </div>
          </div>
        </div>
      </div>
      <div className={styles.table_card}>
        {/* <TableContainer component={Paper} className={styles.tableContainer}> */}
        <div className={styles.tableContainer}>
          <div className={styles.tableBox}>
            <div className={styles.toolBar}>
              <Button className={`${styles.customButton} ${styles.noFocus}`} startIcon={<Add />} onClick={() => setAddNodeOpen(true)} color="primary" variant="contained">
                <span style={{ paddingTop: "1px" }}>增加节点</span>
              </Button>
            </div>
            <Table className={styles.myTable}>
              <TableHead className={styles.tableHead}>
                <TableRow>
                  <TableCell>节点名称</TableCell>
                  <TableCell>节点位置</TableCell>
                  <TableCell>节点地址</TableCell>
                  <TableCell>主机类型</TableCell>
                  <TableCell>节点类型</TableCell>
                  <TableCell>节点状态</TableCell>
                  <TableCell>操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody className={styles.tableBody}>
                {data.map((row) => (
                  <React.Fragment key={row.node_info.id}>
                    <TableRow className={`${styles.rowHover} ${expandedKey === row.node_info.id ? styles.rowExpanded : ''}`}>
                      <TableCell className={styles.tableCell}>{renderCell(row.node_info.node_name)}</TableCell>
                      <TableCell className={styles.tableCell}>{renderCell(row.node_info.deployment_location)}</TableCell>
                      <TableCell className={styles.tableCell}>{renderCell(row.host_info.ip_address)}</TableCell>
                      <TableCell className={styles.tableCell}>{renderCell(row.host_info.operating_system)}</TableCell>
                      <TableCell className={styles.tableCell}>{renderCell(row.node_info.node_type)}</TableCell>
                      <TableCell className={styles.tableCell}>
                        {renderCell(row.node_info.node_status) === '－'
                          ? '－'
                          : (
                            <span className={row.node_info.node_status === 'online' ? styles.statusOnline : styles.statusOffline}>
                              {row.node_info.node_status === 'online' ? '在线' : '离线'}
                            </span>
                          )}
                      </TableCell>
                      <TableCell className={styles.tableCell}>
                        <span
                          className={styles.editBtn}
                          onClick={() => {
                            setEditingNode(row);
                            setEditNodeOpen(true);
                          }}
                        >
                          编辑
                        </span>
                        <span
                          className={styles.actionBtn}
                          onClick={() => handleExpandClick(row.node_info.id)}
                        >
                          {expandedKey === row.node_info.id ? '收起' : '展开'}
                        </span>
                        <span className={styles.deleteBtn} onClick={() => { setDeleteNodeId(row.node_info.id); setConfirmOpen(true); }}>删除</span>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={7}>
                        <Collapse in={expandedKey === row.node_info.id} timeout="auto" unmountOnExit>
                          <Box margin={1}>
                            <Typography variant="body2" component="div">
                              <ExpendCard
                                nodeItem={row}
                                honeypots={honeypotMap[row.node_info.id] || []}
                                honeypotLoading={!!honeypotLoading[row.node_info.id]}
                                handleGetData={handleGetData}
                                refreshHoneypots={() => getHoneypotsByNodeId(row.node_info.id)} // 新增
                              />
                            </Typography>
                          </Box>
                        </Collapse>
                      </TableCell>
                    </TableRow>
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </div>
          {/* <div className={styles.attack_list_pagination_div}>
            <Pagination
              className={styles.attack_list_pagination}
              component="div"
              page={page}
              onPageChange={handleChangePage}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              rowsPerPageOptions={[2, 5, 10, 25, 50, 100]}
              showFirstButton
              count={Math.ceil(total / rowsPerPage)}
              onChange={handleChangePage}
            />
          </div> */}
          <div className={styles.attack_list_pagination_div}>
            <div>共 {total} 条数据</div>
            <Pagination
              className={styles.attack_list_pagination}
              showFirstButton
              count={Math.ceil(total / rowsPerPage)}
              page={page}
              onChange={handleChangePage}
            />
          </div>
        </div>
        {/* </TableContainer > */}
      </div>
      {/* 新增节点弹窗 */}
      < AddNodeDialog
        open={addNodeOpen}
        onClose={() => setAddNodeOpen(false)
        }
        onSubmit={handleAddNode}
      />
      {/* 修改节点弹窗 */}
      <EditNodeDialog
        open={editNodeOpen}
        onClose={() => {
          setEditNodeOpen(false);
          setEditingNode(null);
        }}
        onSubmit={handleEditNode}
        initialData={editingNode}
      />
      {/* 删除节点弹窗 */}
      <Dialog
        open={confirmOpen}
        onClose={() => setConfirmOpen(false)}
      >
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <DialogContentText>
            确认要删除该节点吗？此操作不可恢复！
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button className={styles.noFocus} onClick={() => setConfirmOpen(false)} color="primary">
            取消
          </Button>
          <Button
            className={styles.noFocus}
            onClick={() => {
              if (deleteNodeId !== null) {
                handleDeleteNode(deleteNodeId);
              }
              setConfirmOpen(false);
              setDeleteNodeId(null);
            }}
            // color="secondary"
            color="primary"
          >
            确认
          </Button>
        </DialogActions>
      </Dialog>
    </div >

  );
}
