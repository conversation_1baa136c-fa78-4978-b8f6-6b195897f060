import { makeStyles } from "@material-ui/core";

const Styles = makeStyles((theme) => ({
    container: {
    display: 'flex',
    width: '100%',
    height: '100%',
  },
  left: {
    flex: '0 0 30%',
  },
  right: {
    flex: '0 0 70%',
  },
  dialog: {
    position: 'absolute',
    width: 1000,
    zIndex: 1300,
    pointerEvents: 'auto'
  },
  titleBar: {
    cursor: 'pointer',
    padding: '8px 0px',
    display: 'flex', alignItems: 'center'
  },
  icon: {
    width: '30px',
    height: '30px',
    marginBottom: theme.spacing(0.5),
  },
  messageList: {
    height: '480px',
    overflowY: 'auto',
    overflowX: 'hidden',
    padding: '10px',
    scrollbarWidth: 'thin',
    scrollbarColor: '#888 rgba(241, 241, 241, 0)',
  },
  messageUserText: {
    wordBreak: 'break-all',
    overflowWrap: 'break-word',
    whiteSpace: 'pre-wrap',
    background: 'linear-gradient(90deg, #107F41,rgb(103, 155, 65))',
    color: "white",
    borderRadius: "8px",
    padding: "8px 12px",
    maxWidth: "60%",
    justifyContent: "flex-end",
  },
  messageAiText: {
    wordBreak: 'break-all',
    overflowWrap: 'break-word',
    whiteSpace: 'pre-wrap',
    backgroundColor: "#EEEEEE",
    color: "#333",
    borderRadius: "8px",
    padding: "8px 12px",
    justifyContent: "flex-end",
  },
  user: {
    display: "flex",
    justifyContent: "flex-end",
    marginBottom: "10px",
  },
  ai: {
    display: "flex",
    justifyContent: "flex-start",
    marginBottom: "10px",
  }
}));
export default Styles;