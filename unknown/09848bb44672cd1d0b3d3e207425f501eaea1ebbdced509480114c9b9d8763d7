import React from 'react';
import {
  Paper,
  Typography,
  Box,
  Slider,
  FormControlLabel,
  Checkbox,
  Divider
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import EcoIcon from '@material-ui/icons/Eco';
import FlashOnIcon from '@material-ui/icons/FlashOn';
import SettingsIcon from '@material-ui/icons/Settings';
import FilterListIcon from '@material-ui/icons/FilterList';

const useStyles = makeStyles((theme) => ({
  root: {
    padding: 16,
    borderRadius: 12,
    // backgroundColor: '#f9fafb',
    display: 'flex',
    flexDirection: 'column',
    height: '100%', 
    overflow: 'hidden',
  },
  sectionTitle: {
    display: 'flex',
    alignItems: 'center',
    fontWeight: 600,
    color: '#065f46',
    marginTop: theme.spacing(2),
    marginBottom: theme.spacing(1),
  },
  icon: {
    marginRight: theme.spacing(1),
    color: '#10b981',
  },
  sliderLabel: {
    display: 'flex',
    justifyContent: 'space-between',
    fontSize: 12,
    color: '#6b7280',
    marginTop: -8,
    marginBottom: 8,
  },
  checkboxGroupOne: {
    marginTop: theme.spacing(1),
    marginBottom: theme.spacing(1),
  },
  checkboxGroup: {
    marginTop: theme.spacing(1),
    marginBottom: theme.spacing(1),
    display: 'flex',
    flexDirection: 'column', 
  },
}));

const ModelConfigPanel: React.FC = () => {
  const classes = useStyles();

  return (
    <Paper elevation={2} className={classes.root}>
      <Typography variant="h6" style={{ color: '#065f46', fontWeight: 'bold' }}>
        模型配置
      </Typography>

      <div
        style={{
          height: 2,
          backgroundColor: '#bbf7d0',
          width: '100%',
          marginTop: 12,
          marginBottom: 12,
          borderRadius: 4,
        }}
      />

      {/* 模型敏感度 */}
      <Typography className={classes.sectionTitle}>
        <EcoIcon className={classes.icon} />
        模型敏感度
      </Typography>
      <Slider defaultValue={50} step={10} min={0} max={100} />
      <div className={classes.sliderLabel}>
        <span>低精度</span>
        <span>平衡</span>
        <span>高精度</span>
      </div>

      {/* 解析速度 */}
      <Typography className={classes.sectionTitle}>
        <FlashOnIcon className={classes.icon} />
        解析速度
      </Typography>
      <Slider defaultValue={30} step={10} min={0} max={100} />
      <div className={classes.sliderLabel}>
        <span>快速</span>
        <span>均衡</span>
        <span>深度</span>
      </div>

      {/* 智能过滤 */}
      <Typography className={classes.sectionTitle}>
        <FilterListIcon className={classes.icon} />
        智能过滤
      </Typography>
      <Box className={classes.checkboxGroupOne}>
        <FormControlLabel control={<Checkbox defaultChecked />} label="显示安全事件" />
        <FormControlLabel control={<Checkbox defaultChecked />} label="显示错误信息" />
        <FormControlLabel control={<Checkbox defaultChecked />} label="显示警告信息" />
        <FormControlLabel control={<Checkbox />} label="显示普通信息" />
      </Box>

      {/* 高级选项 */}
      <Typography className={classes.sectionTitle}>
        <SettingsIcon className={classes.icon} />
        高级选项
      </Typography>
      <Box className={classes.checkboxGroup}>
        <FormControlLabel control={<Checkbox defaultChecked />} label="启用实体识别（IP/用户）" />
        <FormControlLabel control={<Checkbox />} label="威胁情报匹配" />
        <FormControlLabel control={<Checkbox />} label="事件关联分析" />
      </Box>
    </Paper>
  );
};

export default ModelConfigPanel;
