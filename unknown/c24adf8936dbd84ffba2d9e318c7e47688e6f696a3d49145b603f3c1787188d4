// src/pages/HoneyPotsOverviewBottom.tsx
import React, {  useEffect,useState } from 'react';
import './logtaskmanage.less'
import { Grid, InputAdornment } from '@material-ui/core';
import { IconButton } from '@material-ui/core';
import CloseIcon from '@material-ui/icons/Close';
import { Snackbar } from '@material-ui/core';
import MuiAlert, { AlertProps } from '@material-ui/lab/Alert';
import Search from '@material-ui/icons/Search';
import {
  makeStyles,
  Typography,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  MenuItem,
} from '@material-ui/core';
import LogAnalyseTable from './logAnalysetable';
import apiClient from '../apis/apiClient';
import { Add, Cached, SearchOutlined } from '@material-ui/icons';
import CreateTask from './createTask';
import TaskDetail from './taskDetail';
const useStyles = makeStyles((theme) => ({
  root: {
    height: '100%',
    //height: '94vh',
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: '#f5f5f5',
    padding: theme.spacing(0),
    boxSizing: 'border-box',
  },
  sectionMiddle: {
    // flex: 0.1,
    marginBottom: theme.spacing(1),
  },
  sectionBottom: {

    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
  },
  tableToolbar: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing(3),
  },
tableContainer: {
  flex: 1,
  maxHeight: '100%',
  minHeight: 0,
  backgroundColor: '#fff',
  display: 'flex',
  flexDirection: 'column',
  padding: theme.spacing(2),
},
customFocusButton: {
  '&:hover': {
    borderColor: '#000', // 鼠标悬浮时边框变黑
  },
  '&:focus': {
    outline: 'none !important',
    boxShadow: 'none !important',
  },
  '&.Mui-focusVisible': {
    outline: 'none !important',
    boxShadow: 'none !important',
  },
  '&:focus-visible': {
    outline: 'none !important',
    boxShadow: 'none !important',
  },
},
  formField: {
    marginBottom: theme.spacing(2),
  },
selectInput: {
  '& .MuiOutlinedInput-notchedOutline': {
    borderWidth: '1px',
  },
  '&:hover .MuiOutlinedInput-notchedOutline': {
    borderColor: '#ccc',
    borderWidth: '1px',
  },
  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
    borderColor: '#ccc !important',
    borderWidth: '1px',
  },
},
customOutlinedInput: {
    '& .MuiOutlinedInput-root': {
      '& fieldset': {
        borderColor: '#ccc', // 默认
        borderWidth: '1px',
      },
      '&:hover fieldset': {
        borderColor: '#000', // hover 黑色
        borderWidth: '1px',
      },
      '&.Mui-focused fieldset': {
        borderColor: '#000 !important', // focus 也黑色
        borderWidth: '1px',
      },
    },
  },
}));
type ConfigType = {
  id: number;
  config_name: string;
  config_type: string;
};

type FormDataType = {
  id: number;
  task_name: string;
  status: string;
  config_id: number;
  progress: number;
  threats_detected?: number; // 可选字段
  config?: ConfigType;       // 可选字段
  created_at: string;
  updated_at: string;
};

const defaultFormData: FormDataType = {
  id: 0,
  task_name: '',
  status: '',
  config_id: 0,
  progress: 0,
  created_at: '',
  updated_at: '',
};
const LogAlertAnalysisTask: React.FC = () => {
  const classes = useStyles();
  const [detailOpen, setDetailOpen]= useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [formData, setFormData] = useState<FormDataType>(defaultFormData);
  const [configList, setConfigList] = useState<ConfigOption[]>([]);
  const [detailData, setDetailData] = useState();
const [searchParams, setSearchParams] = useState({
  status: '',
  config_id: '',
});
const [actualSearchParams, setActualSearchParams] = useState({
  status: '',
  config_id: '',
});
const [confirmOpen, setConfirmOpen] = useState(false);
const [deleteTarget, setDeleteTarget] = useState<FormDataType | null>(null);
const [loading, setLoading] = useState(false);

  // ✅ 分页 + 数据状态
  const [page, setPage] = useState(0); // MUI 从 0 开始
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [taskList, setTaskList] = useState<any[]>([]);

type ConfigOption = {
  id: number;
  config_name: string;
};

const [snackbarOpen, setSnackbarOpen] = useState(false);
const [snackbarMessage, setSnackbarMessage] = useState('');
const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');
const showSnackbar = (message: string, severity: 'success' | 'error' = 'success') => {
  setSnackbarMessage(message);
  setSnackbarSeverity(severity);
  setSnackbarOpen(true);
};
const fetchTasks = async () => {
  setLoading(true);
  try {
    const res = await apiClient.get('/api/log-analysis/tasks', {
      params: {
        page: page + 1,
        per_page: pageSize,
        ...Object.fromEntries(
          Object.entries(actualSearchParams).filter(([_, v]) => v !== '')
        ),
      },
    });

    const { items, pagination } = res.data;
    setTaskList(items);
    setTotal(pagination.total);
  } catch (err) {
    console.error('获取任务列表失败', err);
  } finally {
    setLoading(false);
  }
};


const handleTaskAction = (row: FormDataType) => {
  if (row.status === 'running') {
    // 取消任务
    setDeleteTarget(row);
    setConfirmOpen(true);
  } else {
    // 启动任务
    handleStartTask(row.id);
  }
};

const handleStartTask = async (taskId: number) => {
  try {
    const res = await apiClient.post(`/api/log-analysis/tasks/${taskId}/start`);
    if (res.data.message === "任务启动成功") {
      showSnackbar('任务启动成功', 'success');
      fetchTasks(); // 刷新列表
    } else {
      showSnackbar('启动失败：' + res.data.message, 'error');
    }
  } catch (error: any) {
    console.error('启动任务失败:', error);
    showSnackbar('启动任务失败，请检查网络或稍后再试', 'error');
  }
};

const fetchConfigList = async () => {
  try {
    const res = await apiClient.get('/api/log-analysis/configs', {
      params: {
        page: 1,
        per_page: 100,
      },
    });

    const items = res.data.items || [];
    const configs: ConfigOption[] = items.map((item: any) => ({
      id: item.id,
      config_name: item.config_name,
    }));
    setConfigList(configs);
  } catch (err) {
    console.error('获取配置列表失败', err);
  }
};


useEffect(() => {
  fetchConfigList();
}, []);
useEffect(() => {
  fetchTasks();
}, [page, pageSize, actualSearchParams]);

  const getDetailData = async (id: number) => {
     try {
        const res = await apiClient.get(`/api/log-analysis/tasks/${id}`);
        const { task } = res.data;
        //showSnackbar('获取任务详情成功', 'success');
        setDetailData(task);
        setDetailOpen(true);
    } catch (error) {
      showSnackbar('获取任务详情失败，请检查网络或稍后再试', 'error');
    }
  }


  const handleOpenEditDialog =async (rowData: FormDataType) => {
    getDetailData(rowData.id)
    // setDetailOpen(true);
    setIsEditMode(true);
    setFormData({ ...rowData });
    setOpenDialog(true);
  };
  const handleCloseDetail = () => setDetailOpen(false)
const handleCancel = (row: any) => {
  setDeleteTarget(row);
  setConfirmOpen(true);
};

  return (
    <div className={classes.root}>

      {/* 表格 + 工具栏 */}
      <div className={classes.sectionBottom}>
        <div className={classes.tableContainer}>
          {/* 搜索 + 添加 */}
         {!detailOpen?<><div className={classes.tableToolbar}>
  {/* 左侧：状态 + 配置ID + 筛选按钮 */}
  <div style={{ display: 'flex', gap: 16, alignItems: 'center' }}>
   <TextField
  select
  label="状态"
  size="small"
  variant="outlined"
  value={searchParams.status}
  onChange={(e) =>
    setSearchParams((prev) => ({ ...prev, status: e.target.value }))
  }
  style={{ minWidth: 160 }}
>
  <MenuItem value="">全部</MenuItem>
  <MenuItem value="running">运行中</MenuItem>
  <MenuItem value="completed">已完成</MenuItem>
  <MenuItem value="pending">待处理</MenuItem>
</TextField>

<TextField
  select
  label="配置ID"
  size="small"
  variant="outlined"
  value={searchParams.config_id}
  onChange={(e) =>
    setSearchParams((prev) => ({ ...prev, config_id: e.target.value }))
  }
  style={{ minWidth: 160 }}
>
  <MenuItem value="">全部</MenuItem>
  {configList.map((config) => (
    <MenuItem key={config.id} value={config.id.toString()}>
      {config.config_name}
    </MenuItem>
  ))}
</TextField>

  <Button
  variant="outlined"
  className={classes.customFocusButton}
  color="default"
  startIcon={<Search />}
  onClick={() => {
    setPage(0); // 重置页码
    setActualSearchParams({ ...searchParams }); // 仅此时更新搜索条件
  }}
>
  筛选
</Button>
<Button
  variant="outlined"
  color="default"
    startIcon={
     <Cached  />  // ✅ 镜像图标
    }
  onClick={() => {
    const resetParams = { status: '', config_id: '' };
    setSearchParams(resetParams);
    setActualSearchParams(resetParams);
    setPage(0);
  }}
>
  重置
</Button>
  </div>
  {/* 右侧：添加按钮 */}
  <CreateTask fetchTasks={fetchTasks} />
</div>
          {/* 表格 */}
         <LogAnalyseTable
  data={taskList}
  page={page}
  pageSize={pageSize}
  total={total}
  onPageChange={setPage}
  onRowsPerPageChange={setPageSize}
  onEdit={handleOpenEditDialog}
  onAction={handleTaskAction}
  loading={loading}
/></>:<TaskDetail handleCloseDetail={handleCloseDetail} detailData={detailData} fetchTasks={fetchTasks} getDetailData={getDetailData} />}
        </div>
      </div>
<Dialog
  open={confirmOpen}
  onClose={() => setConfirmOpen(false)}
>
  <DialogTitle>
  确认取消
  <IconButton
    aria-label="close"
    onClick={() => setConfirmOpen(false)}
    style={{
      position: 'absolute',
      right: 8,
      top: 8,
      color: '#999',
    }}
  >
    <CloseIcon />
  </IconButton>
</DialogTitle>
  <DialogContent>
    <DialogContentText>
      确认要取消任务 “{deleteTarget?.task_name}” 吗？此操作不可恢复！
    </DialogContentText>
  </DialogContent>
  <DialogActions>
    <Button
      onClick={async () => {
        if (deleteTarget?.id) {
          try {
            const res = await apiClient.post(`/api/log-analysis/tasks/${deleteTarget.id}/cancel`);
            if (res.data.message === '任务已取消') {
              showSnackbar('任务取消成功', 'success');
              const isLastItemOnPage = taskList.length === 1;
              const isNotFirstPage = page > 0;
              if (isLastItemOnPage && isNotFirstPage) {
                setPage((prev) => prev - 1);
              } else {
                fetchTasks();
              }
            } else {
              showSnackbar('取消失败：' + res.data.message, 'error');
            }
          } catch (error) {
            console.error('取消任务失败:', error);
            showSnackbar('取消任务失败，请检查网络或稍后再试', 'error');
          }
        }
        setConfirmOpen(false);
        setDeleteTarget(null);
      }}
      color="secondary"
    >
      确认
    </Button>
  </DialogActions>
</Dialog>
<Snackbar
  open={snackbarOpen}
  autoHideDuration={3000}
  onClose={() => setSnackbarOpen(false)}
  anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
>
  <MuiAlert
    elevation={6}
    variant="filled"
    onClose={() => setSnackbarOpen(false)}
    severity={snackbarSeverity}
  >
    {snackbarMessage}
  </MuiAlert>
</Snackbar>

    </div>
  );
};
export default LogAlertAnalysisTask;
