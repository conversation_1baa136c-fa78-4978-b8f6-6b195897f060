import React from "react";
import {
  Box,
  Grid,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@material-ui/core";
import ReactECharts from "echarts-for-react";

interface AlarmData {
  id: string;
  time: string;
  severity: string;
  source: string;
  title: string;
  action: string;
  reason: string;
  confidence: number;
}

const alarmData: AlarmData[] = [
  {
    id: "50278f3e...",
    time: "Jan 15 10:30:40",
    severity: "HIGH",
    source: "web01",
    title: "apache Alert...",
    action: "MERGE",
    reason: "基于关键字判断建议合并",
    confidence: 0.3,
  },
  {
    id: "c4e047ac...",
    time: "Jan 15 10:34:15",
    severity: "MEDIUM",
    source: "db01",
    title: "mysql Alert...",
    action: "SUPPRESS",
    reason: "基于关键字判断建议抑制",
    confidence: 0.3,
  },
];

const AlarmAnalysis: React.FC = () => {
  const now = new Date().toLocaleString("zh-CN", { hour12: false });

  const pieChartOptions = {
    title: {
      text: "告警严重程度分布",
      left: "center",
      textStyle: { color: "#4CAF50" },
    },
    legend: {
      bottom: 0,
      data: ["高", "中", "低"],
    },
    color: ["#F06292", "#FFD54F", "#81D4FA"],
    series: [
      {
        type: "pie",
        radius: "60%",
        data: [
          { value: 1, name: "高" },
          { value: 1, name: "中" },
          { value: 0, name: "低" },
        ],
      },
    ],
  };

  const barChartOptions = {
    title: {
      text: "告警处理状态分布",
      left: "center",
      textStyle: { color: "#4CAF50" },
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      bottom: 0,
      data: ["已处理", "未处理", "合并", "抑制"],
    },
    xAxis: {
      type: "category",
      data: ["已处理", "未处理", "合并", "抑制"],
      axisTick: { alignWithLabel: true },
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        name: "告警数量",
        type: "bar",
        data: [2, 0, 1, 1],
        itemStyle: {
          color: function (params: any) {
            const colorList = ["#4CAF50", "#EF5350", "#FFA726", "#29B6F6"];
            return colorList[params.dataIndex];
          },
        },
      },
    ],
  };

  const donutChartOptions = {
    title: {
      text: "降噪比例",
      left: "center",
      textStyle: { color: "#4CAF50" },
    },
    legend: {
      bottom: 0,
      data: ["降噪", "未降噪"],
    },
    color: ["#4CAF50", "#EF5350"],
    series: [
      {
        type: "pie",
        radius: ["40%", "70%"],
        data: [
          { value: 2, name: "降噪" },
          { value: 0, name: "未降噪" },
        ],
      },
    ],
  };

  const stats = [
    { label: "总告警数", value: 2, icon: "🔔" },
    { label: "已处理告警", value: 2, icon: "✅" },
    { label: "抑制告警", value: 1, icon: "🛑" },
    { label: "合并告警", value: 1, icon: "🔀" },
    { label: "优先级调整", value: 0, icon: "⚙️" },
    { label: "降噪比例", value: "100%", icon: "%" },
    { label: "告警类别数", value: 0, icon: "📊" },
  ];

  return (
    <Box
      style={{
        padding: 16,
        minHeight: "100vh",
        overflowX: "auto",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* 标题 */}
      <Typography
        variant="h5"
        align="center"
        style={{ marginBottom: 24, color: "#4CAF50", fontWeight: "bold" }}
      >
        告警降噪分析报告
      </Typography>

      {/* 概览卡片 */}
      <Box>
        <Grid
          container
          spacing={2}
          wrap="nowrap"
          justify="center"
          style={{ minWidth: "max-content" }}
        >
          {stats.map((stat, index) => (
            <Grid item style={{ minWidth: 150 }} key={index}>
              <Paper
                elevation={2}
                style={{
                  padding: 16,
                  textAlign: "center",
                  backgroundColor: "#E8F5E9",
                }}
              >
                <Typography variant="h6" style={{ color: "#4CAF50" }}>
                  {stat.icon}
                </Typography>
                <Typography variant="subtitle1" style={{ color: "#4CAF50" }}>
                  {stat.label}
                </Typography>
                <Typography variant="h6" style={{ fontWeight: "bold" }}>
                  {stat.value}
                </Typography>
              </Paper>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* 图表区域 */}
      <Grid container spacing={3} style={{ marginTop: 24 }}>
        <Grid item xs={12} md={4}>
          <ReactECharts option={pieChartOptions} style={{ height: 300 }} />
        </Grid>
        <Grid item xs={12} md={4}>
          <ReactECharts option={barChartOptions} style={{ height: 300 }} />
        </Grid>
        <Grid item xs={12} md={4}>
          <ReactECharts option={donutChartOptions} style={{ height: 300 }} />
        </Grid>
      </Grid>

      {/* 表格标题 */}
      <Typography
        variant="h6"
        style={{
          marginTop: 32,
          marginBottom: 8,
          color: "#4CAF50",
          fontWeight: "bold",
        }}
      >
        告警分析结果
      </Typography>

      {/* 表格 */}
      <Paper elevation={3} style={{ overflow: "auto" }}>
        <TableContainer>
          <Table style={{ minWidth: 800 }} size="small">
            <TableHead>
              <TableRow style={{ backgroundColor: "#E8F5E9" }}>
                {[
                  "告警ID",
                  "时间戳",
                  "严重程度",
                  "来源",
                  "标题",
                  "降噪动作",
                  "原因",
                  "可信度",
                ].map((header) => (
                  <TableCell
                    key={header}
                    style={{ fontWeight: "bold", border: "1px solid #ccc" }}
                  >
                    {header}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {alarmData.map((alarm) => (
                <TableRow key={alarm.id}>
                  <TableCell style={{ border: "1px solid #ccc" }}>
                    {alarm.id}
                  </TableCell>
                  <TableCell style={{ border: "1px solid #ccc" }}>
                    {alarm.time}
                  </TableCell>
                  <TableCell
                    style={{
                      color: alarm.severity === "HIGH" ? "red" : "orange",
                      fontWeight: "bold",
                      border: "1px solid #ccc",
                    }}
                  >
                    {alarm.severity}
                  </TableCell>
                  <TableCell style={{ border: "1px solid #ccc" }}>
                    {alarm.source}
                  </TableCell>
                  <TableCell style={{ border: "1px solid #ccc" }}>
                    {alarm.title}
                  </TableCell>
                  <TableCell
                    style={{
                      color: alarm.action === "MERGE" ? "green" : "red",
                      fontWeight: "bold",
                      border: "1px solid #ccc",
                    }}
                  >
                    {alarm.action}
                  </TableCell>
                  <TableCell style={{ border: "1px solid #ccc" }}>
                    {alarm.reason}
                  </TableCell>
                  <TableCell style={{ border: "1px solid #ccc" }}>
                    {alarm.confidence.toFixed(2)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* 报告生成时间 */}
      <Typography
        style={{
          marginTop: 6,
          marginBottom: 25,
          fontSize: 14,
          color: "#333",
        }}
      >
        报告生成时间：{now}
      </Typography>
    </Box>
  );
};

export default AlarmAnalysis;
