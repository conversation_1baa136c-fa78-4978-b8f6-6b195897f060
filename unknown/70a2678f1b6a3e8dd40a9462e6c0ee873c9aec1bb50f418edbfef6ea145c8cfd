import { Button, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, Snackbar } from "@material-ui/core";
import MuiAlert, { AlertProps } from "@material-ui/lab/Alert";
import { useState } from "react";
import { showSnackbar } from "../points-manage/component/myMessageBar";


export default function DataAccessManageDelete({name}:{name:string}) {
    const [open, setOpen] = useState(false);
    const [alertOpen, setAlertOpen] = useState(false);
    const handleOnclick = () => {
        setOpen(true);
    }
    function Alert(props: AlertProps) {
        return <MuiAlert elevation={6} variant="filled" {...props} />;
    }
    const handleClose = (event?: React.SyntheticEvent, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    setAlertOpen(false);
  };
    return (
        <>
        <Button style={{border: '1px solid #E0E0E0',marginLeft: 10,fontSize: '16px' }} onClick={handleOnclick}>
            删除
        </Button>
        <Dialog open={open}>
            <DialogTitle>确认删除</DialogTitle>
            <DialogContent>
              <DialogContentText>
                确认要删除日志源“{name}”吗？此操作不可恢复！
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button  color="primary" onClick={() => setOpen(false)}>
                取消
              </Button>
              <Button
                color="secondary" onClick={() => {setOpen(false),showSnackbar("删除成功", "success")}}
              >
                确认
              </Button>
            </DialogActions>
        </Dialog>
        </>
    )
}

