import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>alogA<PERSON>, DialogContent, DialogTitle, Grid, MenuItem, TextField } from "@material-ui/core";
import React, { useState, useEffect } from "react";
import { showSnackbar } from "../points-manage/component/myMessageBar";
interface IProps {
    row:{
      src_name: string
      src_type_label: string
      src_ip: string
      src_port: string
      src_status_label: string
      last_active_time: string
    }
  }



export default function UpdateLogSource({row}: IProps) {
    const defaultForm = {
    src_name: row.src_name,
    src_type: row.src_type_label==='IDS'?'1':row.src_type_label==='防火墙'?'2':'3',
    src_ip: row.src_ip,
    src_port: row.src_port,
    src_status: row.src_status_label==='正常'?'1':'0',
    };
    const [open, setOpen] = useState(false);
    const onClose = () => {setO<PERSON>(false)};
    const onSubmit = (data: any) => {
        console.log(data);
        showSnackbar("更新成功", "success");
        setOpen(false);
    };
    const [form, setForm] = useState(defaultForm);
    useEffect(() => {
        if (!open) setForm(defaultForm);
    }, [open]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setForm((prev) => ({ ...prev, [name]: value }));
    };

    const handleConfirm = () => {
        if (!form.src_name || !form.src_type || !form.src_ip || !form.src_port || !form.src_status) {
              showSnackbar("请填写必填项", "error");
              return;
            }
        let submitData = {
            ...form,
        };
        onSubmit(submitData);
    };

    return (
        <>
        <Button style={{border: '1px solid #E0E0E0',marginRight: 10,fontSize: '16px'}} onClick={() => setOpen(true)}>配置</Button>
        <Dialog open={open} onClose={onClose}  maxWidth="sm" fullWidth>
            <DialogTitle>更新日志源</DialogTitle>
            <DialogContent>
                <Grid container spacing={2}>
                    <Grid item xs={6}>
                        <TextField label="日志源名称" name="src_name" value={form.src_name} onChange={handleChange} margin="dense" required  fullWidth />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField select label="日志源类型" name="src_type" value={form.src_type} onChange={handleChange} margin="dense" fullWidth  required >
                            <MenuItem value="1">IDS</MenuItem>
                            <MenuItem value="2">防火墙</MenuItem>
                            <MenuItem value="3">服务器</MenuItem>
                        </TextField>
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="日志源IP" name="src_ip" value={form.src_ip} onChange={handleChange} margin="dense" fullWidth placeholder="如 ************" required/>
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="日志源端口" name="src_port" value={form.src_port} onChange={handleChange} fullWidth margin="dense" placeholder="如 44321" required/>
                    </Grid>
                    <Grid item xs={6}>
                        <TextField select label="日志源状态" name="src_status" value={form.src_status} onChange={handleChange} fullWidth margin="dense" required>
                            <MenuItem value="0">警告</MenuItem>
                            <MenuItem value="1">正常</MenuItem>
                        </TextField>
                    </Grid>
                </Grid>
            </DialogContent>
            <DialogActions>
                <Button  onClick={onClose}>取消</Button>
                <Button  onClick={handleConfirm} variant="contained" color="primary">
                    确认
                </Button>
            </DialogActions>
        </Dialog>
        </>
    );
}
