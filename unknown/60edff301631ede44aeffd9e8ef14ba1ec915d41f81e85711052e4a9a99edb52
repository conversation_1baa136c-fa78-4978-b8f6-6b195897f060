import React, { useState } from 'react';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Breadcrumbs,
  Link,
} from '@mui/material';
import VisibilityIcon from '@mui/icons-material/Visibility';
import DownloadIcon from '@mui/icons-material/Download';
import { ReportItem } from './types';
import AlarmAnalysis from './components/AlarmAnalysis'; 

const mockReports: ReportItem[] = [
  {
    id: '1',
    name: '2023-10-01_安全日志分析报告',
    generatedAt: '2023-10-01T14:30:00',
  },
  {
    id: '2',
    name: '2023-09-28_系统异常报告',
    generatedAt: '2023-09-28T09:15:00',
  },
];

const formatDateTime = (isoString: string): string => {
  const date = new Date(isoString);
  return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
};

const ReportGenerate: React.FC = () => {
  const [showAnalysis, setShowAnalysis] = useState(false);

  const handleGenerateReport = () => {
    setShowAnalysis(true);
  };

  if (showAnalysis) {
    return <AlarmAnalysis />; // 点击按钮后显示 AlarmAnalysis 页面
  }

  return (
    <Box p={2}>
      {/* Title */}
      <Typography variant="h5" color="success" fontWeight="bold" gutterBottom>
        智能报告生成
      </Typography>
      {/* Breadcrumbs */}
      <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
        <Link underline="hover" color="inherit" href="#">
          智能降噪中心
        </Link>
        <Typography color="text.primary">智能报告生成</Typography>
      </Breadcrumbs>

      {/* Action Buttons */}
      <Box display="flex" gap={2} mb={2}>
        <Button variant="contained" color="success" onClick={handleGenerateReport}>
          生成智能报告
        </Button>
        <Button variant="contained" color="success">
          导出报告
        </Button>
      </Box>

      {/* Show Report Table */}
      <Card>
        <CardContent>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>报告名称</TableCell>
                  <TableCell>生成时间</TableCell>
                  <TableCell align="center">操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {mockReports.map((report) => (
                  <TableRow key={report.id}>
                    <TableCell>{report.name}</TableCell>
                    <TableCell>{formatDateTime(report.generatedAt)}</TableCell>
                    <TableCell align="center">
                      <IconButton color="success" aria-label="view">
                        <VisibilityIcon />
                      </IconButton>
                      <IconButton aria-label="download" color="success">
                        <DownloadIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ReportGenerate;
