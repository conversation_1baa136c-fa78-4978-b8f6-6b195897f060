.expend_card { 
    display: flex;
    justify-content: space-around;
    background-color: white;
    height: 100%;
    padding: 20px 0px;
    // gap:30px
}

.expend_card > div { 
    flex: 0.32;
}

.message_title{
    height: 40px;
    font-size: 17px;
    align-items: center;
    font-weight: 600;
    width: 100%;
    display: flex;

}

.message_title >div:first-child{
    text-align: right;
}

.message_item {
  display: flex;
  align-items: flex-start;
  width: 100%;
  min-height: 36px;
  flex-wrap: wrap;
}

.message_item > div:first-child {
  text-align: right;
  font-weight: 600;
}

.message_item > div:last-child {
  text-align: left;
}

.have_edit {
  display: flex;
  gap: 5px;
}

.edit_icon{
  cursor: pointer;
  margin-top: -2px;
}

.honeypo_services_status {
  width: 100%;
  border: 1px solid #e2dddd;
}

.honeypo_services_status > div { 
  width: 100%;
  padding: 10px 0px;
  border-bottom: 1px solid #e2dddd;
  text-align: center;
}

.honeypo_services_status_item {
  display: flex;
  align-items: center;
  background-color: #fafafb;
}

.honeypo_services_status_item>div {
  text-align: left;
}

.honeypo_services_status_item>div:nth-child(1) {
  flex: 0.4;
  padding-left: 15px;
}

.honeypo_services_status_item>div:nth-child(2) {
  flex: 0.3;
}

.honeypo_services_status_item>div:nth-child(3) {
  flex: 0.2;
}

.honeypo_services_status_item>div:nth-child(4) {
  flex: 0.1;
}

.add_honeypo_services_bar {
  background-color: #fafafb;
  cursor: pointer;
}
.add_honeypo_services_bar:hover {
  background-color: #e2dddd;
}