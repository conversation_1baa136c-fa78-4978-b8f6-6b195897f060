import { Button, <PERSON>alog, DialogActions, DialogContent, DialogContentText, DialogTitle, Grid, MenuItem, Snackbar, TextField } from "@material-ui/core";
import { useState } from "react";
interface IProps {
    row:{
      src_name: string
      src_type_label: string
      src_ip: string
      src_port: string
      src_status_label: string
      last_active_time: string
    }
  }

export default function DataAccessManageDetail({row}: IProps) {
    const [open, setOpen] = useState(false);
    console.log(row)
    const handleOnclick = () => {
        setOpen(true);
    }
    const handleConfirm = () => {
        setOpen(false);
    }

    return (
        <>
        <Button style={{border: '1px solid #E0E0E0',marginLeft: 10,fontSize: '16px' }} onClick={handleOnclick}>
            详情
        </Button>
        <Dialog open={open}   maxWidth="sm" fullWidth >
            <DialogTitle>日志源详情</DialogTitle>
            <DialogContent >
                <Grid container spacing={2}>
                    <Grid item xs={12}>
                        <TextField label="日志源名称" name="src_name" fullWidth InputProps={{readOnly: true}} defaultValue={row.src_name} />
                    </Grid>
                    <Grid item xs={12}>
                        <TextField  label="类型" name="src_type" fullWidth   InputProps={{readOnly: true}} defaultValue={row.src_type_label} />
                    </Grid>
                    <Grid item xs={12}>
                        <TextField label="IP" name="src_ip" fullWidth InputProps={{readOnly: true}} defaultValue={row.src_ip} />
                    </Grid>
                    <Grid item xs={12}>
                        <TextField label="端口" name="src_port" InputProps={{readOnly: true}} fullWidth defaultValue={row.src_port} />
                    </Grid>
                    <Grid item xs={12}>
                        <TextField  label="状态" name="src_status"  fullWidth margin="dense" InputProps={{readOnly: true}} defaultValue={row.src_status_label} />
                    </Grid>
                    <Grid item xs={12}>
                        <TextField  label="最后活跃时间" name="src_status"  fullWidth margin="dense" InputProps={{readOnly: true}} defaultValue={row.last_active_time} />
                    </Grid>
                </Grid>
            </DialogContent>
            <DialogActions>
                 <Button  onClick={handleConfirm} variant="contained" color="primary">
                     关闭
                 </Button>
            </DialogActions>
        </Dialog>
        </>
    )
}

