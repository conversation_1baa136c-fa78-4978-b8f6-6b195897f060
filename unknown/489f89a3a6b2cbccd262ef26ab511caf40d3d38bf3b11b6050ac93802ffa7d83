import React from 'react';
import { makeStyles } from '@material-ui/core/styles';
import { 
  Box, 
  Select, 
  MenuItem, 
  TextField 
} from '@material-ui/core';

const useStyles = makeStyles((theme) => ({
  root: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-end',
    '& > * + *': {
      marginLeft: theme.spacing(2),
    },
    padding: theme.spacing(0, 0),
  },
  select: {
    minWidth: 120,
    '& .MuiSelect-select': {
      padding: '11px 12px',
    },
  },
  dateField: {
    width: 200,
  },
  flexContainer: {
    display: 'flex',
    alignItems: 'center',
    '& > * + *': {
      marginLeft: theme.spacing(1),
    },
  }
}));

const SceneSelector: React.FC = () => {
  const classes = useStyles();
  const [scene, setScene] = React.useState('全部');
  const [startDate, setStartDate] = React.useState('2025-05-16T00:00');
  const [endDate, setEndDate] = React.useState('2025-05-17T23:59');

  return (
    <Box className={classes.root}>
      <Box className={classes.flexContainer}>
        <span>蜜罐场景：</span>
        <Select
          value={scene}
          onChange={(e) => setScene(e.target.value as string)}
          className={classes.select}
          variant="outlined"
        >
          <MenuItem value="全部">全部</MenuItem>
          <MenuItem value="场景1">场景1</MenuItem>
          <MenuItem value="场景2">场景2</MenuItem>
        </Select>
      </Box>
      <Box className={classes.flexContainer}>
        <TextField
          type="datetime-local"
          value={startDate}
          onChange={(e) => setStartDate(e.target.value)}
          className={classes.dateField}
          size="small"
          variant="outlined"
        />
        <span>→</span>
        <TextField
          type="datetime-local"
          value={endDate}
          onChange={(e) => setEndDate(e.target.value)}
          className={classes.dateField}
          size="small"
          variant="outlined"
        />
      </Box>
    </Box>
  );
};

export default SceneSelector; 