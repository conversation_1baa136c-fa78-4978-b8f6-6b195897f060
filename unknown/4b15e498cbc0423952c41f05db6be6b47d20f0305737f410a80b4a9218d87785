import React from 'react';
import styled from '@emotion/styled';
import { Box, Typography } from '@material-ui/core';

const GaugeContainer = styled(Box)`
  position: relative;
  width: 160px;
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 20px auto;
`;

const StyledSVG = styled.svg`
  width: 160px;
  height: 90px;
`;

const ValueDisplay = styled(Typography)`
  position: absolute;
  bottom: 0;
  font-size: 20px !important;
  font-weight: bold !important;
  color: #000;
  transform: translateY(24px);
`;

interface GaugeProps {
  current: number;
  total: number;
}

const Gauge: React.FC<GaugeProps> = ({ current, total }) => {
  const progress = Math.min(current / total, 1);
  const radius = 70;
  const centerX = 80;
  const centerY = 80;
  
  // 计算圆弧路径
  const getArcPath = (startAngle: number, endAngle: number) => {
    const start = polarToCartesian(centerX, centerY, radius, startAngle);
    const end = polarToCartesian(centerX, centerY, radius, endAngle);
    const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";
    
    return [
      "M", start.x, start.y,
      "A", radius, radius, 0, largeArcFlag, 1, end.x, end.y
    ].join(" ");
  };
  
  // 极坐标转笛卡尔坐标
  const polarToCartesian = (centerX: number, centerY: number, radius: number, angleInDegrees: number) => {
    const angleInRadians = ((angleInDegrees - 180) * Math.PI) / 180.0;
    return {
      x: centerX + (radius * Math.cos(angleInRadians)),
      y: centerY + (radius * Math.sin(angleInRadians))
    };
  };

  // 计算进度角度
  const progressAngle = 180 * progress;

  return (
    <GaugeContainer>
      <StyledSVG viewBox="0 0 160 90">
        {/* 背景圆弧 */}
        <path
          d={getArcPath(0, 180)}
          fill="none"
          stroke="#e0e0e0"
          strokeWidth="8"
          strokeLinecap="round"
        />
        {/* 进度圆弧 */}
        <path
          d={getArcPath(0, progressAngle)}
          fill="none"
          stroke="#2196f3"
          strokeWidth="8"
          strokeLinecap="round"
        />
        {/* 底部圆点 */}
        <circle
          cx={centerX}
          cy={centerY}
          r="4"
          fill="#e0e0e0"
        />
      </StyledSVG>
      <ValueDisplay variant="h6">
        {current}/{total}
      </ValueDisplay>
    </GaugeContainer>
  );
};

export default Gauge; 