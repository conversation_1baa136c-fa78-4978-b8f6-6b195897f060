// 模拟数据
// export const mockResponse = {
//   items: [
//     {
//       node_info: {
//         id: 1,
//         node_name: "client001",
//         node_status: "online",
//         created_at: "2024-05-02T08:16:23Z",
//         updated_at: "2024-05-06T14:43:11Z",
//         deployed_at: "2024-05-02T09:10:05Z",
//         deployment_location: "黑龙江哈尔滨",
//         node_type: "Linux",
//         traffic: [11, 13, 9, 17, 12, 15, 14]
//       },
//       host_info: {
//         operating_system: "Ubuntu 20.04",
//         architecture: "x86_64",
//         timezone: "Asia/Shanghai",
//         ip_address: "*************",
//         subnet_mask: "*************",
//         dns_servers: "***************,*******",
//         mac_address: "00:16:3e:5a:9c:01",
//         hardware_config: "4C8G",
//         host_type: "物理机"
//       }
//     },
//     {
//       node_info: {
//         id: 2,
//         node_name: "client002",
//         node_status: "offline",
//         created_at: "2024-05-03T10:22:41Z",
//         updated_at: "2024-05-07T09:31:27Z",
//         deployed_at: "2024-05-03T11:00:12Z",
//         deployment_location: "黑龙江齐齐哈尔",
//         node_type: "Linux",
//         traffic: [2, 3, 1, 4, 2, 0, 1]
//       },
//       host_info: {
//         operating_system: "Windows Server 2016",
//         architecture: "x86_64",
//         timezone: "Asia/Shanghai",
//         ip_address: "************",
//         subnet_mask: "*************",
//         dns_servers: "*********,*******",
//         mac_address: "00:16:3e:5a:9c:02",
//         hardware_config: "8C16G",
//         host_type: "虚拟机"
//       }
//     },
//     {
//       node_info: {
//         id: 3,
//         node_name: "client003",
//         node_status: "online",
//         created_at: "2024-05-04T13:45:38Z",
//         updated_at: "2024-05-08T07:58:19Z",
//         deployed_at: "2024-05-04T14:20:44Z",
//         deployment_location: "黑龙江哈尔滨",
//         node_type: "Linux",
//         traffic: [7, 12, 10, 16, 14, 8, 11]
//       },
//       host_info: {
//         operating_system: "CentOS 7.6",
//         architecture: "arm64",
//         timezone: "Asia/Shanghai",
//         ip_address: "************",
//         subnet_mask: "*************",
//         dns_servers: "*******,***************",
//         mac_address: "00:16:3e:5a:9c:03",
//         hardware_config: "16C32G",
//         host_type: "物理机"
//       }
//     }
//   ],
//   pagination: {
//     page: 1,
//     per_page: 10,
//     total: 3,
//     pages: 1
//   }
// };
