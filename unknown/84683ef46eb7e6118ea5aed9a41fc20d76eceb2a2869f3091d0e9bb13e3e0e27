// AddNodeDialog.tsx
import { <PERSON>ton, <PERSON>alog, DialogActions, DialogContent, DialogTitle, Grid, MenuItem, TextField } from "@material-ui/core";
import React, { useState, useEffect } from "react";
import styles from "../less/addNodeDialog.module.less";
interface AddNodeDialogProps {
    open: boolean;
    onClose: () => void;
    onSubmit: (data: any) => void;
}

const defaultForm = {
    node_name: null,
    ip_address: null,
    mac_address: null,
    node_status: "online",
    node_type: null,
    deployment_location: null,
    operating_system: null,
    architecture: null,
    timezone: "Asia/Shanghai",
    subnet_mask: null,
    dns_servers: null,
    cpu: null,
    ram: null,
    department: null,
    deployed_at: null,
};

export default function AddNodeDialog(props: AddNodeDialogProps) {
    const { open, onClose, onSubmit } = props;
    const [form, setForm] = useState(defaultForm);

    useEffect(() => {
        if (!open) setForm(defaultForm);
    }, [open]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setForm((prev) => ({ ...prev, [name]: value }));
    };

    const handleConfirm = () => {
        let submitData = {
            ...form,
            hardware_config: buildHardwareConfig(form.cpu, form.ram),
        };
        onSubmit(submitData);
    };
    function buildHardwareConfig(cpu: string | null, ram: string | null) {
        // 如果都为空，返回空字符串
        if (!cpu && !ram) return null;
        return JSON.stringify({ cpu, ram });
    }
    return (
        <Dialog open={open} onClose={onClose} className={styles.dialog} maxWidth="sm" fullWidth>
            <DialogTitle>新增节点</DialogTitle>
            {/* <DialogContent>
                <TextField label="节点名称" name="node_name" value={form.node_name} onChange={handleChange} fullWidth margin="dense" required />
                <TextField label="IP地址" name="ip_address" value={form.ip_address} onChange={handleChange} fullWidth margin="dense" placeholder="如 ************" />
                <TextField label="MAC地址" name="mac_address" value={form.mac_address} onChange={handleChange} fullWidth margin="dense" placeholder="如 A3:B4:C5:D6:E7:F8" />
                <TextField
                    select
                    label="节点状态"
                    name="node_status"
                    value={form.node_status}
                    onChange={handleChange}
                    fullWidth
                    margin="dense"
                >
                    <MenuItem value="online">在线</MenuItem>
                    <MenuItem value="offline">离线</MenuItem>
                </TextField>
                <TextField label="节点类型" name="node_type" value={form.node_type} onChange={handleChange} fullWidth margin="dense" />
                <TextField label="部署位置" name="deployment_location" value={form.deployment_location} onChange={handleChange} fullWidth margin="dense" />
                <TextField
                    label="部署时间"
                    name="deployed_at"
                    type="datetime-local"
                    value={form.deployed_at || ""}
                    onChange={handleChange}
                    fullWidth
                    margin="dense"
                    InputLabelProps={{
                        shrink: true,
                    }}
                />
                <TextField label="操作系统" name="operating_system" value={form.operating_system} onChange={handleChange} fullWidth margin="dense" />
                <TextField label="架构" name="architecture" value={form.architecture} onChange={handleChange} fullWidth margin="dense" />
                <TextField label="时区" name="timezone" value={form.timezone} onChange={handleChange} fullWidth margin="dense" placeholder="如 Asia/Shanghai" />
                <TextField label="子网掩码" name="subnet_mask" value={form.subnet_mask} onChange={handleChange} fullWidth margin="dense" placeholder="如 255.255.255.0" />
                <TextField
                    label="DNS服务器(逗号分隔)"
                    name="dns_servers"
                    value={form.dns_servers}
                    onChange={handleChange}
                    fullWidth
                    margin="dense"
                    placeholder='如 ["8.8.8.8","1.1.1.1"]'
                />
                <TextField
                    label="CPU核心数"
                    name="cpu"
                    type="number"
                    value={form.cpu}
                    onChange={handleChange}
                    fullWidth
                    margin="dense"
                    placeholder="如 4"
                    inputProps={{ min: 1, step: 1 }} // 只允许正整数
                />
                <TextField
                    label="内存大小(GB)"
                    name="ram"
                    type="number"
                    value={form.ram}
                    onChange={handleChange}
                    fullWidth
                    margin="dense"
                    placeholder="如 1.5"
                    inputProps={{ min: 0.1, step: 0.1 }} // 支持一位小数
                />

                <TextField label="所属部门" name="department" value={form.department} onChange={handleChange} fullWidth margin="dense" />
            </DialogContent> */}
            <DialogContent>
                <Grid container spacing={2}>
                    <Grid item xs={6}>
                        <TextField label="节点名称" name="node_name" value={form.node_name} onChange={handleChange} margin="dense" required  fullWidth/>
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="IP地址" name="ip_address" value={form.ip_address} onChange={handleChange} margin="dense" fullWidth placeholder="如 ************" />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="MAC地址" name="mac_address" value={form.mac_address} onChange={handleChange} margin="dense" fullWidth placeholder="如 A3:B4:C5:D6:E7:F8" />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField
                            select
                            label="节点状态"
                            name="node_status"
                            value={form.node_status}
                            onChange={handleChange}
                            margin="dense"
                            fullWidth
                        >
                            <MenuItem value="online">在线</MenuItem>
                            <MenuItem value="offline">离线</MenuItem>
                        </TextField>
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="节点类型" name="node_type" value={form.node_type} onChange={handleChange} margin="dense" fullWidth />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="部署位置" name="deployment_location" value={form.deployment_location} onChange={handleChange} fullWidth margin="dense" />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField
                            label="部署时间"
                            name="deployed_at"
                            type="datetime-local"
                            value={form.deployed_at || ""}
                            onChange={handleChange}
                            margin="dense"
                            fullWidth
                            InputLabelProps={{
                                shrink: true,
                            }}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="操作系统" name="operating_system" value={form.operating_system} onChange={handleChange} fullWidth margin="dense" />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="架构" name="architecture" value={form.architecture} onChange={handleChange}fullWidth margin="dense" />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="时区" name="timezone" value={form.timezone} onChange={handleChange} margin="dense" fullWidth placeholder="如 Asia/Shanghai" />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="子网掩码" name="subnet_mask" value={form.subnet_mask} onChange={handleChange} margin="dense" fullWidth placeholder="如 255.255.255.0" />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField
                            label="DNS服务器(逗号分隔)"
                            name="dns_servers"
                            value={form.dns_servers}
                            onChange={handleChange}
                            margin="dense"
                            fullWidth
                            placeholder='如 ["8.8.8.8","1.1.1.1"]'
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField
                            label="CPU核心数"
                            name="cpu"
                            type="number"
                            value={form.cpu}
                            onChange={handleChange}
                            margin="dense"
                            placeholder="如 4"
                            fullWidth
                            inputProps={{ min: 1, step: 1 }}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField
                            label="内存大小(GB)"
                            name="ram"
                            type="number"
                            value={form.ram}
                            onChange={handleChange}
                            margin="dense"
                            placeholder="如 1.5"
                            fullWidth
                            inputProps={{ min: 0.1, step: 0.1 }}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="所属部门" name="department" value={form.department} onChange={handleChange} fullWidth margin="dense" />
                    </Grid>
                </Grid>
            </DialogContent>
            <DialogActions>
                <Button className={styles.noFocus} onClick={onClose}>取消</Button>
                <Button className={styles.noFocus} onClick={handleConfirm} variant="contained" color="primary">
                    确认
                </Button>
            </DialogActions>
        </Dialog>
    );
}
