import React, { useEffect, useState } from 'react';
import {
  Box,
  Grid,
  Paper,
  Button,
  makeStyles,
  Theme,
  createStyles,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Snackbar,
  Typography,
} from '@material-ui/core';
import ChatIcon from '@material-ui/icons/Chat';
import CloseIcon from '@material-ui/icons/Close';
import ReactEcharts from 'echarts-for-react';

const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    root: { width: '100%', minHeight: '100vh', background: '#f5f7fa',paddingBottom: 50,  },
    section: { margin: theme.spacing(2, 0) },
    card: { padding: theme.spacing(2), borderRadius: 8, boxShadow: '0 2px 10px rgba(0,0,0,0.1)', background: '#fff' },
    metricHeader: { display: 'flex', justifyContent: 'space-between', alignItems: 'center' },
    metricValue: { fontSize: 32, fontWeight: 700, color: '#1e8449', margin: theme.spacing(1, 0) },
    detailLabel: { borderRadius: 10, padding: '2px 8px', fontSize: 12, fontWeight: 500 },
    high: { background: 'rgba(231,76,60,0.1)', color: '#e74c3c' },
    medium: { background: 'rgba(243,156,18,0.1)', color: '#f39c12' },
    low: { background: 'rgba(52,152,219,0.1)', color: '#3498db' },
    chatbotBtn: { position: 'fixed', bottom: 500, right: 32, zIndex: 1000, background: '#1e8449', color: '#fff', '&:hover': { background: '#186a3b' } },
    dialogPaper: { borderRadius: 12 },
    quickActions: { display: 'flex', justifyContent: 'space-between', margin: theme.spacing(2, 0) },
    quickBtn: { flex: 1, margin: theme.spacing(0, 1), borderRadius: 8, background: '#fff', border: '1px solid #e0e0e0', padding: theme.spacing(2, 0), '&:hover': { background: '#1e8449', color: '#fff' } },
  })
);

const ipPool = [
  '***********', '*********', '************',
  '*************', '**************', '************',
  '*************', '**************', '*************'
];

const threatList = [
  { ip: '************', type: 'SQL注入', asset: '华北-变电站A' },
  { ip: '***********', type: 'RDP暴力破解', asset: '华东-电厂B' },
  { ip: '************', type: '工控协议漏洞', asset: '华南-变电站C' },
  { ip: '***********', type: 'XSS跨站脚本', asset: '华中-调度中心' },
  { ip: '*************', type: '权限提升', asset: '西北-电厂D' },
];

const HomePage: React.FC = () => {
  const classes = useStyles();
  const [honeypotCount, setHoneypotCount] = useState(1024);
  const [rollingIPs, setRollingIPs] = useState<string[]>(ipPool.slice(0, 3));
  const [chatOpen, setChatOpen] = useState(false);
  const [chatInput, setChatInput] = useState('');
  const [chatMessages, setChatMessages] = useState([
    { sender: 'bot', text: '您好！我是电力网络安全助手，您可以向我查询告警信息、生成报告或获取安全建议。' }
  ]);
  const [notification, setNotification] = useState<string | null>(null);
  const [isolated, setIsolated] = useState<{ [ip: string]: boolean }>({});

  // 蜜罐计数
  useEffect(() => {
    const timer = setInterval(() => {
      setHoneypotCount(c => c + Math.floor(Math.random() * 5));
    }, 3000);
    return () => clearInterval(timer);
  }, []);

  // 滚动IP
  useEffect(() => {
    const timer = setInterval(() => {
      const shuffled = [...ipPool].sort(() => 0.5 - Math.random());
      setRollingIPs(shuffled.slice(0, 3));
    }, 15000);
    return () => clearInterval(timer);
  }, []);

  // 聊天机器人回复
  const handleSendChat = () => {
    if (!chatInput.trim()) return;
    setChatMessages(msgs => [...msgs, { sender: 'user', text: chatInput }]);
    const userMsg = chatInput;
    setChatInput('');
    setTimeout(() => {
      const responses = [
        `已为您查询到关于"${userMsg}"的信息: 共发现15条相关告警记录。`,
        `正在生成"${userMsg}"的报告，请稍候...`,
        `关于"${userMsg}"，系统检测到3个高风险点需要您关注。`,
        `已为您分析"${userMsg}"的数据，结果显示安全评分72分(满分100)。`,
        `"${userMsg}"相关的安全建议已生成，请查看详细报告。`
      ];
      setChatMessages(msgs => [...msgs, { sender: 'bot', text: responses[Math.floor(Math.random() * responses.length)] }]);
    }, 1000);
  };

  // 隔离按钮
  const handleIsolate = (ip: string) => {
    setTimeout(() => {
      setIsolated(prev => ({ ...prev, [ip]: true }));
      setNotification(`已成功隔离IP: ${ip}`);
    }, 500);
  };

  // 快捷操作
  const handleQuickAction = (text: string) => {
    setNotification(`正在执行: ${text}`);
  };

  // 合规率仪表盘
  const complianceOption = {
    series: [{
      type: 'gauge',
      center: ['50%', '60%'],
      startAngle: 180,
      endAngle: 0,
      min: 0,
      max: 100,
      splitNumber: 10,
      radius: '100%',
      axisLine: {
        lineStyle: {
          width: 20,
          color: [
            [0.7, '#e74c3c'],
            [0.85, '#f39c12'],
            [1, '#2ecc71']
          ]
        }
      },
      pointer: {
        length: '12%',
        width: 20,
        itemStyle: { color: 'auto' }
      },
      axisTick: { length: 12, lineStyle: { color: 'auto', width: 2 } },
      splitLine: { length: 20, lineStyle: { color: 'auto', width: 3 } },
      axisLabel: {
        color: '#7f8c8d',
        fontSize: 12,
        distance: -30,
        formatter: (value: number) => {
          if (value === 100) return 'A';
          if (value === 80) return 'B';
          if (value === 60) return 'C';
          if (value === 40) return 'D';
          if (value === 20) return 'E';
          return '';
        }
      },
      detail: {
        fontSize: 24,
        offsetCenter: [0, '0%'],
        valueAnimation: true,
        formatter: '{value}%',
        color: 'inherit'
      },
      data: [{ value: 89, name: '合规率' }]
    }]
  };

  // 风险趋势
  const riskTrendOption = {
    grid: { left: 0, right: 0, top: 0, bottom: 0 },
    xAxis: { type: 'category', show: false, data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'] },
    yAxis: { type: 'value', show: false, min: 50, max: 90 },
    series: [{
      data: [65, 68, 70, 75, 78, 76],
      type: 'line',
      smooth: true,
      symbol: 'none',
      lineStyle: { width: 3, color: '#e74c3c' },
      areaStyle: {
        color: {
          type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(231, 76, 60, 0.3)' },
            { offset: 1, color: 'rgba(231, 76, 60, 0.1)' }
          ]
        }
      }
    }]
  };

  // 告警趋势
  const alertTrendOption = {
    tooltip: { trigger: 'axis' },
    legend: { data: ['原始告警', '降噪后告警'], right: 10, top: 0, textStyle: { color: '#7f8c8d' } },
    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      axisLine: { lineStyle: { color: '#e0e0e0' } },
      axisLabel: { color: '#7f8c8d' }
    },
    yAxis: {
      type: 'value',
      axisLine: { lineStyle: { color: '#e0e0e0' } },
      axisLabel: { color: '#7f8c8d' },
      splitLine: { lineStyle: { color: '#f0f0f0' } }
    },
    series: [
      {
        name: '原始告警',
        type: 'line',
        data: [1200, 1320, 1010, 1340, 900, 830, 1170],
        smooth: true,
        lineStyle: { width: 3, color: '#1e8449' },
        itemStyle: { color: '#1e8449' },
        areaStyle: {
          color: {
            type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(30, 132, 73, 0.3)' },
              { offset: 1, color: 'rgba(30, 132, 73, 0.1)' }
            ]
          }
        }
      },
      {
        name: '降噪后告警',
        type: 'line',
        data: [820, 932, 701, 934, 690, 630, 820],
        smooth: true,
        lineStyle: { width: 3, color: '#3498db' },
        itemStyle: { color: '#3498db' },
        areaStyle: {
          color: {
            type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(52, 152, 219, 0.3)' },
              { offset: 1, color: 'rgba(52, 152, 219, 0.1)' }
            ]
          }
        }
      }
    ]
  };

  return (
    <Box className={classes.root}>
      {/* 核心指标卡片 */}
      <Grid container spacing={2} className={classes.section}>
        {/* 今日告警总数 */}
        <Grid item xs={12} md={3}>
          <Paper className={classes.card}>
            <div className={classes.metricHeader}>
              <Typography variant="subtitle1">今日告警总数</Typography>
              <span className={`${classes.detailLabel} ${classes.high}`}>↑12%</span>
            </div>
            <div className={classes.metricValue}>1,248</div>
            <Grid container>
              <Grid item xs={4}><div className={`${classes.detailLabel} ${classes.high}`}>高危</div><Typography align="center">86</Typography></Grid>
              <Grid item xs={4}><div className={`${classes.detailLabel} ${classes.medium}`}>中危</div><Typography align="center">342</Typography></Grid>
              <Grid item xs={4}><div className={`${classes.detailLabel} ${classes.low}`}>低危</div><Typography align="center">820</Typography></Grid>
            </Grid>
          </Paper>
        </Grid>
        {/* 蜜罐诱捕攻击 */}
        <Grid item xs={12} md={3}>
          <Paper className={classes.card}>
            <div className={classes.metricHeader}>
              <Typography variant="subtitle1">蜜罐诱捕攻击</Typography>
              <span className={`${classes.detailLabel} ${classes.high}`}>↑8%</span>
            </div>
            <div className={classes.metricValue}>{honeypotCount.toLocaleString()}</div>
            <Typography variant="body2" color="textSecondary">实时攻击次数</Typography>
            <Box mt={2}>
              {rollingIPs.map(ip => (
                <Typography key={ip} variant="body2">{ip}</Typography>
              ))}
            </Box>
          </Paper>
        </Grid>
        {/* 资产配置合规率 */}
        <Grid item xs={12} md={3}>
          <Paper className={classes.card}>
            <div className={classes.metricHeader}>
              <Typography variant="subtitle1">资产配置合规率</Typography>
              <span className={`${classes.detailLabel} ${classes.low}`}>↓2%</span>
            </div>
            <Box style={{ height: 100 }}>
              <ReactEcharts option={complianceOption} style={{ height: 100 }} />
            </Box>
            <Typography variant="body2" color="textSecondary">基线标准: 95%</Typography>
          </Paper>
        </Grid>
        {/* 风险评分趋势 */}
        <Grid item xs={12} md={3}>
          <Paper className={classes.card}>
            <div className={classes.metricHeader}>
              <Typography variant="subtitle1">风险评分趋势</Typography>
            </div>
            <Grid container>
              <Grid item xs={6}><Typography variant="body2" color="textSecondary">今日</Typography><Typography className={classes.high} style={{ fontSize: 24, fontWeight: 700 }}>78</Typography></Grid>
              <Grid item xs={6}><Typography variant="body2" color="textSecondary">昨日</Typography><Typography className={classes.medium} style={{ fontSize: 24, fontWeight: 700 }}>65</Typography></Grid>
            </Grid>
            <Box style={{ height: 60, marginTop: 8 }}>
              <ReactEcharts option={riskTrendOption} style={{ height: 60 }} />
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* 自然语言查询 */}
      <Box className={classes.section} display="flex" justifyContent="flex-end">
        <IconButton className={classes.chatbotBtn} onClick={() => setChatOpen(true)}>
          <ChatIcon />
        </IconButton>
      </Box>

      {/* 可视化区域（可以补充你的 ECharts 地图和趋势图） */}
      <Grid container spacing={2} className={classes.section}>
        <Grid item xs={12} md={8}>
          <Paper className={classes.card} style={{ height: 340 }}>
            <Typography variant="subtitle1" gutterBottom>电力设施攻击热力图</Typography>
            {/* <ReactEcharts option={attackMapOption} style={{ height: 300 }} /> */}
            <Box height={300} display="flex" alignItems="center" justifyContent="center" color="#ccc">地图占位</Box>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper className={classes.card} style={{ height: 340 }}>
            <Typography variant="subtitle1" gutterBottom>告警趋势分析</Typography>
            <ReactEcharts option={alertTrendOption} style={{ height: 300 }} />
          </Paper>
        </Grid>
      </Grid>

      {/* TOP威胁列表 */}
      <Box className={classes.section}>
        <Paper className={classes.card}>
          <div className={classes.metricHeader}>
            <Typography variant="subtitle1">TOP 5 威胁源</Typography>
            <Button size="small" variant="outlined">实时更新</Button>
          </div>
          <Box>
            <Grid container style={{ fontWeight: 600, color: '#7f8c8d', borderBottom: '1px solid #e0e0e0', padding: '12px 0' }}>
              <Grid item xs={3}>攻击IP</Grid>
              <Grid item xs={3}>漏洞类型</Grid>
              <Grid item xs={3}>受影响资产</Grid>
              <Grid item xs={3}>操作</Grid>
            </Grid>
            {threatList.map(t => (
              <Grid container key={t.ip} alignItems="center" style={{ borderBottom: '1px solid #e0e0e0', padding: '12px 0', opacity: isolated[t.ip] ? 0.5 : 1 }}>
                <Grid item xs={3}>{t.ip}</Grid>
                <Grid item xs={3}>{t.type}</Grid>
                <Grid item xs={3}>{t.asset}</Grid>
                <Grid item xs={3}>
                  <Button
                    variant="contained"
                    color="secondary"
                    disabled={!!isolated[t.ip]}
                    onClick={() => handleIsolate(t.ip)}
                  >
                    {isolated[t.ip] ? '已隔离' : '隔离'}
                  </Button>
                </Grid>
              </Grid>
            ))}
          </Box>
        </Paper>
      </Box>

      {/* 底部快捷操作 */}
      <Box className={classes.quickActions}>
        {['查看最新告警', '启动蜜罐诱捕', '运行配置检测', '生成日报', '数据备份'].map(action => (
          <Button key={action} className={classes.quickBtn} onClick={() => handleQuickAction(action)}>
            {action}
          </Button>
        ))}
      </Box>

      {/* 聊天机器人对话框 */}
      <Dialog open={chatOpen} onClose={() => setChatOpen(false)} classes={{ paper: classes.dialogPaper }}>
        <DialogTitle>
          智能助手
          <IconButton aria-label="close" onClick={() => setChatOpen(false)} style={{ position: 'absolute', right: 8, top: 8 }}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers style={{ minHeight: 300, maxHeight: 400, overflowY: 'auto' }}>
          {chatMessages.map((msg, idx) => (
            <Box key={idx} mb={2} textAlign={msg.sender === 'user' ? 'right' : 'left'}>
              <Box
                display="inline-block"
                bgcolor={msg.sender === 'bot' ? '#f0f0f0' : '#1e8449'}
                color={msg.sender === 'bot' ? '#2c3e50' : '#fff'}
                px={2} py={1} borderRadius={8}
                style={msg.sender === 'user' ? { borderTopRightRadius: 0 } : { borderTopLeftRadius: 0 }}
              >
                {msg.text}
              </Box>
            </Box>
          ))}
        </DialogContent>
        <DialogActions>
          <TextField
            fullWidth
            variant="outlined"
            size="small"
            placeholder="输入查询指令..."
            value={chatInput}
            onChange={e => setChatInput(e.target.value)}
            onKeyPress={e => { if (e.key === 'Enter') handleSendChat(); }}
          />
          <Button color="primary" onClick={handleSendChat}>发送</Button>
        </DialogActions>
      </Dialog>

      {/* 通知 */}
      <Snackbar
        open={!!notification}
        message={notification}
        autoHideDuration={3000}
        onClose={() => setNotification(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      />
    </Box>
  );
};

export default HomePage;
