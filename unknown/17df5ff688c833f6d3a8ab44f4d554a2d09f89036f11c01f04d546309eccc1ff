import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>alogA<PERSON>, DialogContent, DialogTitle, MenuItem, TextField } from "@material-ui/core";
import React, { useState } from "react";
import { showSnackbar } from "./myMessageBar";
import styles from "../less/addHoneypotDialog.module.less";

interface AddHoneypotDialogProps {
  open: boolean;
  nodeId: number;
  onClose: () => void;
  onSubmit: (data: any) => void;
}

export default function AddHoneypotDialog(props: AddHoneypotDialogProps) {
  const { open, nodeId, onClose, onSubmit } = props;
  const [form, setForm] = useState({
    honeypot_name: "",
    honeypot_type: "",
    service_port: "",
    honeypot_status: "active",
    log_storage_path: "",
    description: ""
  });

  // 输入框变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // 提交
  const handleSubmit = () => {
    if (!form.honeypot_name || !form.honeypot_type || !form.service_port) {
      showSnackbar("请填写必填项", "error");
      return;
    }
    onSubmit({
      ...form,
      service_port: Number(form.service_port),
      node_id: nodeId
    });
  };

  // 关闭时清空
  React.useEffect(() => {
    if (!open) {
      setForm({
        honeypot_name: "",
        honeypot_type: "",
        service_port: "",
        honeypot_status: "active",
        log_storage_path: "",
        description: ""
      });
    }
  }, [open]);

  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>添加蜜罐服务</DialogTitle>
      <DialogContent>
        <TextField
          margin="dense"
          fullWidth
          label="蜜罐名称"
          name="honeypot_name"
          value={form.honeypot_name}
          onChange={handleChange}
          required
        />
        <TextField
          margin="dense"
          fullWidth
          label="蜜罐类型"
          name="honeypot_type"
          value={form.honeypot_type}
          onChange={handleChange}
          required
        />
        <TextField
          margin="dense"
          fullWidth
          label="服务端口"
          name="service_port"
          value={form.service_port}
          onChange={handleChange}
          required
          type="number"
        />
        <TextField
          select
          margin="dense"
          fullWidth
          label="蜜罐状态"
          name="honeypot_status"
          value={form.honeypot_status}
          onChange={handleChange}
        >
          <MenuItem value="active">在线</MenuItem>
          <MenuItem value="inactive">离线</MenuItem>
        </TextField>
        <TextField
          margin="dense"
          fullWidth
          label="日志存储路径"
          name="log_storage_path"
          value={form.log_storage_path}
          onChange={handleChange}
        />
        <TextField
          margin="dense"
          fullWidth
          label="描述"
          name="description"
          value={form.description}
          onChange={handleChange}
        />
      </DialogContent>
      <DialogActions>
        <Button className={styles.noFocus} onClick={onClose}>取消</Button>
        <Button className={styles.noFocus} onClick={handleSubmit} variant="contained" color="primary">
          提交
        </Button>
      </DialogActions>
    </Dialog>
  );
}
