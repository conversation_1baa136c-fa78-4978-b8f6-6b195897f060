import React, { useState, useEffect, useRef } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import { Typography, Box } from '@material-ui/core';

// 定义样式
const useStyles = makeStyles((theme) => ({
  root: {
    width: '100%',
    height: '100%',
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
    padding: theme.spacing(2),
    marginTop: '50px',
  },
  title: {
    marginBottom: theme.spacing(2),
    fontWeight: 'bold',
  },
  chainWrapper: {
    position: 'relative',
    height: '280px',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    marginTop: theme.spacing(2),
  },
  chainContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing(1, 0),
    position: 'relative',
    marginTop: '30px',
  },
  hexagon: {
    position: 'relative',
    width: '110px',
    height: '120px',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
    zIndex: 2,
    borderWidth: '1px',
    borderStyle: 'solid',
  },
  hexagonGray: {
    background: '#ffffff',
    borderColor: '#e0e0e0',
  },
  hexagonOrange: {
    background: '#fffaf0',
    borderColor: '#ffa500',
  },
  hexagonRed: {
    background: '#fff5f5',
    borderColor: '#ff4d4f',
  },
  hexagonContent: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#333',
  },
  icon: {
    width: '24px',
    height: '24px',
    marginBottom: theme.spacing(0.5),
  },
  number: {
    fontSize: '26px',
    fontWeight: 700,
    margin: theme.spacing(0.5, 0),
  },
  label: {
    fontSize: '13px',
    textAlign: 'center',
  },
  subLabel: {
    fontSize: '11px',
    color: '#666',
    marginTop: '-4px',
  },
  line: {
    position: 'absolute',
    height: '1px',
    background: '#e0e0e0',
    top: '50%',
    zIndex: 1,
    width: '100%',
  },
  boldLine: {
    height: '30px',
    background: '#f0f0f0',
    margin: 'auto 0',
    flexGrow: 1,
    position: 'relative',
    zIndex: 2,
  },
  normalLine: {
    height: '3px',
    background: 'black',
    margin: 'auto 0',
    flexGrow: 1,
    position: 'relative',
    zIndex: 2,
  },
  boldArrow: {
    width: 0,
    height: 0,
    borderTop: '15px solid transparent',
    borderBottom: '15px solid transparent',
    borderLeft: '15px solid black',
    marginLeft: '-3px',
    marginRight: '5px',
    zIndex: 2,
  },
  normalArrow: {
    width: 0,
    height: 0,
    borderTop: '6px solid transparent',
    borderBottom: '6px solid transparent',
    borderLeft: '9px solid black',
    marginLeft: '-2px',
    marginRight: '5px',
    zIndex: 2,
  },
  hexagonCount: {
    position: 'absolute',
    top: '10px',
    right: '10px',
    width: '20px',
    height: '20px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: '50%',
    background: '#f5f5f5',
    fontSize: '11px',
    fontWeight: 'bold',
  },
  smallHexagon: {
    width: '60px',
    height: '70px',
    background: 'white',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
    position: 'absolute',
    zIndex: 4,
  },
  alertHexagon: {
    width: '95px',
    height: '110px',
    background: 'white',
    border: '1px solid #FF4D4F',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
    position: 'absolute',
    top: '-25px',
    right: '0',
    zIndex: 5,
  },
  infoHexagon: {
    width: '95px',
    height: '110px',
    background: 'white',
    border: '1px solid #d0d0d0',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
    position: 'absolute',
    zIndex: 4,
  },
  orangeNumber: {
    color: '#FFA500',
    fontWeight: 'bold',
  },
  loginNumber: {
    color: ' #f58634',
    fontWeight: 'bold',
  },
  redNumber: {
    color: '#FF4D4F',
    fontWeight: 'bold',
  },
  leftArrow: {
    position: 'absolute',
    top: '15px',
    left: '15px',
    color: '#FFA500',
    transform: 'rotate(-45deg)',
    fontWeight: 'bold',
    fontSize: '14px',
  },
  rightArrow: {
    position: 'absolute',
    bottom: '15px',
    right: '15px',
    color: '#FF4D4F',
    transform: 'rotate(-45deg)',
    fontWeight: 'bold',
    fontSize: '14px',
  },
  alertCount: {
    position: 'absolute', 
    top: '5px', 
    right: '5px', 
    width: '18px', 
    height: '18px', 
    borderRadius: '50%', 
    background: '#ddd', 
    display: 'flex', 
    alignItems: 'center', 
    justifyContent: 'center', 
    fontSize: '11px',
    fontWeight: 'bold',
    color: '#333'
  }
}));

// 定义接口
interface AttackChainData {
  attackIPs: number;
  scans: number;
  attacks: number;
  loginAttempts: number;
  login:number;
  breaches: {
    current: number;
    total: number;
  };
  heightlogin: {
    current: number;
    total: number;
  };
  monitors: number;
  totalAlerts: number;
}

export default function AttackChain() {
  const classes = useStyles();
  const [data, setData] = useState<AttackChainData>({
    attackIPs: 11,
    scans: 5169,
    attacks: 1108,
    loginAttempts: 68,
    login:68,
    heightlogin: {
      current: 0,
      total: 2,
    },
    breaches: {
      current: 0,
      total: 1,
    },
    monitors: 2,
    totalAlerts: 1,
  });

  // 高危尝试登录节点的引用
  const highRiskLoginRef = useRef<HTMLDivElement>(null);
  // 信息监控节点位置状态
  const [monitorPosition, setMonitorPosition] = useState({ top: 0, left: 0 });

  // 获取最后一个元素的位置（失陷节点）
  const getLastNodePosition = () => {
    const container = document.querySelector(`.${classes.chainContainer}`);
    if (container) {
      const nodes = container.querySelectorAll(`.${classes.hexagon}`);
      if (nodes.length > 0) {
        const lastNode = nodes[nodes.length - 1];
        const rect = lastNode.getBoundingClientRect();
        return {
          left: rect.left,
          top: rect.top,
          width: rect.width
        };
      }
    }
    return null;
  };

  // 警报总数六边形的参考位置
  const position = React.useRef<any>(null);

  React.useEffect(() => {
    // 组件挂载后获取位置
    setTimeout(() => {
      position.current = getLastNodePosition();
    }, 100);
  }, []);

  // 更新信息监控节点位置
  React.useEffect(() => {
    const updateMonitorPosition = () => {
      if (highRiskLoginRef.current) {
        const rect = highRiskLoginRef.current.getBoundingClientRect();
        const parentRect = highRiskLoginRef.current.closest(`.${classes.chainWrapper}`)?.getBoundingClientRect();
        
        if (parentRect) {
          // 计算相对于父容器的位置
          setMonitorPosition({
            top: rect.bottom - parentRect.top, // 高危登录节点底部下方10px
            left: rect.left - parentRect.left + rect.width / 2 // 水平居中对齐
          });
        }
      }
    };

    // 初始更新位置
    updateMonitorPosition();
    
    // 监听窗口大小变化，更新位置
    window.addEventListener('resize', updateMonitorPosition);
    
    // 添加滚动事件监听
    window.addEventListener('scroll', updateMonitorPosition);

    // 清理监听器
    return () => {
      window.removeEventListener('resize', updateMonitorPosition);
      window.removeEventListener('scroll', updateMonitorPosition);
    };
  }, [classes.chainWrapper]);

  return (
    <div className={classes.root}>
      
      <div className={classes.chainWrapper}>
        {/* 警报总数六边形 - 右上方 */}
        <div className={classes.alertHexagon} style={{ 
          background: '#f0f0f0',
          right: '7px',  // 定位到右侧边缘
          top: '-15px', // 保持原有的垂直位置
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          textAlign: 'center'
        }}>
          <Typography variant="h6" style={{ fontSize: '18px', fontWeight: 'bold', color: '#333', margin: '0', padding: '0', lineHeight: '1.2' }}>
            {data.totalAlerts}
          </Typography>
          <Typography variant="caption" style={{ fontSize: '11px', margin: '0', padding: '0', lineHeight: '1.2' }}>
            蜜饵总数
          </Typography>
          <img src="/icons/蜜饵.png" alt="蜜饵" style={{ width: '20px', height: '20px', marginBottom: '4px' }} />
        </div>

        <div className={classes.chainContainer}>
          {/* 攻击IP六边形 */}
          <div className={`${classes.hexagon} ${classes.hexagonGray}`} style={{ position: 'relative', background: '#f0f0f0', zIndex: 3 }}>
            <div className={classes.hexagonContent}>
              <img src="/icons/攻击IP.png" alt="攻击IP" className={classes.icon} />
              <Typography variant="subtitle2" className={classes.label}>
                攻击IP
              </Typography>
              <Typography variant="h4" className={classes.number}>
                {data.attackIPs}
              </Typography>
            </div>
          </div>
          
          {/* 攻击IP和扫描之间的加粗连接线 */}
          <div className={classes.boldLine}></div>
          
          {/* 扫描六边形 */}
          <div className={`${classes.hexagon} ${classes.hexagonOrange}`} style={{ position: 'relative', background: '#f0f0f0', zIndex: 3 }}>
            <div className={classes.hexagonContent}>
              <Typography variant="h4" className={classes.orangeNumber}>
                {data.scans}
              </Typography>
              <Typography variant="subtitle2" className={classes.label}>
                扫描
              </Typography>
              <img src="/icons/漏洞扫描.png" alt="扫描" className={classes.icon} />
            </div>
          </div>
          
          {/* 扫描和攻击之间的正常连接线 */}
          <div className={classes.normalLine}></div>
          <div className={classes.normalArrow}></div>
          
          {/* 攻击六边形 */}
          <div className={`${classes.hexagon} ${classes.hexagonOrange}`} style={{ position: 'relative', background: '#f0f0f0', zIndex: 3 }}>
            <div className={classes.hexagonContent}>
              <img src="/icons/攻击.png" alt="攻击" className={classes.icon} />
              <Typography variant="subtitle2" className={classes.label}>
                攻击
              </Typography>
              <Typography variant="h4" className={classes.orangeNumber}>
                {data.attacks}
              </Typography>
            </div>
          </div>

          {/* 扫描和攻击之间的正常连接线 */}
          <div className={classes.normalLine}></div>
          <div className={classes.normalArrow}></div>
          
          {/* 尝试登录六边形 */}
          <div className={`${classes.hexagon} ${classes.hexagonOrange}`} style={{ position: 'relative', background: '#f0f0f0', zIndex: 3 }}>
            <div className={classes.hexagonContent}>
              <Typography variant="h4" className={classes.loginNumber}>
                {data.login}
              </Typography>
              <Typography variant="subtitle2" className={classes.label}>
                尝试登录
              </Typography>
              <img src="/icons/登录.png" alt="尝试登录" className={classes.icon} />
            </div>
          </div>
          
          
          {/* 攻击和尝试登陆之间的正常连接线 */}
          <div className={classes.normalLine}></div>
          <div className={classes.normalArrow}></div>
          
          {/* 高危尝试登陆六边形 */}
          <div 
            ref={highRiskLoginRef}
            className={`${classes.hexagon} ${classes.hexagonRed}`} 
            style={{ position: 'relative', background: '#f0f0f0', zIndex: 3 }}
          >
            <div className={classes.hexagonContent}>
              <img src="/icons/高危登录.png" alt="高危尝试登录" className={classes.icon} />
              <Typography variant="subtitle2" className={classes.label}>
                高危尝试登陆
              </Typography>
              <Typography variant="h4" className={classes.redNumber}>
              {data.heightlogin.current}/{data.heightlogin.total}
              </Typography>
            </div>
          </div>
          
          {/* 尝试登陆和失陷之间的正常连接线 */}
          <div className={classes.normalLine}></div>
          <div className={classes.normalArrow}></div>
          
          {/* 失陷六边形 */}
          <div className={`${classes.hexagon} ${classes.hexagonRed}`} style={{ position: 'relative', background: '#f0f0f0', zIndex: 3 }}>
            <div className={classes.hexagonContent}>
              <Typography variant="h4" className={classes.redNumber}>
                {data.breaches.current}/{data.breaches.total}
              </Typography>
              <Typography variant="subtitle2" className={classes.label}>
                失陷
              </Typography>
              <img src="/icons/失陷.png" alt="失陷" className={classes.icon} />
            </div>
          </div>
        </div>
        
        {/* 信息监控六边形 - 固定在高危尝试登录节点下方 */}
        <div 
          className={classes.infoHexagon} 
          style={{ 
            background: '#f0f0f0',
            position: 'absolute',
            top: `${monitorPosition.top}px`,
            left: `${monitorPosition.left}px`,
            transform: 'translateX(-50%)', // 水平居中对齐
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            textAlign: 'center',
            zIndex: 5 // 确保在顶层显示
          }}
        >
          <img src="/icons/监控.png" alt="监控" style={{ width: '20px', height: '20px', marginBottom: '4px' }} />
          <Typography variant="caption" style={{ fontSize: '11px', margin: '0', padding: '0', lineHeight: '1.2' }}>
            信息监控
          </Typography>
          <Typography variant="h6" style={{ fontSize: '16px', fontWeight: 'bold', margin: '0', padding: '0', lineHeight: '1.2' }}>
            {data.monitors}
          </Typography>
        </div>
      </div>
    </div>
  );
} 