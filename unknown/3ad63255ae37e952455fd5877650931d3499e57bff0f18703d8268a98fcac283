// 节点信息
interface NodeInfo {
  id: number;
  node_name: string;
  node_status: string;
  created_at: string | null;
  updated_at: string | null;
  deployed_at: string | null;
  node_type: string | null;
  deployment_location: string | null;
  department: string | null;
}

// 主机信息
interface HostInfo {
  operating_system: string;
  architecture: string;
  timezone: string;
  ip_address: string;
  subnet_mask: string;
  dns_servers: string;
  mac_address: string;
  hardware_config: string;
}

// 单个节点
export interface NodeItem {
  node_info: NodeInfo;
  host_info: HostInfo;
}

// 分页信息
export interface PaginationInfo {
  page: number;
  per_page: number;
  total: number;
  pages: number;
}

// 后端返回的完整结构
export interface GetAllNodesResponse {
  items: NodeItem[];
  pagination: PaginationInfo;
}

export interface TableData {
  record_data: NodeItem;
}