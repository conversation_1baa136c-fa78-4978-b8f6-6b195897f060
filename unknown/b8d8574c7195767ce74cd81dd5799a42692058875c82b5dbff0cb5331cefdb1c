// src/utils/statusMap.ts

// 英文状态 => 中文状态 映射
export const statusTextMap: Record<string, string> = {
  active: '活跃',
  inactive: '停用',
  // error: '错误',
  // starting: '启动中',
  // stopping: '停止中',
  // misconfigured: '配置错误',
};

// 可选：中文状态 => 英文状态 映射（如果需要反向转换）
export const statusValueMap: Record<string, string> = {
  活跃: 'active',
  停用: 'inactive',
  错误: 'error',
  启动中: 'starting',
  停止中: 'stopping',
  配置错误: 'misconfigured',
};

// 可选：获取中文状态文本函数
export const getStatusText = (status: string): string => {
  return statusTextMap[status] || status;
};

// 可选：获取英文状态值函数
export const getStatusValue = (text: string): string => {
  return statusValueMap[text] || text;
};
