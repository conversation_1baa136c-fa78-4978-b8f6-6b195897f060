import { Button, <PERSON>alog, DialogActions, DialogContent, DialogContentText, DialogTitle, Grid, MenuItem, Snackbar, TextField } from "@material-ui/core";
import { useState } from "react";
interface ILogData {
  row:{
    timestamp: string
    log_name: string
    alert_signature: string
    severity: number
    src_ip: string
    src_port: string
    dst_ip: string
    dst_port: string
    protocol: number
    category: string
    payload: string
    created_at: string
    updated_at: string
    geo_location:string
  }
}

export default function LogFileDetail({row}: ILogData) {
    const [open, setOpen] = useState(false);
    const handleOnclick = () => {
        setOpen(true);
    }
    const handleConfirm = () => {
        setOpen(false);
    }

    return (
        <>
        <Button style={{border: '1px solid #E0E0E0',marginLeft: 10,fontSize: '16px' }} onClick={handleOnclick}>
            详情
        </Button>
        <Dialog open={open}   maxWidth="sm" fullWidth >
            <DialogTitle>日志详情</DialogTitle>
            <DialogContent >
                <Grid container spacing={2}>
                    <Grid item xs={6}>
                        <TextField label="日志名称" name="log_name"  margin="dense" required  fullWidth InputProps={{readOnly: true}} defaultValue={row.log_name} />
                    </Grid> 
                    <Grid item xs={6}>
                        <TextField label="告警签名名称" name="alert_signature"   margin="dense" required  fullWidth InputProps={{readOnly: true}} defaultValue={row.alert_signature} />
                    </Grid> 
                    <Grid item xs={6}>
                        <TextField  label="告警等级" name="severity"  margin="dense" required  fullWidth InputProps={{readOnly: true}} defaultValue={row.severity} />
                    </Grid> 
                     <Grid item xs={6}>
                        <TextField  label="协议类型" name="protocol"  margin="dense" required  fullWidth InputProps={{readOnly: true}} defaultValue={row.protocol} />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="源IP" name="src_ip"  margin="dense" required  fullWidth InputProps={{readOnly: true}} defaultValue={row.src_ip} />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="源端口" name="src_port" margin="dense" required  fullWidth InputProps={{readOnly: true}} defaultValue={row.src_port} />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="目标IP" name="dst_ip"   margin="dense" required  fullWidth InputProps={{readOnly: true}} defaultValue={row.dst_ip} />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="目标端口" name="dst_port"  margin="dense" required  fullWidth InputProps={{readOnly: true}} defaultValue={row.dst_port} />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField label="攻击类别" name="category" margin="dense" required  fullWidth InputProps={{readOnly: true}} defaultValue={row.category} />
                    </Grid>
                     <Grid item xs={6}>
                        <TextField label="报文内容摘要" name="payload"   margin="dense" required  fullWidth InputProps={{readOnly: true}}  defaultValue={row.payload} />
                     </Grid>
                     <Grid item xs={6}>
                        <TextField label="源IP地理位置" name="geo_location"  margin="dense" required  fullWidth InputProps={{readOnly: true}}  defaultValue={row.geo_location} />
                     </Grid>
                     <Grid item xs={6}>
                        <TextField label="日志时间" name="timestamp"   margin="dense" required  fullWidth InputProps={{readOnly: true}} defaultValue={row.timestamp} />
                     </Grid>
                </Grid>
            </DialogContent>
            <DialogActions>
                 <Button  onClick={handleConfirm} variant="contained" color="primary">
                     关闭
                 </Button>
            </DialogActions>
        </Dialog>
        </>
    )
}

