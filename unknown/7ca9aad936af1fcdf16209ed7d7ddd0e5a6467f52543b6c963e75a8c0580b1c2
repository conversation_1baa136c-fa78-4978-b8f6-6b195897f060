// src/components/ConfigDialog.tsx
import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Switch,
  FormControlLabel,
  Typography,
  MenuItem,
  Collapse,
  Box,
  Grid,
  Tabs,
  Tab,
  Radio,
  RadioGroup,
  FormControl,
  FormLabel,
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';

export type ConfigDialogProps = {
  open: boolean;
  initialData?: Partial<any>;
  onClose: () => void;
  onSubmit: (data: any) => void;
  isEditMode: boolean;
};

const useStyles = makeStyles((theme) => ({
  tabPanel: {
    padding: theme.spacing(2),
  },
  sectionTitle: {
    fontWeight: 600,
    marginBottom: theme.spacing(2),
  },
  formField: {
    marginBottom: theme.spacing(2),
  },
}));

const ConfigDialog: React.FC<ConfigDialogProps> = ({
  open,
  initialData = {},
  onClose,
  onSubmit,
  isEditMode,
}) => {
  const classes = useStyles();

  const [form, setForm] = useState<any>({
    config_name: '',
    description: '',
    config_type: 'local',
    is_active: true,
    model_path: '',
    torch_dtype: 'float16',
    device_map: 'auto',
    trust_remote_code: false,
    temperature: 0.3,
    max_tokens: 512,
    top_p: 0.9,
    api_key: '',
    model_name: 'gpt-4',
    api_base: '',
    timeout: 30,
    max_retries: 3,
    confidence_threshold: 0.5,
    max_recommendations: 5,
    include_cwe_mapping: true,
    include_mitre_mapping: true,
    batch_size: 1,
    enable_caching: true,
    window_size: 10,
    min_sequence_length: 5,
    anomaly_threshold: 0.7,
    enable_statistical_detection: true,
    enable_pattern_detection: true,
    enable_behavioral_detection: true,
    enable_temporal_detection: true,
    learning_period: 1440,
    learning_period_unit: 'minutes',
    enable_llm_enhancement: false,
    llm_enhancement_mode: 'hybrid',
    llm_pattern_analysis: true,
    llm_semantic_analysis: true,
    llm_anomaly_enhancement: true,
    llm_batch_size: 20,
    llm_context_window: 50,
  });

  const [activeTab, setActiveTab] = useState(0);

useEffect(() => {
  if (open && initialData) {
    setForm((prev: any) => ({ ...prev, ...initialData }));
  }
}, [open, initialData]);


  const handleChange = (key: string, value: any) => {
    setForm((prev: any) => ({ ...prev, [key]: value }));
  };

  const handleTabChange = (event: React.ChangeEvent<{}>, newValue: number) => {
    setActiveTab(newValue);
  };

  const renderBasicTab = () => (
  <Box className={classes.tabPanel}>
    <Typography className={classes.sectionTitle}>基础配置</Typography>

    <TextField
      label="配置名称"
      required
      fullWidth
      variant="outlined"
      className={classes.formField}
      value={form.config_name}
      onChange={(e) => handleChange('config_name', e.target.value)}
    />
    <TextField
      label="描述"
      fullWidth
      multiline
      variant="outlined"
      className={classes.formField}
      value={form.description}
      onChange={(e) => handleChange('description', e.target.value)}
    />

   <FormControl component="fieldset" className={classes.formField}>
  <FormLabel component="legend">配置类型 *</FormLabel>
  <RadioGroup
    row
    value={form.config_type}
    onChange={(e) => handleChange('config_type', e.target.value)}
  >
    <FormControlLabel value="local" control={<Radio color="primary" />} label="本地模型" />
    <FormControlLabel value="openai" control={<Radio color="primary" />} label="OpenAI" />
  </RadioGroup>
</FormControl>


    {/* ✅ 激活状态单独一行显示 */}
    <Box mt={2}>
      <Typography className={classes.formField}>激活状态</Typography>
      <FormControlLabel
        control={
          <Switch
            checked={form.is_active}
            onChange={(e) => handleChange('is_active', e.target.checked)}
            color="primary"
          />
        }
        label="激活此配置"
      />
    </Box>
  </Box>
);


  const renderModelTab = () => (
    <Box className={classes.tabPanel}>
      <Typography className={classes.sectionTitle}>
        {form.config_type === 'local' ? '本地模型参数' : 'OpenAI参数'}
      </Typography>
      {form.config_type === 'local' ? (
        <>
          <TextField
            label="模型路径"
            required
            fullWidth
            variant="outlined"
            className={classes.formField}
            value={form.model_path}
            onChange={(e) => handleChange('model_path', e.target.value)}
          />
          <TextField
            select
            label="数据类型"
            fullWidth
            variant="outlined"
            className={classes.formField}
            value={form.torch_dtype}
            onChange={(e) => handleChange('torch_dtype', e.target.value)}
          >
            <MenuItem value="float16">float16</MenuItem>
            <MenuItem value="float32">float32</MenuItem>
          </TextField>
          <TextField
            label="设备映射"
            fullWidth
            variant="outlined"
            className={classes.formField}
            value={form.device_map}
            onChange={(e) => handleChange('device_map', e.target.value)}
          />
          <FormControlLabel
            control={
              <Switch
                checked={form.trust_remote_code}
                onChange={(e) => handleChange('trust_remote_code', e.target.checked)}
              />
            }
            label="信任远程代码"
          />
        </>
      ) : (
        <>
          <TextField
            label="API密钥"
            required
            fullWidth
            type="password"
            variant="outlined"
            className={classes.formField}
            value={form.api_key}
            onChange={(e) => handleChange('api_key', e.target.value)}
          />
          <TextField
            label="模型名称"
            fullWidth
            variant="outlined"
            className={classes.formField}
            value={form.model_name}
            onChange={(e) => handleChange('model_name', e.target.value)}
          />
          <TextField
            label="基础地址"
            fullWidth
            variant="outlined"
            className={classes.formField}
            value={form.api_base}
            onChange={(e) => handleChange('api_base', e.target.value)}
          />
          <TextField
            label="超时时间（秒）"
            type="number"
            fullWidth
            variant="outlined"
            className={classes.formField}
            value={form.timeout}
            onChange={(e) => handleChange('timeout', Number(e.target.value))}
          />
          <TextField
            label="最大重试次数"
            type="number"
            fullWidth
            variant="outlined"
            className={classes.formField}
            value={form.max_retries}
            onChange={(e) => handleChange('max_retries', Number(e.target.value))}
          />
        </>
      )}
      <TextField
        label="温度"
        type="number"
        fullWidth
        variant="outlined"
        className={classes.formField}
        inputProps={{ step: 0.01, min: 0, max: 1 }}
        value={form.temperature}
        onChange={(e) => handleChange('temperature', parseFloat(e.target.value))}
      />
      <TextField
        label="最大Token数"
        type="number"
        fullWidth
        variant="outlined"
        className={classes.formField}
        value={form.max_tokens}
        onChange={(e) => handleChange('max_tokens', Number(e.target.value))}
      />
      <TextField
        label="Top-P采样"
        type="number"
        fullWidth
        variant="outlined"
        className={classes.formField}
        inputProps={{ step: 0.01, min: 0, max: 1 }}
        value={form.top_p}
        onChange={(e) => handleChange('top_p', parseFloat(e.target.value))}
      />
    </Box>
  );

  const renderAnalysisTab = () => (
  <Box className={classes.tabPanel}>
    <Typography className={classes.sectionTitle}>分析规则</Typography>

    <TextField
      label="置信度阈值"
      type="number"
      inputProps={{ step: 0.01, min: 0, max: 1 }}
      fullWidth
      variant="outlined"
      className={classes.formField}
      value={form.confidence_threshold}
      onChange={(e) => handleChange('confidence_threshold', parseFloat(e.target.value))}
    />

    <TextField
      label="最大建议数"
      type="number"
      fullWidth
      variant="outlined"
      className={classes.formField}
      value={form.max_recommendations}
      onChange={(e) => handleChange('max_recommendations', Number(e.target.value))}
    />
    <TextField
      label="批处理大小"
      type="number"
      fullWidth
      variant="outlined"
      className={classes.formField}
      value={form.batch_size}
      onChange={(e) => handleChange('batch_size', Number(e.target.value))}
    />
    {[
      ['include_cwe_mapping', '包含CWE映射'],
      ['include_mitre_mapping', '包含MITRE映射'],
      ['enable_caching', '启用缓存'],
    ].map(([key, label]) => (
      <FormControlLabel
        key={key}
        control={
          <Switch
            checked={form[key]}
            onChange={(e) => handleChange(key, e.target.checked)}
          />
        }
        label={label}
      />
    ))}
  </Box>
);


  const renderSequenceTab = () => (
    <Box className={classes.tabPanel}>
      <Typography className={classes.sectionTitle}>序列分析</Typography>
      <Grid container spacing={2}>
        <Grid item xs={6}>
          <TextField
            label="窗口大小"
            type="number"
            fullWidth
            variant="outlined"
            className={classes.formField}
            value={form.window_size}
            onChange={(e) => handleChange('window_size', Number(e.target.value))}
          />
        </Grid>
        <Grid item xs={6}>
          <TextField
            label="最小序列长度"
            type="number"
            fullWidth
            variant="outlined"
            className={classes.formField}
            value={form.min_sequence_length}
            onChange={(e) => handleChange('min_sequence_length', Number(e.target.value))}
          />
        </Grid>
      </Grid>

      <TextField
        label="异常阈值"
        type="number"
        fullWidth
        variant="outlined"
        className={classes.formField}
        inputProps={{ step: 0.01, min: 0, max: 1 }}
        value={form.anomaly_threshold}
        onChange={(e) => handleChange('anomaly_threshold', parseFloat(e.target.value))}
      />

      {[
        ['enable_statistical_detection', '统计检测'],
        ['enable_pattern_detection', '模式检测'],
        ['enable_behavioral_detection', '行为检测'],
        ['enable_temporal_detection', '时序检测'],
        ['enable_llm_enhancement', '启用LLM增强'],
      ].map(([key, label]) => (
        <FormControlLabel
          key={key}
          control={
            <Switch
              checked={form[key]}
              onChange={(e) => handleChange(key, e.target.checked)}
            />
          }
          label={label}
        />
      ))}

      <TextField
        label="学习周期（分钟）"
        type="number"
        fullWidth
        variant="outlined"
        className={classes.formField}
        value={form.learning_period}
        onChange={(e) => handleChange('learning_period', Number(e.target.value))}
      />

      <Collapse in={form.enable_llm_enhancement}>
        <Typography className={classes.sectionTitle}>LLM增强选项</Typography>
        <TextField
          select
          label="增强模式"
          fullWidth
          variant="outlined"
          className={classes.formField}
          value={form.llm_enhancement_mode}
          onChange={(e) => handleChange('llm_enhancement_mode', e.target.value)}
        >
          <MenuItem value="hybrid">hybrid</MenuItem>
          <MenuItem value="advanced">advanced</MenuItem>
          <MenuItem value="basic">basic</MenuItem>
        </TextField>

        {[
          ['llm_pattern_analysis', '模式分析'],
          ['llm_semantic_analysis', '语义分析'],
          ['llm_anomaly_enhancement', '异常增强'],
        ].map(([key, label]) => (
          <FormControlLabel
            key={key}
            control={
              <Switch
                checked={form[key]}
                onChange={(e) => handleChange(key, e.target.checked)}
              />
            }
            label={label}
          />
        ))}

        <TextField
          label="LLM批大小"
          type="number"
          fullWidth
          variant="outlined"
          className={classes.formField}
          value={form.llm_batch_size}
          onChange={(e) => handleChange('llm_batch_size', Number(e.target.value))}
        />
        <TextField
          label="上下文窗口"
          type="number"
          fullWidth
          variant="outlined"
          className={classes.formField}
          value={form.llm_context_window}
          onChange={(e) => handleChange('llm_context_window', Number(e.target.value))}
        />
      </Collapse>
    </Box>
  );

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
  <DialogTitle>{isEditMode ? '编辑配置' : '创建新配置'}</DialogTitle>
  <DialogContent dividers style={{ maxHeight: '75vh', overflowY: 'auto', padding: 0 }}>
    <Tabs
      value={activeTab}
      onChange={handleTabChange}
      indicatorColor="primary"
      textColor="primary"
      variant="fullWidth"
      style={{ backgroundColor: '#f8f8f8' }}
    >
      <Tab label="基础配置" />
      <Tab label="模型配置" />
      <Tab label="分析配置" />
      <Tab label="序列分析" />
    </Tabs>

    <Box p={2}>
      {activeTab === 0 && renderBasicTab()}
      {activeTab === 1 && renderModelTab()}
      {activeTab === 2 && renderAnalysisTab()}
      {activeTab === 3 && renderSequenceTab()}
    </Box>
  </DialogContent>
  <DialogActions>
    <Button onClick={onClose}>取消</Button>
    <Button color="primary" variant="contained" onClick={() => onSubmit(form)}>
      {isEditMode ? '保存修改' : '创建配置'}
    </Button>
  </DialogActions>
</Dialog>

  );
};

export default ConfigDialog;