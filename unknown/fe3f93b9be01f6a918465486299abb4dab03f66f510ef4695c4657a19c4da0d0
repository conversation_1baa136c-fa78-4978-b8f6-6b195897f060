// src/pages/HoneyPotsOverviewBottom.tsx
import React, {  useEffect,useState } from 'react';
import './logtaskmanage.less'
import { Box, Grid, InputAdornment } from '@material-ui/core';
import { IconButton } from '@material-ui/core';
import CloseIcon from '@material-ui/icons/Close';
import { Snackbar } from '@material-ui/core';
import MuiAlert, { AlertProps } from '@material-ui/lab/Alert';
import Search from '@material-ui/icons/Search';
import LoopIcon from '@material-ui/icons/Loop';
import { Switch, FormControlLabel } from '@material-ui/core';
import { ConfigOption } from './config';
import ConfigDetailDialog from './configDetailDialog';

import {
  makeStyles,
  Typography,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  MenuItem,
} from '@material-ui/core';
import AnalyseModelTable from './analyseModeltable';
import apiClient from '../apis/apiClient';
import ConfigDialog from './configDialog';
import { Add, Cached } from '@material-ui/icons';
const useStyles = makeStyles((theme) => ({
  root: {
    height: '100%',
    //height: '94vh',
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: '#f5f5f5',
    padding: theme.spacing(0),
    boxSizing: 'border-box',
  },
  sectionMiddle: {
    // flex: 0.1,
    marginBottom: theme.spacing(1),
  },
  
  sectionBottom: {

    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
  },
  tableToolbar: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing(3),
  },
tableContainer: {
  flex: 1,
  maxHeight: '100%',
  minHeight: 0,
  backgroundColor: '#fff',
  display: 'flex',
  flexDirection: 'column',
  padding: theme.spacing(2),
},
customFocusButton: {
  '&:hover': {
    borderColor: '#000', // 鼠标悬浮时边框变黑
  },
  '&:focus': {
    outline: 'none !important',
    boxShadow: 'none !important',
  },
  '&.Mui-focusVisible': {
    outline: 'none !important',
    boxShadow: 'none !important',
  },
  '&:focus-visible': {
    outline: 'none !important',
    boxShadow: 'none !important',
  },
},
  formField: {
    marginBottom: theme.spacing(2),
  },
selectInput: {
  '& .MuiOutlinedInput-notchedOutline': {
    borderWidth: '1px',
  },
  '&:hover .MuiOutlinedInput-notchedOutline': {
    borderColor: '#ccc',
    borderWidth: '1px',
  },
  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
    borderColor: '#ccc !important',
    borderWidth: '1px',
  },
},
customOutlinedInput: {
    '& .MuiOutlinedInput-root': {
      '& fieldset': {
        borderColor: '#ccc', // 默认
        borderWidth: '1px',
      },
      '&:hover fieldset': {
        borderColor: '#000', // hover 黑色
        borderWidth: '1px',
      },
      '&.Mui-focused fieldset': {
        borderColor: '#000 !important', // focus 也黑色
        borderWidth: '1px',
      },
    },
  },
}));
type ConfigType = {
  id: number;
  config_name: string;
  config_type: string;
};

type FormDataType = {
  id: number;
  task_name: string;
  status: string;
  config_id: number;
  progress: number;
  threats_detected?: number; // 可选字段
  config?: ConfigType;       // 可选字段
  created_at: string;
  updated_at: string;
};

const defaultFormData: FormDataType = {
  id: 0,
  task_name: '',
  status: '',
  config_id: 0,
  progress: 0,
  created_at: '',
  updated_at: '',
};
const LogAnalyseModel: React.FC = () => {
  const classes = useStyles();

  const [openDialog, setOpenDialog] = useState(false);
const [isEditMode, setIsEditMode] = useState(false);
const [formData, setFormData] = useState<ConfigOption | undefined>(undefined);

  const [configList, setConfigList] = useState<ConfigOption[]>([]);
 
const handleDialogSubmit = async (form: any) => {
  try {
    const payload = {
      config_name: form.config_name,
      description: form.description,
      config_type: form.config_type,
      is_active: form.is_active,
      model_config:
        form.config_type === 'local'
          ? {
              model_type: 'local',
              model_path: form.model_path,
              torch_dtype: form.torch_dtype,
              device_map: form.device_map,
              trust_remote_code: form.trust_remote_code,
              temperature: form.temperature,
              max_tokens: form.max_tokens,
              top_p: form.top_p,
            }
          : {
              model_type: 'openai',
              api_key: form.api_key,
              api_base: form.api_base,
              model_name: form.model_name,
              temperature: form.temperature,
              max_tokens: form.max_tokens,
              top_p: form.top_p,
              timeout: form.timeout,
              max_retries: form.max_retries,
            },
      analysis_config: {
        confidence_threshold: form.confidence_threshold,
        max_recommendations: form.max_recommendations,
        batch_size: form.batch_size,
        enable_caching: form.enable_caching,
        ...(form.config_type === 'local' && {
          include_cwe_mapping: form.include_cwe_mapping,
          include_mitre_mapping: form.include_mitre_mapping,
        }),
      },
      sequence_config: {
        window_size: form.window_size,
        anomaly_threshold: form.anomaly_threshold,
        enable_llm_enhancement: form.enable_llm_enhancement,
        llm_enhancement_mode: form.llm_enhancement_mode,
        ...(form.min_sequence_length !== undefined && { min_sequence_length: form.min_sequence_length }),
        ...(form.llm_batch_size !== undefined && { llm_batch_size: form.llm_batch_size }),
        ...(form.llm_context_window !== undefined && { llm_context_window: form.llm_context_window }),
      },
    };

    const res = isEditMode
      ? await apiClient.put(`/api/log-analysis/configs/${form.id}`, payload)
      : await apiClient.post(`/api/log-analysis/configs/`, payload);

    if (res.data.code === 200 || res.status === 201) {
      showSnackbar(isEditMode ? '配置更新成功' : '配置创建成功', 'success');
      fetchModelConfigs();
      setOpenDialog(false);
    } else {
      const errorMsg = res.data.error || res.data.message || '操作失败';
      showSnackbar(errorMsg, 'error');
    }
  } catch (error: any) {
    console.error('提交配置失败', error);
    // 尝试从 error.response 中获取后端返回的错误信息
    const errorMsg =
      error?.response?.data?.error ||
      error?.response?.data?.message ||
      error.message ||
      '提交配置失败，请重试';
    showSnackbar(errorMsg, 'error');
  }
};



const [searchParams, setSearchParams] = useState({
  config_type: '',
  is_active: '',
});
const [actualSearchParams, setActualSearchParams] = useState({
  config_type: '',
  is_active: '',
});
const [confirmOpen, setConfirmOpen] = useState(false);
 const [deleteConfigTarget, setDeleteConfigTarget] = useState<ConfigOption | null>(null);
const [loading, setLoading] = useState(false);

  // ✅ 分页 + 数据状态
  const [page, setPage] = useState(0); // MUI 从 0 开始
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
//查看弹窗开关
const [detailDialogOpen, setDetailDialogOpen] = useState(false);
const [selectedConfigId, setSelectedConfigId] = useState<number | null>(null);


const [snackbarOpen, setSnackbarOpen] = useState(false);
const [snackbarMessage, setSnackbarMessage] = useState('');
const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');
const showSnackbar = (message: string, severity: 'success' | 'error' = 'success') => {
  setSnackbarMessage(message);
  setSnackbarSeverity(severity);
  setSnackbarOpen(true);
};
const fetchModelConfigs = async () => {
  setLoading(true);
  try {
    const res = await apiClient.get('/api/log-analysis/configs/', {
      params: {
  page: page + 1,
  per_page: pageSize,
  ...Object.fromEntries(
    Object.entries(actualSearchParams).filter(([_, v]) => v !== '' && v !== 'all')
  ),
},

    });

    const { items, pagination } = res.data;
    setConfigList(items);
    setTotal(pagination.total);
  } catch (err) {
    console.error('获取配置列表失败', err);
    showSnackbar('获取配置列表失败', 'error');
  } finally {
    setLoading(false);
  }
};

useEffect(() => {
  fetchModelConfigs();
}, [page, pageSize, actualSearchParams]);
const handleViewClick = (row: ConfigOption) => {
  setSelectedConfigId(row.id);
  setDetailDialogOpen(true);
};


const handleOpenEditDialog = (row: ConfigOption) => {
  setFormData(row);
  setIsEditMode(true);
  setOpenDialog(true);
};


const handleDeleteClick = (row: ConfigOption) => {
  setDeleteConfigTarget(row);
  setConfirmOpen(true);
};


  return (
    <div className={classes.root}>

      {/* 表格 + 工具栏 */}
      <div className={classes.sectionBottom}>
        <div className={classes.tableContainer}>
          {/* 搜索 + 添加 */}
         <div className={classes.tableToolbar}>
  {/* 左侧：状态 + 配置ID + 筛选按钮 */}
  <div style={{ display: 'flex', gap: 16, alignItems: 'center' }}>
   <TextField
  select
  label="配置类型"
  size="small"
  variant="outlined"
  value={searchParams.config_type}
  onChange={(e) =>
    setSearchParams((prev) => ({ ...prev, config_type: e.target.value }))
  }
  style={{ minWidth: 160 }}
>
  <MenuItem value="all">全部</MenuItem>
  <MenuItem value="local">本地模型</MenuItem>
  <MenuItem value="openai">OpenAI</MenuItem>
</TextField>

<Box display="flex" alignItems="center">
  <Typography style={{ marginRight: 8 }}>激活状态</Typography>
  <FormControlLabel
    control={
      <Switch
        checked={searchParams.is_active === 'true'}
        onChange={(e) => {
          const checked = e.target.checked;
          setSearchParams((prev) => ({
            ...prev,
            is_active: checked ? 'true' : '',
          }));
        }}
        color="primary"
      />
    }
    label={searchParams.is_active === 'true' ? '已激活' : '未激活'}
    labelPlacement="end"
  />
</Box>
  <Button
  variant="outlined"
  className={classes.customFocusButton}
  color="default"
  startIcon={<Search />}
  onClick={() => {
    setPage(0); // 重置页码
    setActualSearchParams({ ...searchParams }); // 仅此时更新搜索条件
  }}
>
  筛选
</Button>
<Button
  variant="outlined"
  className={classes.customFocusButton}
  color="default"
    startIcon={
     <Cached  />  // ✅ 镜像图标
    }
  onClick={() => {
    setSearchParams({ config_type: '', is_active: '' }); // 清空筛选条件
    setActualSearchParams({ config_type: '', is_active: '' });
    setPage(0); // 重置页码
  }}
>
  重置
</Button>

  </div>
  {/* 右侧：添加按钮 */}
 <Button
  variant="contained"
  color="primary"
  startIcon={<Add />}
  onClick={() => {
    setFormData(undefined);
    setIsEditMode(false);
    setOpenDialog(true);
  }}
>
  创建新配置
</Button>

</div>
          {/* 表格 */}
         <AnalyseModelTable
  data={configList}
  page={page}
  pageSize={pageSize}
  onView={handleViewClick}
  total={total}
  onPageChange={setPage}
  onRowsPerPageChange={setPageSize}
  onEdit={handleOpenEditDialog}
    onDelete={handleDeleteClick}
  loading={loading}
/> 
        </div>
      </div>
<Dialog
  open={confirmOpen}
  onClose={() => setConfirmOpen(false)}
>
  <DialogTitle>
    确认删除配置
    <IconButton
      aria-label="close"
      onClick={() => setConfirmOpen(false)}
      style={{
        position: 'absolute',
        right: 8,
        top: 8,
        color: '#999',
      }}
    >
      <CloseIcon />
    </IconButton>
  </DialogTitle>
  <DialogContent>
    <DialogContentText>
      确认要删除配置 “{deleteConfigTarget?.config_name}” 吗？此操作不可恢复！
    </DialogContentText>
  </DialogContent>
  <DialogActions>
    <Button onClick={() => setConfirmOpen(false)}>
      取消
    </Button>
    <Button
  onClick={async () => {
    if (deleteConfigTarget?.id) {
      try {
        const res = await apiClient.delete(`/api/log-analysis/configs/${deleteConfigTarget.id}`);

        if (res.data.code === 200) {
          showSnackbar('配置删除成功', 'success');

          // 删除后刷新列表
          const isLastItemOnPage = configList.length === 1;
          const isNotFirstPage = page > 0;
          if (isLastItemOnPage && isNotFirstPage) {
            setPage((prev) => prev - 1); // 触发 useEffect 刷新
          } else {
            await fetchModelConfigs(); // 手动刷新
          }
        } else {
          showSnackbar('删除失败：' + res.data.message, 'error');
        }
      } catch (error) {
        console.error('删除配置失败:', error);
        showSnackbar('删除配置失败，请检查网络或稍后再试', 'error');
      }
    }
    setConfirmOpen(false);
    setDeleteConfigTarget(null);
  }}
  color="secondary"
>
  确认删除
</Button>
  </DialogActions>
</Dialog>
<div>
<ConfigDialog
  key={isEditMode ? `edit-${formData?.id}` : 'create'} // 👈 关键点：强制刷新组件
  open={openDialog}
  initialData={formData}
  isEditMode={isEditMode}
  onClose={() => setOpenDialog(false)}
  onSubmit={handleDialogSubmit}
/>
</div>
<Snackbar
  open={snackbarOpen}
  autoHideDuration={1500}
  onClose={() => setSnackbarOpen(false)}
  anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
>
  <MuiAlert
    elevation={6}
    variant="filled"
    onClose={() => setSnackbarOpen(false)}
    severity={snackbarSeverity}
  >
    {snackbarMessage}
  </MuiAlert>
</Snackbar>
<ConfigDetailDialog
  open={detailDialogOpen}
  configId={selectedConfigId}
  onClose={() => setDetailDialogOpen(false)}
  onRefresh={fetchModelConfigs}
  showSnackbar={showSnackbar}
  onEdit={(config) => {
    setFormData(config);
    setIsEditMode(true);
    setOpenDialog(true);
  }}
/>


    </div>
  );
};
export default LogAnalyseModel;
