import React, { useState } from 'react';
import Box from '@material-ui/core/Box';
import Typography from '@material-ui/core/Typography';
import Grid from '@material-ui/core/Grid';
import AnalysisResultPanel from './compinents/AnalysisResultPanel';
import LogInputPanel from './compinents/LogInputPanel';
import LogTable from './compinents/LogTable';
import ModelConfigPanel from './compinents/ModelConfigPanel';

export default function LogAlertAnalysis() {
  const [selectedLogs, setSelectedLogs] = useState<number[]>([1, 4]);

  const handleToggle = (id: number) => {
    setSelectedLogs((prev) =>
      prev.includes(id) ? prev.filter((i) => i !== id) : [...prev, id]
    );
  };

  return (
    <div style={{ padding: 24, paddingBottom: 80, background: 'white' }}>
      <Typography variant="h5" style={{ color: '#065f46', fontWeight: 'bold' }} gutterBottom>
        智能日志分析
      </Typography>

      <Typography variant="body2" color="textSecondary" gutterBottom style={{ marginBottom: 16 }}>
        日志管理平台 &gt; 日志查看与分析
      </Typography>

      {/* 三栏等高布局 */}
      <Grid container spacing={2} style={{ height: 610 }}>
        {/* 日志输入 */}
        <Grid item xs={12} md={3}>
          <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <LogInputPanel />
          </div>
        </Grid>

        {/* 解析结果 */}
        <Grid item xs={12} md={6}>
          <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <AnalysisResultPanel />
          </div>
        </Grid>

        {/* 模型配置 */}
        <Grid item xs={12} md={3}>
          <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <ModelConfigPanel />
          </div>
        </Grid>
      </Grid>

      {/* 日志列表 */}
      <div style={{ marginTop: 32 }}>
        <LogTable selectedLogs={selectedLogs} onToggle={handleToggle} />
      </div>
    </div>
  );
}
