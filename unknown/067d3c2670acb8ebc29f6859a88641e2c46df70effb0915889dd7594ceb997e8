// src/pages/HoneyPotsOverviewBottom.tsx
import React, {  useEffect,useState } from 'react';
import './potmanage.less'
import { Grid, InputAdornment } from '@material-ui/core';
import { IconButton } from '@material-ui/core';
import CloseIcon from '@material-ui/icons/Close';
import { Snackbar } from '@material-ui/core';
import MuiAlert, { AlertProps } from '@material-ui/lab/Alert';
import Search from '@material-ui/icons/Search';
import LoopIcon from '@material-ui/icons/Loop';
import {
  makeStyles,
  Typography,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  MenuItem,
} from '@material-ui/core';
import HoneyPotTable from './HoneyPotTable';
import { statusTextMap } from './statusMap';
import apiClient from '../apis/apiClient';
import { Add, Cached, SearchOutlined } from '@material-ui/icons';
const useStyles = makeStyles((theme) => ({
  root: {
    height: '100%',
    //height: '94vh',
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: '#f5f5f5',
    padding: theme.spacing(0),
    boxSizing: 'border-box',
  },
  sectionMiddle: {
    // flex: 0.1,
    marginBottom: theme.spacing(1),
  },
  sectionBottom: {

    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
  },
  tableToolbar: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing(3),
  },
tableContainer: {
  flex: 1,
  maxHeight: '100%',
  minHeight: 0,
  backgroundColor: '#fff',
  display: 'flex',
  flexDirection: 'column',
  padding: theme.spacing(2),
},
customFocusButton: {
  '&:hover': {
    borderColor: '#000', // 鼠标悬浮时边框变黑
  },
  '&:focus': {
    outline: 'none !important',
    boxShadow: 'none !important',
  },
  '&.Mui-focusVisible': {
    outline: 'none !important',
    boxShadow: 'none !important',
  },
  '&:focus-visible': {
    outline: 'none !important',
    boxShadow: 'none !important',
  },
},
  formField: {
    marginBottom: theme.spacing(2),
  },
selectInput: {
  '& .MuiOutlinedInput-notchedOutline': {
    borderWidth: '1px',
  },
  '&:hover .MuiOutlinedInput-notchedOutline': {
    borderColor: '#ccc',
    borderWidth: '1px',
  },
  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
    borderColor: '#ccc !important',
    borderWidth: '1px',
  },
},
customOutlinedInput: {
    '& .MuiOutlinedInput-root': {
      '& fieldset': {
        borderColor: '#ccc', // 默认
        borderWidth: '1px',
      },
      '&:hover fieldset': {
        borderColor: '#000', // hover 黑色
        borderWidth: '1px',
      },
      '&.Mui-focused fieldset': {
        borderColor: '#000 !important', // focus 也黑色
        borderWidth: '1px',
      },
    },
  },
}));

type FormDataType = {
  id?: number;
  honeypot_name: string;
  honeypot_type: string;
  service_port: string;
  node_id: string;
  honeypot_status: string;
  log_storage_path: string;
  description: string;
};

type NodeOption = {
  id: number;
  name: string;
};

const defaultFormData: FormDataType = {
  honeypot_name: '',
  honeypot_type: '',
  service_port: '',
  node_id: '',
  honeypot_status: 'active',
  log_storage_path: '',
  description: '',
};
const HoneyPotsManage: React.FC = () => {
  const classes = useStyles();

  const [openDialog, setOpenDialog] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [formData, setFormData] = useState<FormDataType>(defaultFormData);
  const [nodeList, setNodeList] = useState<NodeOption[]>([]);
  const [searchName, setSearchName] = useState('');
  const [searchKeyword, setSearchKeyword] = useState(''); // 实际用于搜索的关键词
  
  const [activeButton, setActiveButton] = useState<'search' | 'reset' | null>(null);
const [confirmOpen, setConfirmOpen] = useState(false);
const [deleteTarget, setDeleteTarget] = useState<FormDataType | null>(null);
const [loading, setLoading] = useState(false);

  const handleOpenAddDialog = () => {
    setIsEditMode(false);
    setFormData(defaultFormData);
    setOpenDialog(true);
  };
  // ✅ 分页 + 数据状态
  const [page, setPage] = useState(0); // MUI 从 0 开始
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [honeypotList, setHoneypotList] = useState<FormDataType[]>([]);
const Alert = (props: AlertProps) => {
  return <MuiAlert elevation={6} variant="filled" {...props} />;
};
const [snackbarOpen, setSnackbarOpen] = useState(false);
const [snackbarMessage, setSnackbarMessage] = useState('');
const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');
const showSnackbar = (message: string, severity: 'success' | 'error' = 'success') => {
  setSnackbarMessage(message);
  setSnackbarSeverity(severity);
  setSnackbarOpen(true);
};
    // ✅ 请求蜜罐列表
 const fetchHoneypots = async () => {
  setLoading(true);
  try {
    const res = await apiClient.get('/api/search_honeypots', {
      params: {
        page: page + 1,
        page_size: pageSize,
        ...(searchKeyword.trim() ? { honeypot_name: searchKeyword.trim() } : {}),
      },
    });
    const { items, total } = res.data.data;
    setHoneypotList(items);
    setTotal(total);
  } catch (err) {
    console.error('获取蜜罐列表失败', err);
  } finally {
    setLoading(false); // ✅ 无论成功或失败都关闭 loading 状态
  }
};

const fetchNodes = async () => {
  try {
    const res = await apiClient.get('/api/nodes', {
      params: {
        
      },
    });
    const items = res.data.data.items;
    const nodes: NodeOption[] = items.map((item: any) => ({
      id: item.node_info.id,
      name: item.node_info.node_name,
    }));

    setNodeList(nodes);
  } catch (error) {
    console.error('获取节点列表失败', error);
  }
};
useEffect(() => {
  fetchNodes();
}, []);
 useEffect(() => {
  fetchHoneypots();
}, [page, pageSize,searchKeyword]); // 加上 searchName
  const handleOpenEditDialog = (rowData: FormDataType) => {
    setIsEditMode(true);
    setFormData({ ...rowData });
    setOpenDialog(true);
  };
  const handleCloseDialog = () => {
    setOpenDialog(false);
  };
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };
const handleSubmit = async () => {
 if (isEditMode) {
  try {
    const res = await apiClient.post('/api/update_honeypot', {
      honeypot_id: formData.id,
      honeypot_name: formData.honeypot_name,
      honeypot_type: formData.honeypot_type,
      service_port: Number(formData.service_port),
      node_id: Number(formData.node_id),
      honeypot_status: formData.honeypot_status,
      log_storage_path: formData.log_storage_path,
      description: formData.description,
    });
    if (res.data.code === 200) {
      fetchHoneypots(); // ✅ 更新成功后刷新表格
      handleCloseDialog(); // ✅ 关闭弹窗
      showSnackbar('蜜罐更新成功', 'success'); // ✅ 成功提示
    } else {
      showSnackbar('更新失败：' + res.data.message, 'error');
    }
  } catch (error) {
    console.error('更新蜜罐失败:', error);
    showSnackbar('更新蜜罐失败，请检查网络或稍后再试', 'error');
  }
}
else {
    try {
      const res = await apiClient.post('/api/create_honeypot', {
        ...formData,
        service_port: Number(formData.service_port),
        node_id: Number(formData.node_id),
      });
      if (res.data.code === 200) {
        fetchHoneypots();
        handleCloseDialog();
        showSnackbar('蜜罐添加成功', 'success'); // ✅ 成功提示
      } else {
        showSnackbar('添加失败：' + res.data.message, 'error'); // ❌ 失败提示
      }
    } catch (error) {
      console.error('添加蜜罐失败:', error);
      showSnackbar('添加蜜罐失败，请检查网络或稍后再试', 'error'); // ❌ 网络异常提示
    }
  }
};
const handleDelete = (row: FormDataType) => {
  setDeleteTarget(row);
  setConfirmOpen(true);
};
  return (
    <div className={classes.root}>

      {/* 表格 + 工具栏 */}
      <div className={classes.sectionBottom}>
        <div className={classes.tableContainer}>
          {/* 搜索 + 添加 */}
          <div className={classes.tableToolbar}>
  {/* 左侧：搜索框 + 搜索按钮 + 重置按钮 */}
  <div style={{ display: 'flex', gap: 8 }}>
    <TextField
  value={searchName}
  onChange={(e) => setSearchName(e.target.value)}
  onKeyDown={(e) => {
    if (e.key === 'Enter') {
      setPage(0);
      setSearchKeyword(searchName);
    }
  }}
  size="small"
  variant="outlined"
  className={classes.customOutlinedInput}
   id="outlined-adornment-weight"
  placeholder="请输入蜜罐名称"
  InputProps={{
    startAdornment: (
      <InputAdornment position="start">
        <SearchOutlined style={{ color: 'black' }} />
      </InputAdornment>
    ),
  }}
/>

   <Button
  className={classes.customFocusButton}
  variant="outlined"
  color={activeButton === 'search' ? 'primary' : 'default'}
  disableRipple
  disableElevation
  startIcon={<Search />}
  onClick={() => {
    setPage(0);
    setSearchKeyword(searchName);
    setActiveButton('search');
  }}
>
  搜索
</Button>
<Button
  className={classes.customFocusButton}
  variant="outlined"
  color={activeButton === 'reset' ? 'primary' : 'default'}
  disableRipple
  disableElevation
  startIcon={
   <Cached  />  // ✅ 镜像图标
  }
  onClick={() => {
    setSearchName('');
    setSearchKeyword('');
    setPage(0);
    setActiveButton('reset');
  }}
>
  重置
</Button>
  </div>
  {/* 右侧：添加按钮 */}
  <Button
  variant="contained"
  color="primary"
  startIcon={<Add />}
  onClick={handleOpenAddDialog}
>
  添加蜜罐
</Button>

</div>
          {/* 表格 */}
         <HoneyPotTable
  data={honeypotList}
  page={page}
  pageSize={pageSize}
  nodeList={nodeList}
  total={total}
  onPageChange={setPage}
  onRowsPerPageChange={setPageSize}
  onEdit={handleOpenEditDialog}
  onDelete={handleDelete} 
  loading={loading}
/>
        </div>
      </div>
      {/* 添加/编辑弹窗 */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth='md' fullWidth scroll="paper"
  PaperProps={{
    style: {
      height: '55vh', // 弹窗最大高度为视口的 80%
    },
  }}>
       <DialogTitle style={{ position: 'relative', paddingRight: 40 }}>
  {isEditMode ? '修改蜜罐' : '添加蜜罐'}
  <IconButton
    onClick={handleCloseDialog}
    style={{ position: 'absolute', right: 8, top: 8 }}
    aria-label="close"
    size="medium"
  >
    <CloseIcon />
  </IconButton>
</DialogTitle>
      <DialogContent>
  <Grid container spacing={4}>
    {/* 第一行：名称 + 类型 */}
    <Grid item xs={6}>
      <TextField
        label="蜜罐名称"
        name="honeypot_name"
        fullWidth
        variant="outlined"
        size="small"
        value={formData.honeypot_name}
        onChange={handleChange}
        required
      />
    </Grid>
    <Grid item xs={6}>
      <TextField
        label="蜜罐类型"
        name="honeypot_type"
        fullWidth
        variant="outlined"
        size="small"
        value={formData.honeypot_type}
        onChange={handleChange}
        required
      />
    </Grid>
    {/* 第二行：端口 + 节点 */}
    <Grid item xs={6}>
      <TextField
        label="服务端口"
        name="service_port"
        fullWidth
        variant="outlined"
        size="small"
        inputProps={{
    min: 1,
    max: 65535,
    step: 1,
    inputMode: 'numeric',
  }}
        value={formData.service_port}
        onChange={handleChange}
        required
      />
    </Grid>
    <Grid item xs={6}>
     {/* <TextField
  select
  label="关联节点"
  name="node_id"
  fullWidth
  variant="outlined"
  size="small"
  value={formData.node_id}
  onChange={handleChange}
  required
> */}
<TextField
  select
  label="关联节点"
  name="node_id"
  fullWidth
  variant="outlined"
  size="small"
  value={formData.node_id}
  onChange={handleChange}
  InputProps={{
    classes: {
      root: classes.selectInput,
    },
  }}
>
  {nodeList.map((node) => (
    <MenuItem key={node.id} value={node.id}>
      {node.name}
    </MenuItem>
  ))}
</TextField>

    </Grid>
    {/* 第三行：状态 + 日志路径 */}
    <Grid item xs={6}>
      <TextField
        select
        label="状态"
        name="honeypot_status"
        fullWidth
        variant="outlined"
        size="small"
        InputProps={{
    classes: {
      root: classes.selectInput,
    },
  }}
        value={formData.honeypot_status}
        onChange={handleChange}
      >
        {Object.entries(statusTextMap).map(([value, label]) => (
          <MenuItem key={value} value={value}>
            {label}
          </MenuItem>
        ))}
      </TextField>
    </Grid>
    <Grid item xs={6}>
      <TextField
        label="日志存储路径"
        name="log_storage_path"
        fullWidth
        variant="outlined"
        size="small"
        value={formData.log_storage_path}
        onChange={handleChange}
      />
    </Grid>
    {/* 第四行：备注（单独一行） */}
    <Grid item xs={12}>
      <TextField
        label="备注/描述"
        name="description"
        fullWidth
        variant="outlined"
        size="small"
        multiline
        rows={5}
        value={formData.description}
        onChange={handleChange}
      />
    </Grid>
  </Grid>
</DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>取消</Button>
          <Button color="primary" variant="contained" onClick={handleSubmit}>
            提交
          </Button>
        </DialogActions>
      </Dialog>
      <Snackbar
  open={snackbarOpen}
  autoHideDuration={3000}
  onClose={() => setSnackbarOpen(false)}
  anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
>
  <Alert onClose={() => setSnackbarOpen(false)} severity={snackbarSeverity}>
    {snackbarMessage}
  </Alert>
</Snackbar>
<Dialog
  open={confirmOpen}
  onClose={() => setConfirmOpen(false)}
>
  <DialogTitle>确认删除</DialogTitle>
  <DialogContent>
    <DialogContentText>
      确认要删除蜜罐 “{deleteTarget?.honeypot_name}” 吗？此操作不可恢复！
    </DialogContentText>
  </DialogContent>
  <DialogActions>
    <Button onClick={() => setConfirmOpen(false)} color="primary">
      取消
    </Button>
    <Button
      onClick={async () => {
        if (deleteTarget?.id) {
          try {
            const res = await apiClient.post('/api/delete_honeypot', {
              honeypot_id: deleteTarget.id,
            });
            if (res.data.code === 200) {
              showSnackbar('蜜罐删除成功', 'success');
              const isLastItemOnPage = honeypotList.length === 1;
              const isNotFirstPage = page > 0;
              if (isLastItemOnPage && isNotFirstPage) {
                setPage((prev) => prev - 1);
              } else {
                fetchHoneypots();
              }
            } else {
              showSnackbar('删除失败：' + res.data.message, 'error');
            }
          } catch (error) {
            console.error('删除蜜罐失败:', error);
            showSnackbar('删除蜜罐失败，请检查网络或稍后再试', 'error');
          }
        }
        setConfirmOpen(false);
        setDeleteTarget(null);
      }}
      color="secondary"
    >
      确认
    </Button>
  </DialogActions>
</Dialog>
    </div>
  );
};
export default HoneyPotsManage;
