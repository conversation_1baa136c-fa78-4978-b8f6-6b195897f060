import React, { useState } from 'react';
import {
  Table, TableBody, TableCell, TableContainer, TableHead, TableRow,
  Paper, Checkbox, Button, Box, Select, MenuItem, Pagination, Typography,
  TextField,
  Dialog,
  DialogTitle,
  IconButton,
} from '@mui/material';
import CloseIcon from '@material-ui/icons/Close';
import LogReport from './LogReport';

// 日志项类型定义
interface LogItem {
  id: number;
  source: string;
  content: string;
  result: string;
}

// 组件 Props 类型定义
interface LogTableProps {
  selectedLogs: number[];
  onToggle: (id: number) => void;
}

// 示例数据（共 44 条）
const logData: LogItem[] = [
  { id: 1, source: '防火墙', content: '日志一xxxxx', result: '日志一xxxxx' },
  { id: 2, source: 'IDS', content: '日志二xxxxx', result: '日志二xxxxx' },
  { id: 3, source: '防火墙', content: '日志三xxxxx', result: '日志三xxxxx' },
  { id: 4, source: '服务器', content: '日志四xxxxx', result: '日志四xxxxx' },
  { id: 5, source: 'IDS', content: '日志五xxxxx', result: '日志五xxxxx' },
  { id: 6, source: '防火墙', content: '日志六xxxxx', result: '日志六xxxxx' },
  { id: 7, source: 'WAF', content: '日志七xxxxx', result: '日志七xxxxx' },
  { id: 8, source: 'WAF', content: '日志八xxxxx', result: '日志八xxxxx' },
  { id: 9, source: 'WAF', content: '日志九xxxxx', result: '日志九xxxxx' },
  { id: 10, source: 'WAF', content: '日志十xxxxx', result: '日志十xxxxx' },
  { id: 11, source: '服务器', content: '日志四xxxxx', result: '日志四xxxxx' },
  { id: 12, source: 'IDS', content: '日志五xxxxx', result: '日志五xxxxx' },
  { id: 13, source: '防火墙', content: '日志六xxxxx', result: '日志六xxxxx' },
  { id: 14, source: 'WAF', content: '日志七xxxxx', result: '日志七xxxxx' },
  { id: 15, source: 'WAF', content: '日志八xxxxx', result: '日志八xxxxx' },
  { id: 16, source: 'WAF', content: '日志九xxxxx', result: '日志九xxxxx' },
  { id: 17, source: 'WAF', content: '日志十xxxxx', result: '日志十xxxxx' },
  { id: 18, source: '服务器', content: '日志四xxxxx', result: '日志四xxxxx' },
  { id: 19, source: 'IDS', content: '日志五xxxxx', result: '日志五xxxxx' },
  { id: 20, source: '防火墙', content: '日志六xxxxx', result: '日志六xxxxx' },
  { id: 21, source: 'WAF', content: '日志七xxxxx', result: '日志七xxxxx' },
  { id: 22, source: 'WAF', content: '日志八xxxxx', result: '日志八xxxxx' },
  { id: 23, source: 'WAF', content: '日志九xxxxx', result: '日志九xxxxx' },
  { id: 24, source: 'WAF', content: '日志十xxxxx', result: '日志十xxxxx' },
  { id: 25, source: '服务器', content: '日志四xxxxx', result: '日志四xxxxx' },
  { id: 26, source: 'IDS', content: '日志五xxxxx', result: '日志五xxxxx' },
  { id: 27, source: '防火墙', content: '日志六xxxxx', result: '日志六xxxxx' },
  { id: 28, source: 'WAF', content: '日志七xxxxx', result: '日志七xxxxx' },
  { id: 29, source: 'WAF', content: '日志八xxxxx', result: '日志八xxxxx' },
  { id: 30, source: 'WAF', content: '日志九xxxxx', result: '日志九xxxxx' },
  { id: 31, source: 'WAF', content: '日志十xxxxx', result: '日志十xxxxx' },
  { id: 32, source: '服务器', content: '日志四xxxxx', result: '日志四xxxxx' },
  { id: 33, source: 'IDS', content: '日志五xxxxx', result: '日志五xxxxx' },
  { id: 34, source: '防火墙', content: '日志六xxxxx', result: '日志六xxxxx' },
  { id: 35, source: 'WAF', content: '日志七xxxxx', result: '日志七xxxxx' },
  { id: 36, source: 'WAF', content: '日志八xxxxx', result: '日志八xxxxx' },
  { id: 37, source: 'WAF', content: '日志九xxxxx', result: '日志九xxxxx' },
  { id: 38, source: 'WAF', content: '日志十xxxxx', result: '日志十xxxxx' },
  { id: 39, source: '服务器', content: '日志四xxxxx', result: '日志四xxxxx' },
  { id: 40, source: 'IDS', content: '日志五xxxxx', result: '日志五xxxxx' },
  { id: 41, source: '防火墙', content: '日志六xxxxx', result: '日志六xxxxx' },
  { id: 42, source: 'WAF', content: '日志七xxxxx', result: '日志七xxxxx' },
  { id: 43, source: 'WAF', content: '日志八xxxxx', result: '日志八xxxxx' },
  { id: 44, source: 'WAF', content: '日志九xxxxx', result: '日志九xxxxx' },
];

const LogTable: React.FC<LogTableProps> = ({ selectedLogs, onToggle }) => {
  const [page, setPage] = useState(1); // 当前页码（从 1 开始）
  const [rowsPerPage] = useState(5);   // 每页条数固定为 5
  const [inputPage, setInputPage] = useState(''); // 跳转页码输入框

  const totalPages = Math.ceil(logData.length / rowsPerPage);
  const [openReportDialog, setOpenReportDialog] = useState(false);

  // 当前页数据
  const paginatedData = logData.slice((page - 1) * rowsPerPage, page * rowsPerPage);

  // 当前页是否全选
  const isAllSelected = paginatedData.every((log) => selectedLogs.includes(log.id));

  // 全选/取消全选
  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = paginatedData.map((log) => log.id);
      const merged = Array.from(new Set([...selectedLogs, ...newSelected]));
      onToggleAll(merged);
    } else {
      const remaining = selectedLogs.filter((id) => !paginatedData.some((log) => log.id === id));
      onToggleAll(remaining);
    }
  };


  // 批量更新选中项
  const onToggleAll = (newSelected: number[]) => {
    newSelected.forEach((id) => {
      if (!selectedLogs.includes(id)) onToggle(id);
    });
    selectedLogs.forEach((id) => {
      if (!newSelected.includes(id)) onToggle(id);
    });
  };

  // 页码跳转
  const handleJump = () => {
    const target = parseInt(inputPage, 10);
    if (!isNaN(target) && target >= 1 && target <= totalPages) {
      setPage(target);
      setInputPage('');
    }
  };

  // 分页器页码变更
  const handleChangePage = (_event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  return (
    <>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
        <Typography variant="h6">日志列表</Typography>
        <Box display="flex" gap={1}>
          <Button 
            variant="contained" 
            color="success"  
            onClick={() => setOpenReportDialog(true)}>
            日志报告导出
          </Button>
          <Button variant="contained" color="success">导出为 CSV</Button>
        </Box>
      </Box>

      {/* 表格区域 */}
      <TableContainer component={Paper} elevation={1}>
        <Table size="small">
          <TableHead>
            <TableRow>
              {/* 缩小复选框列宽 */}
              <TableCell align="center" sx={{ width: 40 }}>
                <Checkbox
                  checked={isAllSelected}
                  onChange={handleSelectAll}
                  color="primary"
                  size="small"
                />
              </TableCell>
              {/* 添加 ID 列 */}
              <TableCell align="center" sx={{ width: 60 }}>ID</TableCell>
              <TableCell align="center">日志来源</TableCell>
              <TableCell align="center">日志内容</TableCell>
              <TableCell align="center">分析结果</TableCell>
              <TableCell align="center">操作</TableCell>
            </TableRow>
          </TableHead>

          <TableBody>
            {paginatedData.map((log) => (
              <TableRow key={log.id} hover>
                {/* 缩小复选框列宽 */}
                <TableCell align="center" sx={{ width: 40 }}>
                  <Checkbox
                    checked={selectedLogs.includes(log.id)}
                    onChange={() => onToggle(log.id)}
                    color="primary"
                    size="small"
                  />
                </TableCell>
                {/* 显示 ID */}
                <TableCell align="center" sx={{ width: 60 }}>{log.id}</TableCell>
                <TableCell align="center">{log.source}</TableCell>
                <TableCell align="center">
                  {log.content}{' '}
                  <Button
                    variant="text"
                    size="small"
                    sx={{ color: '#22c55e', textTransform: 'none', fontWeight: 500 }}
                  >
                    查看
                  </Button>
                </TableCell>
                <TableCell align="center">
                  {log.result}{' '}
                  <Button
                    variant="text"
                    size="small"
                    sx={{ color: '#22c55e', textTransform: 'none', fontWeight: 500 }}
                  >
                    查看
                  </Button>
                </TableCell>
                <TableCell align="center">
                  <Button
                    variant="text"
                    size="small"
                    sx={{ color: '#22c55e', textTransform: 'none', fontWeight: 500 }}
                  >
                    详情
                  </Button>
                  <Button
                    variant="text"
                    size="small"
                    sx={{ color: '#22c55e', textTransform: 'none', fontWeight: 500 }}
                  >
                    删除
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* 分页器 + 跳转 */}
      <Box mt={2} display="flex" justifyContent="flex-end" alignItems="center" flexWrap="wrap">
        <Pagination
          count={totalPages}
          page={page}
          onChange={handleChangePage}
          color="primary"
          shape="rounded"
          showFirstButton
          showLastButton
        />
        <TextField
          size="small"
          variant="outlined"
          value={inputPage}
          onChange={(e) => setInputPage(e.target.value)}
          inputProps={{
            inputMode: 'numeric',
            pattern: '[0-9]*',
            style: { width: 20, height: 10, textAlign: 'center' },
          }}
        />
        <Typography variant="body2" sx={{ px: 1 }}>页</Typography>
        <Button
          variant="outlined"
          size="small"
          onClick={handleJump}
          sx={{ minWidth: 60 }}
        >
          跳转
        </Button>
      </Box>

      <Dialog
        open={openReportDialog}
        onClose={() => setOpenReportDialog(false)}
        fullWidth
        maxWidth="lg"
      >
        <DialogTitle>
          日志报告导出
          <IconButton
            aria-label="close"
            onClick={() => setOpenReportDialog(false)}
            style={{ position: 'absolute', right: 8, top: 8 }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <Box p={2}>
          <LogReport />
        </Box>
      </Dialog>
    </>
  );
};

export default LogTable;
