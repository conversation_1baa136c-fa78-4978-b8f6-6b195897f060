import { Button, <PERSON>alog, <PERSON>alogActions, DialogContent, DialogContentText, DialogTitle, Snackbar } from "@material-ui/core";
import Mui<PERSON><PERSON><PERSON>, { AlertProps } from "@material-ui/lab/Alert";
import { useState } from "react";
import { showSnackbar } from "../points-manage/component/myMessageBar";
import DeleteIcon from '@material-ui/icons/Delete';
import apiClient from "../apis/apiClient";

interface Session {
  session_id: string;
  session_name?: string;
  created_at?: string;
  updated_at?: string;
  last_message?: {
    created_at?: string;
    content?: string;
  };
}
interface ChatDeleteProps {
  session: Session;
  fetchSessions: () => void;
  setMessages: (messages: any) => void;
  setSessionId: (sessionId: any) => void;
}
export default function ChatDelete({session,fetchSessions,setMessages,setSessionId }:ChatDeleteProps) {
  const [open, setOpen] = useState(false);
  const fetchSessionsRef = fetchSessions
  const handleDeleteSession = async (e: React.MouseEvent<HTMLButtonElement, MouseEvent>,sessionId: any,sessionName: any) => {
    setOpen(true)
  }
  const handleDeleteConfirm =async () => {
    setOpen(false)
     try {
      const response = await apiClient.delete(`/api/chat/sessions/${session.session_id}`, {
      });
      if (response.data.success === true) {
        fetchSessions()
        setMessages([])
        setSessionId(null)
        showSnackbar("删除成功", "success");
      } else {
        showSnackbar("删除失败", "error");
      }
    } catch (error) {
      console.error(error);
      showSnackbar("删除失败", "error");
    }
  }


    return (
        <>
        <Button onClick={(e) => {handleDeleteSession(e,session.session_id,session.session_name)}} style={{minWidth: 0,padding: 4}}><DeleteIcon /></Button>
        <Dialog open={open}>
            <DialogTitle>确认删除</DialogTitle>
            <DialogContent>
              <DialogContentText>
                确认要删除日志源“{session.session_name}”吗？此操作不可恢复！
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button  color="primary" onClick={() => setOpen(false)}>
                取消
              </Button>
              <Button
                color="secondary" onClick={() => {handleDeleteConfirm()}}
              >
                确认
              </Button>
            </DialogActions>
        </Dialog>
        </>
    )
}

