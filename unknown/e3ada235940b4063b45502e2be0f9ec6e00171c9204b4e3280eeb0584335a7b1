// src/pages/ConfigDetailDialog.tsx
import React, { useEffect, useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  CircularProgress,
  Grid,
  Typography,
  Paper,
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import apiClient from '../apis/apiClient';

const useStyles = makeStyles((theme) => ({
  paperBox: {
    padding: theme.spacing(2),
    minHeight: 200,
    whiteSpace: 'pre-wrap',
    fontFamily: 'monospace',
    backgroundColor: '#f9f9f9',
    overflow: 'auto',
  },
  sectionTitle: {
    fontWeight: 'bold',
    marginBottom: theme.spacing(1),
    color: '#388e3c',
  },
  gridItem: {
    padding: theme.spacing(1),
  },
  actionBar: {
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%',
    flexWrap: 'wrap',
    padding: theme.spacing(2),
  },
  greenBtn: {
    backgroundColor: '#4caf50',
    color: '#fff',
    marginRight: 8,
    '&:hover': {
      backgroundColor: '#388e3c',
    },
  },
  yellowBtn: {
    backgroundColor: '#ffb300',
    color: '#fff',
    marginRight: 8,
    '&:hover': {
      backgroundColor: '#ffa000',
    },
  },
  grayBtn: {
    backgroundColor: '#90a4ae',
    color: '#fff',
    marginRight: 8,
    '&:hover': {
      backgroundColor: '#78909c',
    },
  },
  redBtn: {
    backgroundColor: '#f44336',
    color: '#fff',
    '&:hover': {
      backgroundColor: '#d32f2f',
    },
  },
}));

interface ConfigDetailDialogProps {
  open: boolean;
  configId: number | null;
  onClose: () => void;
  onRefresh?: () => void; // 可选：操作成功后刷新列表
  onEdit?: (configData: any) => void; // 可选：跳转编辑
  showSnackbar?: (msg: string, type?: 'success' | 'error') => void;
}

const ConfigDetailDialog: React.FC<ConfigDetailDialogProps> = ({
  open,
  configId,
  onClose,
  onRefresh,
  onEdit,
  showSnackbar,
}) => {
  const classes = useStyles();
  const [loading, setLoading] = useState(false);
  const [configData, setConfigData] = useState<any>(null);

  useEffect(() => {
    if (open && configId) {
      fetchConfigDetail(configId);
    }
  }, [open, configId]);

  const fetchConfigDetail = async (id: number) => {
    setLoading(true);
    try {
      const res = await apiClient.get(`/api/log-analysis/configs/${id}`);
      if (res.data.code === 200) {
        setConfigData(res.data.config);
      }
    } catch (err) {
      console.error('获取配置详情失败', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAction = async (action: 'activate' | 'deactivate' | 'set-default' | 'delete') => {
  if (!configData) return;
  const id = configData.id;
  let url = `/api/log-analysis/configs/${id}`;
  let successMsg = '';
  let res;

  try {
    switch (action) {
      case 'activate':
        url += '/activate';
        successMsg = '配置激活成功';
        res = await apiClient.post(url, { config_id: id });
        break;
      case 'deactivate':
        url += '/deactivate';
        successMsg = '配置停用成功';
        res = await apiClient.post(url, { config_id: id });
        break;
      case 'set-default':
        url += '/set-default';
        successMsg = '成功设为默认配置';
        res = await apiClient.post(url, { config_id: id });
        break;
      case 'delete':
        successMsg = '配置删除成功';
        res = await apiClient.delete(url);
        break;
    }

    if (res?.data?.code === 200) {
      showSnackbar?.(successMsg, 'success');
      onRefresh?.();
      onClose();
    } else {
      showSnackbar?.(res?.data?.message || '操作失败', 'error');
    }
  } catch (err) {
    console.error(`${action} 操作失败`, err);
    showSnackbar?.('操作失败，请稍后重试', 'error');
  }
};


  const renderJson = (obj: any) => JSON.stringify(obj, null, 2);

  const renderBasicInfo = (config: any) => ({
    id: config.id,
    name: config.config_name,
    type: config.config_type,
    description: config.description,
    is_active: config.is_active,
    is_default: config.is_default,
    created_at: config.created_at,
  });

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>配置详情</DialogTitle>
      <DialogContent dividers>
        {loading ? (
          <div style={{ textAlign: 'center', padding: 40 }}>
            <CircularProgress />
          </div>
        ) : configData ? (
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4} className={classes.gridItem}>
              <Typography className={classes.sectionTitle}>基础信息</Typography>
              <Paper className={classes.paperBox}>
                {renderJson(renderBasicInfo(configData))}
              </Paper>
            </Grid>
            <Grid item xs={12} sm={4} className={classes.gridItem}>
              <Typography className={classes.sectionTitle}>模型配置</Typography>
              <Paper className={classes.paperBox}>
                {renderJson(configData.model_config)}
              </Paper>
            </Grid>
            <Grid item xs={12} sm={4} className={classes.gridItem}>
              <Typography className={classes.sectionTitle}>分析配置</Typography>
              <Paper className={classes.paperBox}>
                {renderJson({
                  ...configData.analysis_config,
                  ...configData.sequence_config,
                })}
              </Paper>
            </Grid>
          </Grid>
        ) : (
          <Typography>无法加载配置数据</Typography>
        )}
      </DialogContent>

      {/* 操作按钮 */}
      <DialogActions className={classes.actionBar}>
        <div>
          <Button className={classes.greenBtn} onClick={() => handleAction('activate')}>
            激活配置
          </Button>
          <Button className={classes.yellowBtn} onClick={() => handleAction('deactivate')}>
            停用配置
          </Button>
          <Button className={classes.grayBtn} onClick={() => handleAction('set-default')}>
            设为默认
          </Button>
          <Button className={classes.redBtn} onClick={() => handleAction('delete')}>
            删除配置
          </Button>
        </div>
        <Button onClick={onClose} variant="outlined">
          关闭
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConfigDetailDialog;
