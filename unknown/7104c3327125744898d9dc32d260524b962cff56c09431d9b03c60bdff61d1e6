// src/pages/HoneyPotTable.tsx
import React from 'react';
import {
  Table, TableBody,  TableContainer, TableHead, TableRow,
  IconButton, Paper, TablePagination, makeStyles,withStyles,TableCell,
  CircularProgress
} from '@material-ui/core';
import Pagination from '@material-ui/lab/Pagination'
import { Edit, Delete } from '@material-ui/icons';
import { getStatusText } from './statusMap';
const useStyles = makeStyles((theme) => ({
  toolbar: {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: theme.spacing(2),
  },
}));
interface HoneyPotTableProps {
  data: any[];
  page: number;
  pageSize: number;
  total: number;
  loading?: boolean;
  nodeList: { id: number; name: string }[]; 
  onPageChange: (page: number) => void;
  onRowsPerPageChange: (size: number) => void;
  onEdit: (rowData: any) => void;
  onDelete: (rowData: any) => void; 
}

export default function HoneyPotTable(props: HoneyPotTableProps) {
  const {
    data = [],
    page,
    pageSize,
    total,
    onPageChange,
    onRowsPerPageChange,
    onEdit,
    nodeList,
     onDelete,
     loading
  } = props;
  const renderCell = (value: any) => {
  return value !== null && value !== undefined && value !== '' ? value : '/';
};

const StyledTableRow = withStyles(() => ({
  root: {
    height: 30, 
    '&:nth-of-type(odd)': {
      backgroundColor: '#f9f9f9',
    },
  },
}))(TableRow);

const StyledTableCell = withStyles(() => ({
  root: {
    paddingTop: 6,
    paddingBottom: 6,
    lineHeight: '1.4rem',
    textAlign: 'center',
  },
  head: {
    backgroundColor: '#babcbb',
    color: '#fff',
    textAlign: 'center',
  },
}))(TableCell);

  return (
  <>
    <TableContainer
      component={Paper}
      style={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        minHeight: 600,
      }}
    >
      {loading ? (
        <div
          style={{
            flex: 1,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: 300,
          }}
        >
          <CircularProgress size={48} />
        </div>
      ) : (
        <>
          <div style={{ flex: 1, overflowY: 'scroll', overflowX: 'hidden' }}>
            <Table size="small">
              <TableHead>
                <StyledTableRow>
                  <StyledTableCell>名称</StyledTableCell>
                  <StyledTableCell>类型</StyledTableCell>
                  <StyledTableCell>位置</StyledTableCell>
                  <StyledTableCell>端口</StyledTableCell>
                  <StyledTableCell>关联节点</StyledTableCell>
                  <StyledTableCell>状态</StyledTableCell>
                  <StyledTableCell>访问次数</StyledTableCell>
                  <StyledTableCell>操作</StyledTableCell>
                </StyledTableRow>
              </TableHead>
              <TableBody>
                {data.map((row) => (
                  <StyledTableRow key={row.id}>
                    <StyledTableCell>{renderCell(row.honeypot_name)}</StyledTableCell>
                    <StyledTableCell>{renderCell(row.honeypot_type)}</StyledTableCell>
                    <StyledTableCell>{renderCell(row.department)}</StyledTableCell>
                    <StyledTableCell>{renderCell(row.service_port)}</StyledTableCell>
                    <StyledTableCell>
                      {renderCell(
                        nodeList.find((node) => node.id === Number(row.node_id))?.name
                      )}
                    </StyledTableCell>
                    <StyledTableCell
                      style={{
                        color: row.honeypot_status === 'inactive' ? 'red' : 'inherit',
                      }}
                    >
                      {renderCell(getStatusText(row.honeypot_status))}
                    </StyledTableCell>
                    <StyledTableCell>{renderCell(row.id)}</StyledTableCell>
                    <StyledTableCell>
                      <IconButton onClick={() => onEdit(row)}>
                        <Edit />
                      </IconButton>
                      <IconButton onClick={() => onDelete(row)}>
                        <Delete color="error" />
                      </IconButton>
                    </StyledTableCell>
                  </StyledTableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* 分页区域 */}
          <div className="attack-list-pagination-div">
            <div>共 {total} 条数据</div>
            <Pagination
              className="attack-list-pagination"
              showFirstButton
              count={Math.ceil(total / pageSize)}
              page={page + 1} 
              onChange={(event, value) => onPageChange(value - 1)} 
            />
          </div>
        </>
      )}
    </TableContainer>
  </>
);

}
