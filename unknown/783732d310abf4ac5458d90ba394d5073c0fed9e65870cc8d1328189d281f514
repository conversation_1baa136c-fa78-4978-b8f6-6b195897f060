import React from "react";
import Header from "./component/Header";
import FeatureGrid from "./component/FeatureGrid";
import ConfigPanel from "./component/ConfigPanel";
import { Typography, Breadcrumbs, Link, Box } from "@mui/material";
export default function NoiseModelEngine() {
  return (
    <div
      style={{
        width: "100%",
        minHeight: "calc(100vh - 60px)",
        paddingBottom: 60,
      }}
    >
      <Box p={2}>
        <Typography variant="h5" color="success" fontWeight="bold" gutterBottom>
          智能降噪引擎
        </Typography>
        {/* Breadcrumbs */}
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
          <Link underline="hover" color="inherit" href="#">
            智能降噪中心
          </Link>
          <Typography color="text.primary">智能报告生成</Typography>
        </Breadcrumbs>

        {/* 标题和描述区块 */}
        <div>
          <p
            style={{
              // maxWidth: 800,
              // margin: "0 auto",
              color: "#666",
              lineHeight: 1.8,
              fontSize: "1rem",
            }}
          >
            基于AI技术的高级告警过滤系统，通过模式识别、白名单管理、时间序列分析和大模型辅助规则生成，大幅降低安全告警噪音，提高威胁检测精准度。
          </p>
        </div>
      </Box>
      <FeatureGrid />
      <ConfigPanel />
    </div>
  );
}
