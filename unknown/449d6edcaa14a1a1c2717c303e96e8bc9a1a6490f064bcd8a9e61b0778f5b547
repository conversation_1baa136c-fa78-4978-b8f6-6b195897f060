import React from 'react';
import { Paper, Typography, Chip } from '@material-ui/core';

type MessageType = 'WARNING' | 'INFO' | 'SECURITY' | 'ERROR';

interface AnalysisMessage {
  type: MessageType;
  text: string;
  ip: string;
}

const analysisMessages: AnalysisMessage[] = [
  { type: 'WARNING', text: 'firewall drop packet', ip: '************' },
  { type: 'INFO', text: 'admin login successful', ip: '************' },
  { type: 'SECURITY', text: 'Failed login attempt', ip: '************' },
  { type: 'ERROR', text: 'DB connection timeout', ip: '10.105.12.3306' },
  { type: 'WARNING', text: 'firewall drop packet', ip: '************' },
  { type: 'INFO', text: 'admin login successful', ip: '************' },
  { type: 'SECURITY', text: 'Failed login attempt', ip: '************' },
  { type: 'ERROR', text: 'DB connection timeout', ip: '10.105.12.3306' },
  { type: 'WARNING', text: 'firewall drop packet', ip: '************' },
  { type: 'INFO', text: 'admin login successful', ip: '************' },
  { type: 'SECURITY', text: 'Failed login attempt', ip: '************' },
  { type: 'ERROR', text: 'DB connection timeout', ip: '10.105.12.3306' },
  { type: 'WARNING', text: 'firewall drop packet', ip: '************' },
  { type: 'INFO', text: 'admin login successful', ip: '************' },
  { type: 'SECURITY', text: 'Failed login attempt', ip: '************' },
  { type: 'ERROR', text: 'DB connection timeout', ip: '10.105.12.3306' },
  { type: 'WARNING', text: 'firewall drop packet', ip: '************' },
  { type: 'INFO', text: 'admin login successful', ip: '************' },
  { type: 'SECURITY', text: 'Failed login attempt', ip: '************' },
  { type: 'ERROR', text: 'DB connection timeout', ip: '10.105.12.3306' },
  { type: 'ERROR', text: 'DB connection timeout', ip: '10.105.12.3306' },
];

const typeColor: Record<MessageType, 'default' | 'primary' | 'secondary'> = {
  WARNING: 'default',
  INFO: 'primary',
  SECURITY: 'secondary',
  ERROR: 'secondary', // fallback
};

const chipStyle: Record<MessageType, React.CSSProperties> = {
  WARNING: { backgroundColor: '#facc15', color: '#000' },
  INFO: { backgroundColor: '#3b82f6', color: '#fff' },
  SECURITY: { backgroundColor: '#8b5cf6', color: '#fff' },
  ERROR: { backgroundColor: '#ef4444', color: '#fff' },
};

const AnalysisResultPanel: React.FC = () => {
  return (
    <Paper
      elevation={2}
      style={{
        padding: 16,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
      }}
    >
      <Typography variant="h6" style={{ color: '#065f46', fontWeight: 'bold' }}>
        解析结果
      </Typography>

      <div
        style={{
          height: 2,
          backgroundColor: '#bbf7d0',
          width: '100%',
          marginTop: 12,
          marginBottom: 12,
          borderRadius: 4,
        }}
      />

      {/* 滚动区域 */}
      <div style={{ overflowY: 'auto', maxHeight: 520  }}>
        {analysisMessages.map((msg, index) => (
          <div key={index} style={{ marginBottom: 8 }}>
            <Chip
              label={msg.type}
              color={typeColor[msg.type]}
              size="small"
              style={{ marginRight: 8, ...chipStyle[msg.type] }}
            />
            <Typography variant="body2" component="span">
              {msg.text} — <strong>{msg.ip}</strong>
            </Typography>
          </div>
        ))}
      </div>
    </Paper>
  );
};

export default AnalysisResultPanel;
