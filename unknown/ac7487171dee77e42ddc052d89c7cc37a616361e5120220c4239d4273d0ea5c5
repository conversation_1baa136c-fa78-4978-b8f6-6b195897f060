import React, { useEffect } from 'react';
import { <PERSON><PERSON>, DialogTitle, DialogContent, <PERSON>alog<PERSON><PERSON>, Button, Switch, Slider,  } from '@material-ui/core';
import styles from '../css/configForm.module.css';
import { ConfigType , SilenceRule, EscalationRule, ClusteringRule} from '../types/types';
import { MuiPickersUtilsProvider, DateTimePicker } from '@material-ui/pickers';
import DateFnsUtils from '@date-io/date-fns';
// utils/date.ts
function normalizeDate(value: any): string | null {
    if (!value) return null;
  
    if (value instanceof Date) {
      return value.toISOString();
    }
  
    try {
      const d = new Date(value);
      if (!isNaN(d.getTime())) {
        return d.toISOString();
      }
    } catch (e) {
      console.error("Invalid date:", value);
    }
  
    return null;
  }
  
  

export default function ConfigForm({ open, onClose, onSave, config }: any) {
    // 静默规则的状态
    const [silenceRule, setSilenceRule] = React.useState<SilenceRule>({
        rule_name: '',
        condition: '',
        expire_at: new Date(),
        is_open: true,
      });

    // 升级规则的状态
    const [escalationRule, setEscalationRule] = React.useState<EscalationRule>({
        rule_name: '',
        condition: '',
        threshold_count: 10,
        threshold_period: 60,
        new_severity: '严重',
        is_open: true,
      });
    // 聚类规则的状态
    const [clusteringRule, setClusteringRule] = React.useState<ClusteringRule>({
        eps: 0.5,
        time_window_minutes: 10,
        min_cluster_size: 2,
        cluster_features: [],
    });
    
      

    // 默认数据
    const [form, setForm] = React.useState<ConfigType>({
        config_name: '',
        config_type: 'local', // 'local' or 'openai'
        description: '',
        model_config: {
            model_type: 'local',
            model_path: '',
            api_key: '',
            api_base: '',
            model_name: '',
            temperature: 0.3,
            max_tokens: 512,
        },
        noise_reduction_config: {
            similarity_threshold: 0.8,
            time_window_minutes: 60,
            confidence_threshold: 0.7,
            enable_clustering: true,
            enable_suppression: true,
            enable_priority_adjustment: true,
        },
    });
    React.useEffect(() => {
        if (open) {
            if (config) {
                // 初始化通用配置
            setForm(prev => ({
                ...prev,
                config_name: config.config_name,
                config_type: config.config_type,
                description: config.description,
                model_config: config.model_config || {},
                noise_reduction_config: config.noise_reduction_config || {}
            }));
            // 根据类型初始化特定配置
            switch (config.config_type) {
                case 'silence':
                const silenceConfig = config.specificConfig || {
                    rule_name: '',
                    condition: '',
                    expire_at: new Date(),
                    is_open: true
                };

                // 👇 转换 expire_at 为 Date 对象
                const expireAtDate = new Date(silenceConfig.expire_at);
                setSilenceRule({
                    ...silenceConfig,
                    expire_at: isNaN(expireAtDate.getTime()) ? new Date() : expireAtDate
                });
                break;

                    
                case 'escalate':
                    setEscalationRule(config.specificConfig || {
                        rule_name: '',
                        condition: '',
                        threshold_count: 10,
                        threshold_period: 60,
                        new_severity: '严重',
                        is_open: true
                    });
                    break;
                    
                case 'clustering':
                    setClusteringRule(config.specificConfig || {
                        eps: 0.5,
                        time_window_minutes: 10,
                        min_cluster_size: 2,
                        cluster_features: []
                    });
                    break;
            }
            } else {
                // 新增时清空
                setForm({
                    config_name: '',
                    config_type: 'local',
                    description: '',
                    model_config: {
                        model_type: 'local',
                        model_path: '',
                        api_key: '',
                        api_base: '',
                        model_name: '',
                        temperature: 0.3,
                        max_tokens: 512,
                    },
                    noise_reduction_config: {
                        similarity_threshold: 0.8,
                        time_window_minutes: 60,
                        confidence_threshold: 0.7,
                        enable_clustering: true,
                        enable_suppression: true,
                        enable_priority_adjustment: true,
                    },
                });
                //  清空静默规则
                setSilenceRule({
                    rule_name: '',
                    condition: '',
                    expire_at: new Date(),
                    is_open: true,
                });

                //  清空升级规则
                setEscalationRule({
                    rule_name: '',
                    condition: '',
                    threshold_count: 10,
                    threshold_period: 60,
                    new_severity: '严重',
                    is_open: true,
                });
                // 清空聚类规则
                setClusteringRule({
                    eps: 0.5,
                    time_window_minutes: 10,
                    min_cluster_size: 2,
                    cluster_features: [],
                })
            }
        }
    }, [open, config]);


    // 受控表单处理
    const handleChange = (field: string, value: any) => setForm(f => ({ ...f, [field]: value }));
    const handleModelConfigChange = (field: string, value: any) =>
        setForm(f => ({ ...f, model_config: { ...f.model_config!, [field]: value } }));
    const handleNoiseChange = (field: string, value: any) =>
        setForm(f => ({ ...f, noise_reduction_config: { ...f.noise_reduction_config, [field]: value } }));

    // 提交
    const handleSubmit = (e: any) => {
        e.preventDefault();

        let finalForm: any = {
            config_name: form.config_name,
            config_type: form.config_type,
            description: form.description,
        };

        if (form.config_type === 'local' || form.config_type === 'openai') {
            finalForm.model_config = form.model_config;
            finalForm.noise_reduction_config = form.noise_reduction_config;
        } else if (form.config_type === 'silence') {
            finalForm.silence_config = {
                rule_name: silenceRule.rule_name,
                condition: silenceRule.condition,
                expire_at: normalizeDate(silenceRule.expire_at), // 确保是 ISO 字符串
                is_open: silenceRule.is_open
            };
        } else if (form.config_type === 'escalate') {
            finalForm.escalate_config = {
                rule_name: escalationRule.rule_name,
                condition: escalationRule.condition,
                threshold_count: escalationRule.threshold_count,
                threshold_period: escalationRule.threshold_period.toString(),
                new_severity: escalationRule.new_severity,
                is_open: escalationRule.is_open,
            };
            
        } else if(form.config_type === 'clustering') {
            finalForm.cluster_config = {
                eps: clusteringRule.eps,
                time_window_minutes: clusteringRule.time_window_minutes,
                min_cluster_size: clusteringRule.min_cluster_size,
                cluster_features: clusteringRule.cluster_features
              };
              

              
        } 

        // 如果是编辑操作，添加 id 字段
        if (config?.id) {
            finalForm.id = config.id;
        }
        onSave(finalForm);
    };

    // 切换类型时清空相关字段
    React.useEffect(() => {
        if (form.config_type === 'local') {
            setForm(f => ({
                ...f,
                model_config: {
                    ...f.model_config,
                    model_type: 'local',
                    model_path: '',
                    api_key: '',
                    api_base: '',
                    model_name: '',
                }
            }));
        } else if (form.config_type === 'openai') {
            setForm(f => ({
                ...f,
                model_config: {
                    ...f.model_config,
                    model_type: 'openai',
                    model_path: '',
                }
            }
        ));
        }
    }, [form.config_type]);

    return (
        <Dialog open={open} maxWidth="md" fullWidth>
            <form className={styles.form} onSubmit={handleSubmit}>
                <DialogTitle>配置参数</DialogTitle>
                <DialogContent className={styles.content}>
                    {/* 基础信息 */}
                    <div className={styles.section}>
                        <div className={styles.sectionTitle}><span>📝</span> 基础信息</div>
                        <div className={styles.formRow}>
                            <label className={styles.label}>配置名称 *</label>
                            <input
                                className={styles.input}
                                value={form.config_name}
                                onChange={e => handleChange('config_name', e.target.value)}
                                required
                            />
                            
                        </div>
                        <div className={styles.formRow}>
                            <label className={styles.label}>配置类型 *</label>
                            <select
                                className={styles.input}
                                value={form.config_type}
                                onChange={e => handleChange('config_type', e.target.value)}
                            >
                                <option value="local">本地模型</option>
                                <option value="openai">OpenAI</option>
                                <option value="silence">静默规则</option>
                                <option value="escalate">升级规则</option>
                                <option value="clustering">聚类规则</option>
                            </select>
                        </div>
                        <div className={styles.formRow}>
                            <label className={styles.label}>描述</label>
                            <input
                                className={styles.input}
                                value={form.description}
                                onChange={e => handleChange('description', e.target.value)}
                            />
                        </div>
                    </div>
                    {/* 静默规则对应内容 */}
                    {form.config_type === 'silence' && (
                        <div className={styles.section}>
                            <div className={styles.sectionTitle}><span>🔕</span> 静默规则</div>

                            <div className={styles.formRow}>
                            <label className={styles.label}>规则名称</label>
                            <input
                                className={styles.input}
                                placeholder="请输入规则名称"
                                value={silenceRule.rule_name}
                                onChange={e => setSilenceRule(r => ({ ...r, rule_name: e.target.value }))}
                            />
                            </div>

                            <div className={styles.formRow}>
                            <label className={styles.label}>条件表达式</label>
                            <div className={styles.columnWrapper}>
                                <textarea
                                className={`${styles.input} ${styles.expressionTextarea}`}
                                placeholder="例：severity == 'critical' && host == 'web-server-01'"
                                rows={4}
                                value={silenceRule.condition}
                                onChange={e => setSilenceRule(r => ({ ...r, condition: e.target.value }))}
                                />
                                <small className={styles.expressionTip}>使用 JavaScript 语法编写条件表达式</small>
                            </div>
                            </div>
                            
                            
                            <div className={styles.formRow}>
                            <label className={styles.label}>过期时间</label>
                            <MuiPickersUtilsProvider utils={DateFnsUtils}>
                                <DateTimePicker
                                value={silenceRule.expire_at}
                                onChange={(date) => {
                                    if (date instanceof Date && !isNaN(date.getTime())) {
                                      setSilenceRule(r => ({ ...r, expire_at: date }));
                                    }
                                  }}
                                  
                                inputVariant="outlined"
                                format="yyyy/MM/dd HH:mm"
                                className={styles.input}
                                />
                            </MuiPickersUtilsProvider>
                            </div>

                            <div className={styles.formRow}>
                            <label className={styles.label}>状态</label>
                            <select
                                className={styles.input}
                                value={silenceRule.is_open ? 'true' : 'false'} 
                                onChange={e => setSilenceRule(r => ({ ...r, is_open: e.target.value === 'true' }))}
                            >
                                <option value="true">启用</option>
                                <option value="false">禁用</option>
                            </select>
                            </div>
                        </div>
                    )}
                    {/* 升级规则对应内容 */}
                    {form.config_type === 'escalate' && (
                    <div className={styles.section}>
                        <div className={styles.sectionTitle}><span>📈</span> 升级规则</div>

                        <div className={styles.formRow}>
                        <label className={styles.label}>规则名称</label>
                        <input
                            className={styles.input}
                            placeholder="请输入规则名称"
                            value={escalationRule.rule_name}
                            onChange={e => setEscalationRule(r => ({ ...r, rule_name: e.target.value }))}
                        />
                        </div>

                        <div className={styles.formRow}>
                        <label className={styles.label}>条件表达式</label>
                        <div className={styles.columnWrapper}>
                            <textarea
                            className={`${styles.input} ${styles.expressionTextarea}`}
                            placeholder="例：severity == 'critical' && host == 'web-server-01'"
                            rows={4}
                            value={escalationRule.condition}
                            onChange={e => setEscalationRule(r => ({ ...r, condition: e.target.value }))}
                            />
                            <small className={styles.expressionTip}>使用 JavaScript 语法编写条件表达式</small>
                        </div>
                        </div>

                        <div className={styles.formRow}>
                        <label className={styles.label}>触发阈值 (次数):</label>
                        <input
                            type="number"
                            className={styles.input}
                            value={escalationRule.threshold_count}
                            onChange={e => setEscalationRule(r => ({ ...r, threshold_count: +e.target.value }))}
                        />
                        </div>

                        <div className={styles.formRow}>
                        <label className={styles.label}>时间周期 (分钟):</label>
                        <input
                            type="number"
                            className={styles.input}
                            value={escalationRule.threshold_period}
                            onChange={e => setEscalationRule(r => ({ ...r, threshold_period : +e.target.value }))}
                        />
                        </div>

                        <div className={styles.formRow}>
                        <label className={styles.label}>升级至严重级:</label>
                        <select
                            className={styles.input}
                            value={escalationRule.new_severity}
                            onChange={e => setEscalationRule(r => ({ ...r, new_severity: e.target.value as '严重' | '警告' | '错误' }))}
                        >
                            <option value="严重">严重</option>
                            <option value="警告">警告</option>
                            <option value="错误">错误</option>
                        </select>
                        </div>

                        <div className={styles.formRow}>
                        <label className={styles.label}>状态</label>
                        <select
                            className={styles.input}
                            value={escalationRule.is_open ? 'true' : 'false'}
                            onChange={e => setEscalationRule(r => ({ ...r, is_open: e.target.value === 'true' }))}
                        >
                            <option value="true">启用</option>
                            <option value="false">禁用</option>     
                        </select>
                        </div>
                    </div>
                    )}
                    {/* 聚类规则对应内容 */}
                    {form.config_type === 'clustering' && (
                        <div className={styles.section}>
                            <div className={styles.sectionTitle}><span>🔍</span> 聚类规则</div>

                            <div className={styles.formRow}>
                                <label className={styles.label}>半径 </label>
                                <div className={styles.sliderGroup}>
                                    <Slider
                                        value={clusteringRule.eps}
                                        min={0}
                                        max={1}
                                        step={0.01}
                                        onChange={(e, newValue) => setClusteringRule(r => ({ ...r, eps: newValue as number }))}
                                        className={styles.slider}
                                    />
                                    <span className={styles.sliderValue}>{clusteringRule.eps}</span>
                                </div>
                                
                            </div>
                            

                            <div className={styles.formRow}>
                                <label className={styles.label}>时间窗口 (分钟)</label>
                                <input
                                    type="number"
                                    className={styles.input}
                                    value={clusteringRule.time_window_minutes}
                                    onChange={e => setClusteringRule(r => ({ ...r, time_window_minutes: +e.target.value }))}
                                />
                            </div>
                        

                            <div className={styles.formRow}>
                                <label className={styles.label}>最小聚类数</label>
                                <input
                                    type="number"
                                    className={styles.input}
                                    value={clusteringRule.min_cluster_size}
                                    onChange={e => setClusteringRule(r => ({ ...r, min_cluster_size: parseInt(e.target.value) }))}
                                />
                            </div>

                            <div className={styles.formRow}>
                                <label className={styles.label}>聚类特征</label>
                                <input
                                    type="text"
                                    className={styles.input}
                                    placeholder="输入多个特征值，用逗号分隔"
                                    value={clusteringRule.cluster_features.join(', ')} // 显示为字符串
                                    onChange={e => {
                                        const newValue = e.target.value
                                            .split(',')
                                            .map(str => str.trim())
                                            .filter(str => str.length > 0);
                                        setClusteringRule(r => ({ ...r, cluster_features: newValue }));
                                    }}
                                    />

                            </div>
                        </div>
                    )}


                    

                {(form.config_type === 'local' || form.config_type === 'openai') && (
                <>                
                    {/* 模型配置 */}
                    <div className={styles.section}>
                        <div className={styles.sectionTitle}><span>🤖</span> 模型配置</div>
                        {form.config_type === 'local' && (
                            <div className={styles.formRow}>
                                <label className={styles.label}>模型路径 *</label>
                                <input
                                    className={styles.input}
                                    value={form.model_config!.model_path}
                                    onChange={e => handleModelConfigChange('model_path', e.target.value)}
                                    required
                                />
                            </div>
                        )}
                        {form.config_type === 'openai' && (
                            <>
                                <div className={styles.formRow}>
                                    <label className={styles.label}>API Key *</label>
                                    <input
                                        className={styles.input}
                                        value={form.model_config!.api_key}
                                        onChange={e => handleModelConfigChange('api_key', e.target.value)}
                                        required
                                    />
                                </div>
                                <div className={styles.formRow}>
                                    <label className={styles.label}>API Base</label>
                                    <input
                                        className={styles.input}
                                        value={form.model_config!.api_base}
                                        onChange={e => handleModelConfigChange('api_base', e.target.value)}
                                    />
                                </div>
                                <div className={styles.formRow}>
                                    <label className={styles.label}>模型名称</label>
                                    <input
                                        className={styles.input}
                                        value={form.model_config!.model_name}
                                        onChange={e => handleModelConfigChange('model_name', e.target.value)}
                                    />
                                </div>
                            </>
                        )}
                        
                        

                        <div className={styles.formRow}>
                            <label className={styles.label}>温度</label>
                            <div className={styles.sliderGroup}>
                                <Slider
                                    value={form.model_config!.temperature}
                                    min={0}
                                    max={1}
                                    step={0.01}
                                    onChange={(_, v) => handleModelConfigChange('temperature', v as number)}
                                    className={styles.slider}
                                />
                                <span className={styles.sliderValue}>{form.model_config!.temperature}</span>
                            </div>
                        </div>
                        <div className={styles.formRow}>
                            <label className={styles.label}>最大Token数 *</label>
                            <input
                                type="number"
                                className={styles.input}
                                value={form.model_config!.max_tokens}
                                onChange={e => handleModelConfigChange('max_tokens', +e.target.value)}
                                required
                            />
                        </div>
                    </div>

                    {/* 降噪规则 */}
                    <div className={styles.section}>
                        <div className={styles.sectionTitle}><span>🛠️</span> 降噪规则</div>
                        <div className={styles.formRow}>
                            <label className={styles.label}>相似度阈值</label>
                            <div className={styles.sliderGroup}>
                                <Slider
                                    value={form.noise_reduction_config!.similarity_threshold}
                                    min={0}
                                    max={1}
                                    step={0.01}
                                    onChange={(_, v) => handleNoiseChange('similarity_threshold', v as number)}
                                    className={styles.slider}
                                />
                                <span className={styles.sliderValue}>{form.noise_reduction_config!.similarity_threshold}</span>
                            </div>
                        </div>
                        <div className={styles.formRow}>
                            <label className={styles.label}>时间窗口 (分钟)</label>
                            <input
                                type="number"
                                className={styles.input}
                                value={form.noise_reduction_config!.time_window_minutes}
                                onChange={e => handleNoiseChange('time_window_minutes', +e.target.value)}
                            />
                        </div>
                        <div className={styles.formRow}>
                            <label className={styles.label}>置信度阈值</label>
                            <div className={styles.sliderGroup}>
                                <Slider
                                    value={form.noise_reduction_config!.confidence_threshold}
                                    min={0}
                                    max={1}
                                    step={0.01}
                                    onChange={(_, v) => handleNoiseChange('confidence_threshold', v as number)}
                                    className={styles.slider}
                                />
                                <span className={styles.sliderValue}>{form.noise_reduction_config!.confidence_threshold}</span>
                            </div>
                        </div>
                        <div className={styles.formRow}>
                            <label className={styles.label}>启用聚类</label>
                            <Switch
                                checked={form.noise_reduction_config!.enable_clustering}
                                onChange={e => handleNoiseChange('enable_clustering', e.target.checked)}
                                color="primary"
                            />
                        </div>
                        <div className={styles.formRow}>
                            <label className={styles.label}>启用抑制</label>
                            <Switch
                                checked={form.noise_reduction_config!.enable_suppression}
                                onChange={e => handleNoiseChange('enable_suppression', e.target.checked)}
                                color="primary"
                            />
                        </div>
                        <div className={styles.formRow}>
                            <label className={styles.label}>启用优先级调整</label>
                            <Switch
                                checked={form.noise_reduction_config!.enable_priority_adjustment}
                                onChange={e => handleNoiseChange('enable_priority_adjustment', e.target.checked)}
                                color="primary"
                            />
                        </div>
                    </div>
                    </>)}
                </DialogContent>
                <DialogActions>
                    <Button onClick={onClose}>取消</Button>
                    <Button color="primary" variant="contained" type="submit">保存</Button>
                </DialogActions>
            </form>
        </Dialog>
    );
}
