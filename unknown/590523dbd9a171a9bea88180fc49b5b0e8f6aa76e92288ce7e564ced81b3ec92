import React, { useEffect, useState } from 'react';
import {
  Box,
  Button,
  makeStyles,
  Theme,
  createStyles,
  IconButton,
  Snackbar,
} from '@material-ui/core';
import CloseIcon from '@material-ui/icons/Close';
import DashboardCards from './cards';
import DashboardCharts from './chart';
import ThreatList from './list';

const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    root: { width: '100%', minHeight: '100vh', background: '#f5f7fa',paddingBottom: 50,  },
    // section: { margin: theme.spacing(2, 0) },
   card: {
  padding: theme.spacing(2),
  borderRadius: 8,
  boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
  background: '#fff',
  minHeight: 230,
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
},
cardHover: {
  transition: 'transform 0.2s cubic-bezier(.4,2,.6,1), box-shadow 0.2s',
  '&:hover': {
    transform: 'translateY(8px)',
    boxShadow: '0 8px 24px rgba(30,132,73,0.15)'
  }
},
scoreRed: { color: '#e74c3c' },
scoreOrange: { color: '#f39c12' },

rollingIpBox: {
  height: 72, // 3行*24px
  overflow: 'hidden',
  position: 'relative',
},
rollingIpList: {
  display: 'flex',
  flexDirection: 'column',
  transition: 'transform 0.5s cubic-bezier(.4,2,.6,1)',
},
    metricHeader: { display: 'flex', justifyContent: 'space-between', alignItems: 'center' },
    metricValue: { fontSize: 32, fontWeight: 700, color: '#1e8449', margin: theme.spacing(1, 0) },
    detailLabel: { borderRadius: 10, padding: '2px 8px', fontSize: 12, fontWeight: 500 },
    high: { background: 'rgba(231,76,60,0.1)', color: '#e74c3c' },
    medium: { background: 'rgba(243,156,18,0.1)', color: '#f39c12' },
    low: { background: 'rgba(52,152,219,0.1)', color: '#3498db' },
    chatbotBtn: {
  position: 'fixed',
  bottom: 520,
  right:0,
  zIndex: 1000,
  //boxShadow: '0 4px 24px rgba(30,132,73,0.16)',
  background: 'transparent', // 明确指定透明
  '&:hover': {
    background: 'rgba(0,0,0,0.04)', // 可选，略微灰色波纹
  },
  padding: 8,
},

    dialogPaper: { borderRadius: 12 },
    quickActions: { display: 'flex', justifyContent: 'space-between', margin: theme.spacing(2, -1) },
    quickBtn: { flex: 1, margin: theme.spacing(0, 1), borderRadius: 8, background: '#fff', border: '1px solid #e0e0e0', padding: theme.spacing(2, 0), '&:hover': { background: '#1e8449', color: '#fff' } },
    chartHeader: {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: 8,
},
chartSwitchBtn: {
  minWidth: 100,
  marginLeft: 8,
  fontWeight: 500,
  fontSize: 16,
  borderRadius: 6,
},
  })
);

const ipPool = [
  '***********', '*********', '************',
  '*************', '**************', '************',
  '*************', '**************', '*************'
];

const threatList = [
  { ip: '************', type: 'SQL注入', asset: '华北-变电站A' },
  { ip: '***********', type: 'RDP暴力破解', asset: '华东-电厂B' },
  { ip: '************', type: '工控协议漏洞', asset: '华南-变电站C' },
  { ip: '***********', type: 'XSS跨站脚本', asset: '华中-调度中心' },
  { ip: '*************', type: '权限提升', asset: '西北-电厂D' },
];

const HomePage: React.FC = () => {
  const classes = useStyles();
  const [honeypotCount, setHoneypotCount] = useState(1024);
  const [rollingIPs, setRollingIPs] = useState<string[]>(ipPool.slice(0, 3));
  const [chatOpen, setChatOpen] = useState(false);
  const [notification, setNotification] = useState<string | null>(null);
  const [isolated, setIsolated] = useState<{ [ip: string]: boolean }>({});

  // 蜜罐计数
  useEffect(() => {
    const timer = setInterval(() => {
      setHoneypotCount(c => c + Math.floor(Math.random() * 5));
    }, 3000);
    return () => clearInterval(timer);
  }, []);

  // 滚动IP
  useEffect(() => {
    const timer = setInterval(() => {
      const shuffled = [...ipPool].sort(() => 0.5 - Math.random());
      setRollingIPs(shuffled.slice(0, 3));
    }, 15000);
    return () => clearInterval(timer);
  }, []);

  // 隔离按钮
  const handleIsolate = (ip: string) => {
    setTimeout(() => {
      setIsolated(prev => ({ ...prev, [ip]: true }));
      setNotification(`已成功隔离IP: ${ip}`);
    }, 500);
  };

  // 快捷操作
  const handleQuickAction = (text: string) => {
    setNotification(`正在执行: ${text}`);
  };

  // 合规率仪表盘
 const complianceOption = {
  series: [{
    type: 'gauge',
    center: ['50%', '62%'],      // 仪表盘整体稍下移
    radius: '90%',               // 半径适中
    startAngle: 180,
    endAngle: 0,
    min: 0,
    max: 100,
    splitNumber: 10,
    axisLine: {
      lineStyle: {
        width: 20,
        color: [
          [0.7, '#e74c3c'],
          [0.85, '#f39c12'],
          [1, '#2ecc71']
        ]
      }
    },
    pointer: {
      length: '65%',             // 指针不要太长
      width: 4,
      itemStyle: { color: 'black' }
    },
    axisTick: { length: 12, lineStyle: { color: 'auto', width: 2 } },
    splitLine: { length: 20, lineStyle: { color: 'auto', width: 3 } },
    axisLabel: {
      color: '#7f8c8d',
      fontSize: 12,
      distance: -30,
      formatter: (value: number) => {
        if (value === 100) return 'A';
        if (value === 80) return 'B';
        if (value === 60) return 'C';
        if (value === 40) return 'D';
        if (value === 20) return 'E';
        return '';
      }
    },
    title: {
      show: true,
      offsetCenter: [0, '-20%'], // “合规率”上移
      fontSize: 16
    },
    detail: {
      fontSize: 25,
      offsetCenter: [0, '35%'],  // 数值下移
      valueAnimation: true,
      formatter: '{value}%',
      color: '#1e8449'
    },
    data: [{ value: 89, name: '合规率' }]
  }]
};


  // 风险趋势
  const riskTrendOption = {
    grid: { left: 0, right: 0, top: 0, bottom: 0 },
    xAxis: { type: 'category', show: false, data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'] },
    yAxis: { type: 'value', show: false, min: 50, max: 90 },
    series: [{
      data: [65, 68, 70, 75, 78, 76],
      type: 'line',
      smooth: true,
      symbol: 'none',
      lineStyle: { width: 3, color: '#e74c3c' },
      areaStyle: {
        color: {
          type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(231, 76, 60, 0.3)' },
            { offset: 1, color: 'rgba(231, 76, 60, 0.1)' }
          ]
        }
      }
    }]
  };

  // 告警趋势
  const alertTrendOption = {
    tooltip: { trigger: 'axis' },
    legend: { data: ['原始告警', '降噪后告警'], right: 10, top: 0, textStyle: { color: '#7f8c8d' } },
    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      axisLine: { lineStyle: { color: '#e0e0e0' } },
      axisLabel: { color: '#7f8c8d' }
    },
    yAxis: {
      type: 'value',
      axisLine: { lineStyle: { color: '#e0e0e0' } },
      axisLabel: { color: '#7f8c8d' },
      splitLine: { lineStyle: { color: '#f0f0f0' } }
    },
    series: [
      {
        name: '原始告警',
        type: 'line',
        data: [1200, 1320, 1010, 1340, 900, 830, 1170],
        smooth: true,
        lineStyle: { width: 3, color: '#1e8449' },
        itemStyle: { color: '#1e8449' },
        areaStyle: {
          color: {
            type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(30, 132, 73, 0.3)' },
              { offset: 1, color: 'rgba(30, 132, 73, 0.1)' }
            ]
          }
        }
      },
      {
        name: '降噪后告警',
        type: 'line',
        data: [820, 932, 701, 934, 690, 630, 820],
        smooth: true,
        lineStyle: { width: 3, color: '#3498db' },
        itemStyle: { color: '#3498db' },
        areaStyle: {
          color: {
            type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(52, 152, 219, 0.3)' },
              { offset: 1, color: 'rgba(52, 152, 219, 0.1)' }
            ]
          }
        }
      }
    ]
  };

  return (
    <Box className={classes.root}>
      {/* 核心指标卡片 */}
      <Box  style={{ marginBottom:'8px' }}>
      <DashboardCards
  honeypotCount={honeypotCount}
  rollingIPs={ipPool}  // 传全部ip
  complianceOption={complianceOption}
  riskTrendOption={riskTrendOption}
  classes={classes}
/>
</Box>
<Box  style={{ marginBottom:'18px' }}>
    {/* 中间可视化 */}
    <DashboardCharts
      alertTrendOption={alertTrendOption}
      classes={classes}
    />
</Box>
<Box >
    {/* TOP威胁列表 */}
    <ThreatList
      threatList={threatList}
      isolated={isolated}
      handleIsolate={handleIsolate}
      classes={classes}
></ThreatList>
</Box>
      <Box className={classes.quickActions}>
        {['查看最新告警', '启动蜜罐诱捕', '运行配置检测', '生成日报', '数据备份'].map(action => (
          <Button key={action} className={classes.quickBtn} onClick={() => handleQuickAction(action)}>
            {action}
          </Button>
        ))}
      </Box>
       <IconButton
  className={classes.chatbotBtn}
  onClick={() => setChatOpen(true)}
>
</IconButton>

      {/* 通知 */}
      <Snackbar
        open={!!notification}
        message={notification}
        autoHideDuration={3000}
        onClose={() => setNotification(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      />
    </Box>
  );
};
export default HomePage;
