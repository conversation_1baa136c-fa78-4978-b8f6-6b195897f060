import React, { useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import { Paper, Select, MenuItem, Button, DialogTitle, Dialog, DialogContent, DialogContentText, DialogActions } from '@material-ui/core';
import { Pagination } from '@material-ui/lab';
import axios from 'axios';
import styles from '../css/configList.module.css';
import { ConfigType } from '../types/types';
import apiClient from '../../../pages/apis/apiClient';
import { showSnackbar } from './myMessageBar';
import { Add, Cached } from '@material-ui/icons';



interface ConfigListProps {
    onEdit: (config: ConfigType) => void;
    onDelete: (id: number) => void;
    onAdd: () => void;
    // ref: React.RefObject<{ reload: () => void }>;
}




const ConfigList = forwardRef(({ onEdit, onDelete, onAdd }: ConfigListProps, ref) => {
    const [typeFilter, setTypeFilter] = useState<string>('');
    const [activeFilter, setActiveFilter] = useState<string>('');
    const [page, setPage] = useState(1);
    const [rowsPerPage] = useState(10);
    const [total, setTotal] = useState(0);
    const [configs, setConfigs] = useState<ConfigType[]>([]);
    const [loading, setLoading] = useState(false);
    const [confirmOpen, setConfirmOpen] = useState(false);
    const [deleteConfigId, setDeleteConfigId] = useState<number | null>(null);

    useImperativeHandle(ref, () => ({
        reload: fetchData
    }));
    const fetchData = (params = {}) => {
        setLoading(true);
        apiClient.get('/api/noise-reduction/configs', {
            params: {
                config_type: typeFilter || undefined,
                is_active: activeFilter === '' ? undefined : activeFilter === 'true',
                page,
                per_page: rowsPerPage,
                ...params,
            }
        }).then(resp => {
            const newTotal = resp.data.pagination?.total || 0;
            const maxPage = Math.max(1, Math.ceil(newTotal / rowsPerPage));
            if (page > maxPage) {
                setPage(maxPage);
                setLoading(false);
                return;
            }
            // 统一处理配置数据
            const parsedConfigs = resp.data.configs.map((config: { model_config: any; noise_reduction_config: any; config_type: any; silence_rule: {}; escalate_rule: {}; cluster_rule: {}; }) => {
            // 提取通用配置
            const commonConfig = {
                model_config: config.model_config || {},
                noise_reduction_config: config.noise_reduction_config || {}
            };

            // 处理特定规则配置
            let specificConfig = {};
            switch (config.config_type) {
                case 'silence':
                    specificConfig = config.silence_rule || {};
                    break;
                case 'escalate':
                    specificConfig = config.escalate_rule || {};
                    break;
                case 'clustering':
                    specificConfig = config.cluster_rule || {};
                    break;
                default:
                    break;
            }

            return { 
                ...config,
                ...commonConfig,  // 所有类型都包含这两个字段
                specificConfig   // 保存特定规则数据
            };
        });

        setConfigs(parsedConfigs);
            setTotal(newTotal);
        }, err => {
            setConfigs([]);
            setTotal(0);
        }).finally(() => {
            setLoading(false);
        });
    };




    // 依赖变化就请求
    useEffect(() => {
        fetchData();
        // eslint-disable-next-line
    }, [typeFilter, activeFilter, page, rowsPerPage]);



    // 切换分页
    const handleChangePage = (_: unknown, newPage: number) => {
        setPage(newPage);
    };

    // badge class
    const badgeClass = (type: string) =>
        type === 'local'
            ? `${styles.badge} ${styles.badgePrimary}`
            : `${styles.badge} ${styles.badgeSecondary}`;
    const activeBadgeClass = (active: boolean) =>
        active
            ? `${styles.badge} ${styles.badgePrimary}`
            : `${styles.badge} ${styles.badgeSecondary}`;
    const defaultBadgeClass = (isDefault: boolean) =>
        isDefault
            ? `${styles.badge} ${styles.badgePrimary}`
            : `${styles.badge} ${styles.badgeSecondary}`;

    return (
        <Paper className={styles.card} elevation={0}>
            <div className={styles.toolbar}>
                <div className={styles.filterBar}>
                    <div className={styles.filterItem}>
                        <label>配置类型</label>
                        <Select
                            value={typeFilter}
                            onChange={e => {
                                setTypeFilter(e.target.value as string);
                                setPage(1);
                            }}
                            fullWidth
                            displayEmpty
                            variant="outlined"
                            style={{ fontSize: 14, background: '#fff', height: 36 }}
                            inputProps={{ style: { padding: 8, borderRadius: 4 } }}
                        >
                            <MenuItem value="">全部</MenuItem>
                            <MenuItem value="local">本地模型</MenuItem>
                            <MenuItem value="openai">OpenAI</MenuItem>
                            <MenuItem value="silence">静默规则</MenuItem>
                            <MenuItem value="escalate">升级规则</MenuItem>
                            <MenuItem value="clustering">聚类规则</MenuItem>
                            
                        </Select>
                    </div>
                    <div className={styles.filterItem}>
                        <label>激活状态</label>
                        <Select
                            value={activeFilter}
                            onChange={e => {
                                setActiveFilter(e.target.value as string);
                                setPage(1);
                            }}
                            fullWidth
                            displayEmpty
                            variant="outlined"
                            style={{ fontSize: 14, background: '#fff', height: 36 }}
                            inputProps={{ style: { padding: 8, borderRadius: 4 } }}
                        >
                            <MenuItem value="">全部</MenuItem>
                            <MenuItem value="true">已激活</MenuItem>
                            <MenuItem value="false">未激活</MenuItem>
                        </Select>
                    </div>
                    <div className={styles.filterItem}>
                        <label> &nbsp;</label>
                        <Button
                            className={`${styles.search_select_reload_button} ${styles.noFocus}`}
                            onClick={() => {
                                setActiveFilter("");
                                setTypeFilter("");
                                setPage(1);
                            }}
                            startIcon={<Cached />}
                        >
                            <span style={{ paddingTop: "1px" }}>
                                重置
                            </span>
                        </Button></div>

                </div>
                <div className={styles.addConfigBtn}>
                    <Button variant='contained' color="primary" className={`${styles.btn} ${styles.btnPrimary}`} startIcon={<Add />} onClick={onAdd}>
                        <span style={{ paddingTop: "3px" }}>添加配置</span>
                    </Button>
                </div>
            </div>
            {/* 表格 */}
            <div className={styles.tableContainer}>
                <table className={styles.table}>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>配置名称</th>
                            <th>类型</th>
                            <th>激活状态</th>
                            <th>是否默认</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody className={styles.tbody}>
                        {loading && (
                            <tr>
                                <td colSpan={8} style={{ textAlign: 'center', color: '#aaa', padding: 32 }}>
                                    加载中...
                                </td>
                            </tr>
                        )}
                        {!loading && configs.length === 0 && (
                            <tr>
                                <td colSpan={8} style={{ textAlign: 'center', color: '#aaa', padding: 32 }}>
                                    暂无数据
                                </td>
                            </tr>
                        )}
                        {!loading && configs.map((row, index) => (
                            <tr key={row.id}>
                                <td>{index + (rowsPerPage * (page - 1)) + 1}</td>
                                <td>{row.config_name}</td>
                                <td>
                                    <span className={badgeClass(row.config_type || '')}>{row.config_type || '-'}</span>
                                </td>
                                <td>
                                    <span className={activeBadgeClass(!!row.is_active)}>
                                        {row.is_active ? '已激活' : '未激活'}
                                    </span>
                                </td>
                                <td>
                                    <span className={defaultBadgeClass(!!row.is_default)}>
                                        {row.is_default ? '是' : '否'}
                                    </span>
                                </td>
                                <td>{row.created_at?.replace('T', ' ').slice(0, 16)}</td>
                                <td>
                                    <Button className={`${styles.btn} ${styles.btnOutline} btn-sm`} size="small" onClick={() => onEdit(row)}>编辑</Button>
                                    <Button className={`${styles.btn} ${styles.btnOutline} btn-sm`} size="small" onClick={() => { setDeleteConfigId(row.id!); setConfirmOpen(true); }}>删除</Button>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
                {/* 分页 */}
                <div className={styles.attack_list_pagination_div}>
                    <div>共 {total} 条数据</div>
                    <Pagination
                        className={styles.attack_list_pagination}
                        showFirstButton
                        count={Math.ceil(total / rowsPerPage)}
                        page={page}
                        onChange={handleChangePage}
                    />
                </div>
            </div>

            <Dialog
                open={confirmOpen}
                onClose={() => setConfirmOpen(false)}
            >
                <DialogTitle>确认删除</DialogTitle>
                <DialogContent>
                    <DialogContentText>
                        确认要删除该配置吗？此操作不可恢复！
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setConfirmOpen(false)} color="primary">
                        取消
                    </Button>
                    <Button
                        onClick={() => {
                            if (deleteConfigId !== null) {
                                onDelete(deleteConfigId);
                            }
                            setConfirmOpen(false);
                            setDeleteConfigId(null);
                        }}
                        color="primary"
                    >
                        确认
                    </Button>
                </DialogActions>
            </Dialog>

        </Paper >
    );
})
export default ConfigList;
