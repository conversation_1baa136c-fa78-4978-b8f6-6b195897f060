export type ConfigOption = {
  id: number;
  config_name: string;
  config_type: 'local' | 'openai';
  is_active: boolean;
  is_default: boolean;
  description: string;
  created_at: string;
  updated_at: string;
  created_by: string;

  analysis_config: {
    batch_size: number;
    confidence_threshold: number;
    enable_caching: boolean;
    include_cwe_mapping: boolean;
    include_mitre_mapping: boolean;
    max_recommendations: number;
  };

  model_config: {
    api_base: string | null;
    api_key: string | null;
    device_map: string;
    max_retries: number | null;
    max_tokens: number;
    model_name: string | null;
    model_path: string | null;
    model_type: 'local' | 'openai';
    temperature: number;
    timeout: number | null;
    top_p: number;
    torch_dtype: string;
    trust_remote_code: boolean;
  };

  sequence_config: {
    anomaly_threshold: number;
    enable_behavioral_detection: boolean;
    enable_llm_enhancement: boolean;
    enable_pattern_detection: boolean;
    enable_statistical_detection: boolean;
    enable_temporal_detection: boolean;
    learning_period: number;
    llm_anomaly_enhancement: boolean;
    llm_batch_size: number;
    llm_context_window: number;
    llm_enhancement_mode: string;
    llm_pattern_analysis: boolean;
    llm_semantic_analysis: boolean;
    max_sequences_per_batch: number;
    min_sequence_length: number;
    window_size: number;
  };
};
