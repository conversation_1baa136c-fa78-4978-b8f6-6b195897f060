import React, { useEffect, useState } from 'react';
import ReactEcharts from 'echarts-for-react';
import * as echarts from 'echarts';

const attackData = [
  { name: '哈尔滨市', value: 120 },
  { name: '齐齐哈尔市', value: 90 },
  { name: '牡丹江市', value: 60 },
  { name: '佳木斯市', value: 40 },
  { name: '大庆市', value: 70 },
  { name: '伊春市', value: 55 },
  { name: '鸡西市', value: 35 },
  { name: '鹤岗市', value: 25 },
  { name: '双鸭山市', value: 30 },
  { name: '七台河市', value: 20 },
  { name: '绥化市', value: 50 },
  { name: '黑河市', value: 15 },
  { name: '大兴安岭地区', value: 10 }
];


const AttackMap: React.FC = () => {
  const [mapLoaded, setMapLoaded] = useState(false);

  useEffect(() => {
   fetch('/heilongjiang.json')
      .then(res => res.json())
      .then(json => {
        echarts.registerMap('heilongjiang', json as any);
        setMapLoaded(true);
      });
  }, []);

  const attackMapOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}<br/>攻击次数: {c}'
    },
    visualMap: {
      min: 0,
      max: 120,
      left: 20,
      bottom: 20,
      text: ['高', '低'],
      inRange: {
        color: ['#e0f7fa', '#90caf9', '#1976d2', '#d32f2f']
      },
      calculable: true
    },
    geo: {
      map: 'heilongjiang',
      roam: true,
      label: {
        show: true,
        color: '#333'
      },
      itemStyle: {
        areaColor: '#f5f7fa',
        borderColor: '#aaa'
      },
      emphasis: {
        label: { show: true, color: '#000' },
        itemStyle: { areaColor: '#ffe082' }
      }
    },
    series: [
      {
        name: '攻击次数',
        type: 'map',
        map: 'heilongjiang',
        geoIndex: 0,
        data: attackData
      }
    ]
  };

  if (!mapLoaded) return <div>地图加载中...</div>;

  return (
    <ReactEcharts
      option={attackMapOption}
      style={{ height: 300, width: '100%' }}
    />
  );
};

export default AttackMap;
