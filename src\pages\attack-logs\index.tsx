import React,{ useState } from 'react';
import AttackChart from './components/attackChart';
import Dashboard from './components/dashboard';

export default function AttackLogs() {
   // 用于接收图表传来的总数
  const [totals, setTotals] = useState({
    ssh: 0,
    mysql: 0,
    http: 0,
    pop3: 0,
  });
  return (
    <div style={{ width: '100%', height: '155vh', background: 'white', padding: 20, boxSizing: 'border-box' }}>
      <h2 style={{ textAlign: 'center' ,fontWeight:'normal'}}>
        欢迎来到 <span style={{ color: '#000' ,fontWeight: 'bold' }}>蜜罐信息</span> WebUI
      </h2>
      <p style={{ textAlign: 'center',  }}>
        SSH事件数: <span style={{ color: '#000' ,fontWeight: 'bold'}}>{totals.ssh}</span> &nbsp;
        MySQL事件数: <span style={{ color: '#000' ,fontWeight: 'bold'}}>{totals.mysql}</span> &nbsp;
        HTTP事件数: <span style={{ color: '#000' ,fontWeight: 'bold'}}>{totals.http}</span>&nbsp;
        POP3事件数: <span style={{ color: '#000' ,fontWeight: 'bold'}}>{totals.pop3}</span>&nbsp;
       </p>
       {/* 传入 setTotals 函数 */}
       <AttackChart onTotalChange={setTotals} />
      <Dashboard />
    </div>
  );
}