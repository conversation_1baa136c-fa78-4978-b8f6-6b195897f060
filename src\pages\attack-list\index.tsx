import { useEffect, useState } from 'react'
import {
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  MenuItem,
  FormControl,
  withStyles,
  CircularProgress,
  Snackbar,
} from '@material-ui/core'
import Pagination from '@material-ui/lab/Pagination'
import MuiAlert, { AlertProps } from '@material-ui/lab/Alert'
import { SaveAlt as SaveAltIcon } from '@material-ui/icons'
import SearchIcon from '@material-ui/icons/Search'
import './index.less'
import CachedIcon from '@material-ui/icons/Cached'
import axios from 'axios'
// 数据类型
type DataType = {
  port_dest: number
  port_src: number
  protocol: string
  module_name: string
  machine_name: string
  ip_src: string
  ip_dest: string
  date: string
  country_ip_dest: string
  country_ip_src: string
}

function Alert(props: AlertProps) {
  return <MuiAlert elevation={6} variant="filled" {...props} />
}

const StyledTableCell = withStyles((theme) => ({
  head: {
    backgroundColor: '#babcbb',
    color: theme.palette.common.white,
    textAlign: 'center',
  },
  body: {
    fontSize: 14,
    textAlign: 'center',
  },
}))(TableCell)

const StyledTableRow = withStyles((theme) => ({
  root: {
    '&:nth-of-type(odd)': {
      backgroundColor: theme.palette.action.hover,
    },
  },
}))(TableRow)

const api = import.meta.env.VITE_BACK_URL // 接口url
export default function AttackList() {
  const rowsPerPage = 10 // 每页条数

  // React.state
  const [selectedDate, setSelectedDate] = useState<string>('') // 时间选择
  const [selectedModule, setSelectedModule] = useState<string>('') // 模块选择
  const [selectedEvent, setSelectedEvent] = useState<string>('') // 事件选择
  const [searchParams, setSearchParams] = useState({
    selectedDate: '',
    selectedModule: '',
    selectedEvent: '',
  })
  const [page, setPage] = useState<number>(1) // 页码
  const [dataList, setDataList] = useState<DataType[]>([]) // 表格内的数据
  const [total, setTotal] = useState<number>(0) // 总数量
  const [loading, setLoading] = useState<boolean>(false) // 数据加载状态
  const [alertOpen, setAlertOpen] = useState<boolean>(false) // 提示框状态
  const [alertMessage, setAlertMessage] = useState<string>('') // 提示框内容
  const [moduleList, setModuleList] = useState<string[]>([]) // 模块名称集合

  // Restful.API
  // 获取全部模块名称
  async function getModules() {
    try {
      const response = await axios.get(`${api}/api/core/list/modules`)
      setModuleList(response.data)
    } catch (error: any) {
      setAlertOpen(true)
      setAlertMessage(error?.message)
    }
  }

  // 获取数据
  async function getAttackDataList() {
    const params = {
      date: selectedDate === '' ? undefined : selectedDate,
      module_name: selectedModule === '' ? undefined : selectedModule,
      event_type: selectedEvent === '' ? undefined : selectedEvent,
      skip: (page - 1) * rowsPerPage,
      limit: rowsPerPage,
    }

    try {
      setLoading(true)
      const response = await axios.get(`${api}/api/events/explore`, {
        params,
      })
      setDataList(response.data.data)
      setTotal(response.data.total)
    } catch (error: any) {
      setAlertOpen(true)
      setAlertMessage(error?.message)
    } finally {
      setLoading(false)
    }
  }
  // 下载文件（Excel、CSV）
  async function downloadAttackDataList(file_type: string) {
    const params = {
      date: selectedDate === '' ? undefined : selectedDate,
      module_name: selectedModule === '' ? undefined : selectedModule,
      event_type: selectedEvent === '' ? undefined : selectedEvent,
      skip: 0,
      limit: 1000,
      file_type,
    }

    try {
      const response = await axios.get(`${api}/api/events/export`, {
        params,
        responseType: 'blob',
      })
      let suffix = file_type === 'EXCEL' ? 'xlsx' : 'csv'
      // 创建一个新的 URL 对象表示指定的 File 对象或 Blob 对象
      const url = window.URL.createObjectURL(
        new Blob([response.data], { type: `application/${file_type}` })
      )
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `exported_data.${suffix}`)
      document.body.appendChild(link)
      link.click()

      // 清理并释放 URL 对象
      link.parentNode?.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error: any) {
      setAlertOpen(true)
      setAlertMessage(error?.message)
    }
  }

  // Function
  // 分页
  const handleChangePage = (event: unknown, newPage: number) => {
    console.log('newPage', newPage)
    setPage(newPage)
  }

  // 关闭提示框
  const handleClose = (event?: React.SyntheticEvent, reason?: string) => {
    if (reason === 'clickaway') {
      return
    }
    setAlertOpen(false)
  }

  // React.Hooks
  // 获取表格数据
  useEffect(() => {
    getAttackDataList()
  }, [page, searchParams])

  // 获取全部模块名称
  useEffect(() => {
    getModules()
  }, [])

  return (
    <>
      <div className="attack-list-container">
        {/* 顶部： 搜索 & 按钮 */}
        <div className="attack-list-search-content">
          <div className="attack-list-search-list">
            <FormControl className="attack-list-select">
              <TextField
                label="事件类型"
                select
                InputLabelProps={{
                  shrink: true, // 强制标签始终在上方
                }}
                value={selectedEvent}
                onChange={(e: React.ChangeEvent<{ value: unknown }>) => {
                  setSelectedEvent(e.target.value as string)
                }}>
                <MenuItem value="honeypot">honeypot</MenuItem>
                <MenuItem value="network">network</MenuItem>
                <MenuItem value="credential">credential</MenuItem>
                <MenuItem value="file">file</MenuItem>
                <MenuItem value="data">data</MenuItem>
                <MenuItem value="pcap">pcap</MenuItem>
              </TextField>
            </FormControl>
            <FormControl className="attack-list-select">
              <TextField
                value={selectedModule}
                label="模块名称"
                InputLabelProps={{
                  shrink: true, // 强制标签始终在上方
                }}
                select
                onChange={(e: React.ChangeEvent<{ value: unknown }>) => {
                  setSelectedModule(e.target.value as string)
                }}>
                {moduleList.map((moduleName, index) => (
                  <MenuItem key={index} value={moduleName}>
                    {moduleName}
                  </MenuItem>
                ))}
              </TextField>
            </FormControl>
            <FormControl className="attack-list-select">
              <TextField
                id="date"
                label="时间"
                type="date"
                InputLabelProps={{
                  shrink: true, // 强制标签始终在上方
                }}
                value={selectedDate}
                onChange={(e) => {
                  setSelectedDate(e.target.value)
                }}
              />
            </FormControl>
            <div className="attack-list-download-btns">
              <Button
                variant="outlined"
                onClick={() => {
                  setPage(1) // 重置页码为第一页
                  setSearchParams({
                    selectedDate,
                    selectedEvent,
                    selectedModule,
                  })
                }}
                startIcon={<SearchIcon />}>
                搜索
              </Button>
              <Button
                variant="outlined"
                onClick={() => {
                  setPage(1) // 重置页码为第一页
                  setSelectedDate('')
                  setSelectedEvent('')
                  setSelectedModule('')
                  setSearchParams({
                    selectedDate: '',
                    selectedEvent: '',
                    selectedModule: '',
                  })
                }}
                startIcon={<CachedIcon />}>
                重置
              </Button>
            </div>
          </div>
          <div className="attack-list-download-btns">
            <Button
              variant="contained"
              color="primary"
              onClick={() => downloadAttackDataList('CSV')}
              startIcon={<SaveAltIcon />}>
              下载CSV
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={() => downloadAttackDataList('EXCEL')}
              startIcon={<SaveAltIcon />}>
              下载Excel
            </Button>
          </div>
        </div>

        <TableContainer className="attack-list-table">
          <Table style={{ width: '100%' }}>
            <TableHead>
              <TableRow>
                <StyledTableCell>源IP</StyledTableCell>
                <StyledTableCell>源端口</StyledTableCell>
                <StyledTableCell>目的IP</StyledTableCell>
                <StyledTableCell>目的端口</StyledTableCell>
                <StyledTableCell>模块名称</StyledTableCell>
                <StyledTableCell>机器名称</StyledTableCell>
                <StyledTableCell>源国家</StyledTableCell>
                <StyledTableCell>目的国家</StyledTableCell>
                <StyledTableCell>日期</StyledTableCell>
              </TableRow>
            </TableHead>
            <TableBody style={{ position: 'relative' }}>
              {loading && (
                <div style={{ width: '100%', height: '300px' }}>
                  <CircularProgress className="attack-list-loading" />
                </div>
              )}
              {!loading &&
                dataList.map((row, index) => (
                  <StyledTableRow key={index}>
                    <StyledTableCell>{row.ip_src}</StyledTableCell>
                    <StyledTableCell>{row.port_src}</StyledTableCell>
                    <StyledTableCell>{row.ip_dest}</StyledTableCell>
                    <StyledTableCell>{row.port_dest}</StyledTableCell>
                    <StyledTableCell>{row.module_name}</StyledTableCell>
                    <StyledTableCell>{row.machine_name}</StyledTableCell>
                    <StyledTableCell>{row.country_ip_src}</StyledTableCell>
                    <StyledTableCell>{row.country_ip_dest}</StyledTableCell>
                    <StyledTableCell>{row.date}</StyledTableCell>
                  </StyledTableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
        <div className="attack-list-pagination-div ">
          <div>共 {total} 条数据</div>
          <Pagination
            className="attack-list-pagination"
            showFirstButton
            count={Math.ceil(total / rowsPerPage)}
            page={page}
            onChange={handleChangePage}
          />
        </div>
      </div>
      <Snackbar
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        open={alertOpen}
        autoHideDuration={6000}
        onClose={handleClose}>
        <Alert onClose={handleClose} severity="error">
          {alertMessage}
        </Alert>
      </Snackbar>
    </>
  )
}
