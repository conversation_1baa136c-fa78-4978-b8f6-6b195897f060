import React, { useState, useEffect } from 'react';
import { Box, Paper } from '@material-ui/core';
import SingleAlertFilter from './SingleAlertFilter';
import SingleAlertTable from './SingleAlertTable';
import './styles.css';
import apiClient from '../apis/apiClient';
import { useNavigate } from 'react-router-dom';

// 告警状态类型
export type AlertStatus = '待处理' | '处理中' | '已处理' | '已忽略' | '误报';

// API状态映射
const statusApiMap: Record<AlertStatus, string> = {
  '待处理': 'pending',
  '处理中': 'processing',
  '已处理': 'processed',
  '已忽略': 'ignored',
  '误报': 'false_positive'
};

// 告警类型
export type AlertType = '恶意攻击' | '异常行为' | '系统异常' | '网络异常';

// API告警类型映射
const alertTypeApiMap: Record<AlertType, string> = {
  '恶意攻击': 'malicious_attack',
  '异常行为': 'abnormal_behavior',
  '系统异常': 'system_anomaly',
  '网络异常': 'network_anomaly'
};

// 严重程度类型
export type SeverityLevel = '低' | '中' | '高' | '严重';

// API严重程度映射
const severityApiMap: Record<SeverityLevel, string> = {
  '低': 'low',
  '中': 'medium',
  '高': 'high',
  '严重': 'critical'
};

// 数据源类型
export type DataSource = '蜜罐' | '日志' | '网络流量' | '系统监控';

// API数据源映射
const dataSourceApiMap: Record<DataSource, string> = {
  '蜜罐': 'honeypot',
  '日志': 'log',
  '网络流量': 'network_traffic',
  '系统监控': 'system_monitor'
};

// 告警数据类型
export interface AlertData {
  id: number;
  alertName: string;
  alertType: AlertType;
  status: AlertStatus;
  severity: SeverityLevel;
  dataSource: DataSource;
  sourceIp: string;
  targetIp: string;
  detectTime: string;
  description: string;
}

// API返回的告警数据类型
interface ApiAlertData {
  id: number;
  alert_name: string;
  alert_type: string;
  status: string;
  severity: string;
  data_source: string;
  source_ip: string;
  target_ip: string;
  detect_time: string;
  description: string;
  created_at: string;
  updated_at: string;
}

// API返回的分页数据
interface ApiPagination {
  total: number;
  pages: number;
  current_page: number;
  per_page: number;
  has_next: boolean;
  has_prev: boolean;
}

// API响应结构
interface ApiResponse {
  code: number;
  message: string;
  alerts: ApiAlertData[];
  pagination: ApiPagination;
}

// 筛选条件类型
export interface FilterConditions {
  status: AlertStatus[];
  alertTypes: AlertType[];
  severityLevels: SeverityLevel[];
  dataSources: DataSource[];
  sourceIp?: string;
  targetIp?: string;
  startTime?: string;
  endTime?: string;
  sortField: string;
  sortDirection: 'asc' | 'desc';
}

// 将API返回的告警数据转换为前端使用的格式
const convertApiAlertToAlertData = (apiAlert: ApiAlertData): AlertData => {
  // 转换告警类型
  const alertType: AlertType = 
    apiAlert.alert_type === 'malicious_attack' ? '恶意攻击' :
    apiAlert.alert_type === 'abnormal_behavior' ? '异常行为' :
    apiAlert.alert_type === 'system_anomaly' ? '系统异常' : '网络异常';
  
  // 转换状态
  const status: AlertStatus = 
    apiAlert.status === 'pending' ? '待处理' :
    apiAlert.status === 'processing' ? '处理中' :
    apiAlert.status === 'processed' ? '已处理' :
    apiAlert.status === 'ignored' ? '已忽略' : '误报';
  
  // 转换严重程度
  const severity: SeverityLevel = 
    apiAlert.severity === 'low' ? '低' :
    apiAlert.severity === 'medium' ? '中' :
    apiAlert.severity === 'high' ? '高' : '严重';
  
  // 转换数据源
  const dataSource: DataSource = 
    apiAlert.data_source === 'honeypot' ? '蜜罐' :
    apiAlert.data_source === 'log' ? '日志' :
    apiAlert.data_source === 'network_traffic' ? '网络流量' : '系统监控';

  // 格式化检测时间为 "YYYY-MM-DD HH:MM" 格式
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  };

  return {
    id: apiAlert.id,
    alertName: apiAlert.alert_name,
    alertType,
    status,
    severity,
    dataSource,
    sourceIp: apiAlert.source_ip,
    targetIp: apiAlert.target_ip,
    detectTime: formatDate(apiAlert.detect_time),
    description: apiAlert.description
  };
};

const SingleAlertPage: React.FC = () => {
  // 原始数据
  const [allAlerts, setAllAlerts] = useState<AlertData[]>([]);
  
  // 筛选后的数据
  const [filteredAlerts, setFilteredAlerts] = useState<AlertData[]>([]);
  
  // 当前页码
  const [currentPage, setCurrentPage] = useState(1);

  // 总页数
  const [totalPages, setTotalPages] = useState(1);
  
  // 加载状态
  const [loading, setLoading] = useState(false);
  
  // 默认筛选条件
  const [filterConditions, setFilterConditions] = useState<FilterConditions>({
    status: [],
    alertTypes: [],
    severityLevels: [],
    dataSources: [],
    sortField: 'detect_time',
    sortDirection: 'desc'
  });

  const navigate = useNavigate();

  // 从API获取告警列表
  const fetchAlerts = async (page: number = 1, conditions = filterConditions) => {
    setLoading(true);
    try {
      // 构建查询参数
      const params: Record<string, string | number> = {
        page,
        per_page: 10 // 每页显示10条数据
      };

      // 添加状态筛选
      if (conditions.status.length > 0) {
        params.status = conditions.status
          .map(status => statusApiMap[status])
          .join(',');
      }

      // 添加告警类型筛选
      if (conditions.alertTypes.length > 0) {
        params.alert_type = conditions.alertTypes
          .map(type => alertTypeApiMap[type])
          .join(',');
      }

      // 添加严重程度筛选
      if (conditions.severityLevels.length > 0) {
        params.severity = conditions.severityLevels
          .map(level => severityApiMap[level])
          .join(',');
      }

      // 添加数据源筛选
      if (conditions.dataSources.length > 0) {
        params.data_source = conditions.dataSources
          .map(source => dataSourceApiMap[source])
          .join(',');
      }

      // 添加IP筛选
      if (conditions.sourceIp) {
        params.source_ip = conditions.sourceIp;
      }

      if (conditions.targetIp) {
        params.target_ip = conditions.targetIp;
      }

      // 添加时间范围筛选
      if (conditions.startTime) {
        params.start_time = conditions.startTime;
      }

      if (conditions.endTime) {
        params.end_time = conditions.endTime;
      }

      // 添加排序字段
      params.sort_by = conditions.sortField;

      // 添加排序方向
      params.sort_order = conditions.sortDirection;

      console.log('筛选参数:', params);

      // 发送请求 - 这里使用模拟的API端点
      const response = await apiClient.get<ApiResponse>('/api/single-alert/alerts/', { params });
      
      // 检查响应状态
      if (response.data.code === 200) {
        // 转换API返回的数据格式
        const alerts = response.data.alerts.map(convertApiAlertToAlertData);
        setAllAlerts(alerts);
        setFilteredAlerts(alerts);
        
        // 更新分页信息
        setCurrentPage(response.data.pagination.current_page);
        setTotalPages(response.data.pagination.pages);
      } else {
        console.error('API返回错误:', response.data.message);
        setAllAlerts([]);
        setFilteredAlerts([]);
      }
    } catch (error) {
      console.error('获取告警列表失败:', error);
      // 使用模拟数据作为fallback
      const mockAlerts = generateMockAlerts();
      setAllAlerts(mockAlerts);
      setFilteredAlerts(mockAlerts);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  // 生成模拟数据
  const generateMockAlerts = (): AlertData[] => {
    return [
      {
        id: 1,
        alertName: '恶意IP访问检测',
        alertType: '恶意攻击',
        status: '待处理',
        severity: '高',
        dataSource: '蜜罐',
        sourceIp: '*************',
        targetIp: '********',
        detectTime: '2024-08-01 14:30',
        description: '检测到来自恶意IP的攻击行为'
      },
      {
        id: 2,
        alertName: '异常登录尝试',
        alertType: '异常行为',
        status: '处理中',
        severity: '中',
        dataSource: '日志',
        sourceIp: '***********',
        targetIp: '********',
        detectTime: '2024-08-01 13:45',
        description: '检测到异常的登录尝试行为'
      }
    ];
  };

  // 组件挂载时获取告警列表
  useEffect(() => {
    fetchAlerts();
  }, []);

  // 处理筛选条件变化
  const handleFilterChange = (newConditions: FilterConditions) => {
    setFilterConditions(newConditions);
    fetchAlerts(1, newConditions);
  };

  // 处理页码变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchAlerts(page);
  };

  // 处理重置筛选条件
  const handleReset = () => {
    const resetConditions = {
      status: [],
      alertTypes: [],
      severityLevels: [],
      dataSources: [],
      sourceIp: '',
      targetIp: '',
      startTime: '',
      endTime: '',
      sortField: 'detect_time',
      sortDirection: 'desc' as 'desc'
    };
    
    setFilterConditions(resetConditions);
    fetchAlerts(1, resetConditions);
  };

  // 处理告警操作（详情、处理、忽略）
  const handleAlertAction = (id: number, action: 'detail' | 'process' | 'ignore') => {
    console.log(`执行告警 ${id} 的 ${action} 操作`);
    if (action === 'detail') {
      navigate(`/singleAlert/alertDetail/${id}`);
    }
  };

  return (
    <Box className="single-alert-wrapper">
      <Box className="single-alert-header">
        <h2>单个告警降噪</h2>
      </Box>
      <SingleAlertFilter 
        conditions={filterConditions} 
        onFilterChange={handleFilterChange} 
        onReset={handleReset}
      />
      
      <Box mt={3}>
        <SingleAlertTable 
          alerts={filteredAlerts} 
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          onAlertAction={handleAlertAction}
          loading={loading}
        />
      </Box>
    </Box>
  );
};

export default SingleAlertPage;
