lockfileVersion: '6.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

dependencies:
  '@emotion/react':
    specifier: ^11.14.0
    version: 11.14.0(@types/react@18.3.23)(react@18.3.1)
  '@emotion/styled':
    specifier: ^11.14.0
    version: 11.14.0(@emotion/react@11.14.0)(@types/react@18.3.23)(react@18.3.1)
  '@material-ui/core':
    specifier: ^4.12.4
    version: 4.12.4(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1)
  '@material-ui/icons':
    specifier: ^4.11.3
    version: 4.11.3(@material-ui/core@4.12.4)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1)
  '@material-ui/lab':
    specifier: 4.0.0-alpha.61
    version: 4.0.0-alpha.61(@material-ui/core@4.12.4)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1)
  '@material-ui/pickers':
    specifier: ^3.3.11
    version: 3.3.11(@date-io/core@1.3.13)(@material-ui/core@4.12.4)(prop-types@15.8.1)(react-dom@18.3.1)(react@18.3.1)
  '@material-ui/styles':
    specifier: '4'
    version: 4.11.5(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1)
  '@mui/icons-material':
    specifier: ^7.1.0
    version: 7.1.2(@mui/material@7.1.2)(@types/react@18.3.23)(react@18.3.1)
  '@mui/material':
    specifier: ^7.1.0
    version: 7.1.2(@emotion/react@11.14.0)(@emotion/styled@11.14.0)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1)
  '@types/echarts':
    specifier: ^5.0.0
    version: 5.0.0
  '@types/react-router-dom':
    specifier: ^5.3.3
    version: 5.3.3
  '@types/vis':
    specifier: ^4.21.27
    version: 4.21.27
  Alert:
    specifier: link:@material-ui/lab/Alert
    version: link:@material-ui/lab/Alert
  axios:
    specifier: ^1.9.0
    version: 1.10.0
  dayjs:
    specifier: ^1.11.13
    version: 1.11.13
  echarts:
    specifier: ^5.6.0
    version: 5.6.0
  echarts-for-react:
    specifier: ^3.0.2
    version: 3.0.2(echarts@5.6.0)(react@18.3.1)
  less:
    specifier: ^4.3.0
    version: 4.3.0
  material-ui-dropzone:
    specifier: ^3.5.0
    version: 3.5.0(@material-ui/core@4.12.4)(@material-ui/icons@4.11.3)(prop-types@15.8.1)(react-dom@18.3.1)(react@18.3.1)
  react:
    specifier: ^18.2.0
    version: 18.3.1
  react-dom:
    specifier: ^18.2.0
    version: 18.3.1(react@18.3.1)
  react-router-dom:
    specifier: ^7.4.1
    version: 7.6.2(react-dom@18.3.1)(react@18.3.1)
  vis-data:
    specifier: ^7.1.9
    version: 7.1.9(uuid@9.0.1)(vis-util@5.0.7)
  vis-network:
    specifier: ^9.1.9
    version: 9.1.12(@egjs/hammerjs@2.0.17)(component-emitter@2.0.0)(keycharm@0.4.0)(uuid@9.0.1)(vis-data@7.1.9)(vis-util@5.0.7)

devDependencies:
  '@types/react':
    specifier: ^18.0.24
    version: 18.3.23
  '@types/react-dom':
    specifier: ^18.0.8
    version: 18.3.7(@types/react@18.3.23)
  '@vitejs/plugin-react':
    specifier: ^2.2.0
    version: 2.2.0(vite@3.2.11)
  less-loader:
    specifier: ^12.3.0
    version: 12.3.0(less@4.3.0)
  typescript:
    specifier: ^4.6.4
    version: 4.9.5
  vite:
    specifier: ^3.2.3
    version: 3.2.11(less@4.3.0)

packages:

  /@ampproject/remapping@2.3.0:
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==, tarball: https://registry.npmmirror.com/@ampproject/remapping/-/remapping-2.3.0.tgz}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25
    dev: true

  /@babel/code-frame@7.27.1:
    resolution: {integrity: sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==, tarball: https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.27.1
      js-tokens: 4.0.0
      picocolors: 1.1.1

  /@babel/compat-data@7.27.5:
    resolution: {integrity: sha512-KiRAp/VoJaWkkte84TvUd9qjdbZAdiqyvMxrGl1N6vzFogKmaLgoM3L1kgtLicp2HP5fBJS8JrZKLVIZGVJAVg==, tarball: https://registry.npmmirror.com/@babel/compat-data/-/compat-data-7.27.5.tgz}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/core@7.27.4:
    resolution: {integrity: sha512-bXYxrXFubeYdvB0NhD/NBB3Qi6aZeV20GOWVI47t2dkecCEoneR4NPVcb7abpXDEvejgrUfFtG6vG/zxAKmg+g==, tarball: https://registry.npmmirror.com/@babel/core/-/core-7.27.4.tgz}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.27.5
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.27.4)
      '@babel/helpers': 7.27.6
      '@babel/parser': 7.27.5
      '@babel/template': 7.27.2
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
      convert-source-map: 2.0.0
      debug: 4.4.1
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/generator@7.27.5:
    resolution: {integrity: sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==, tarball: https://registry.npmmirror.com/@babel/generator/-/generator-7.27.5.tgz}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/parser': 7.27.5
      '@babel/types': 7.27.6
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 3.1.0

  /@babel/helper-annotate-as-pure@7.27.3:
    resolution: {integrity: sha512-fXSwMQqitTGeHLBC08Eq5yXz2m37E4pJX1qAU1+2cNedz/ifv/bVXft90VeSav5nFO61EcNgwr0aJxbyPaWBPg==, tarball: https://registry.npmmirror.com/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.27.3.tgz}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.27.6
    dev: true

  /@babel/helper-compilation-targets@7.27.2:
    resolution: {integrity: sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==, tarball: https://registry.npmmirror.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/compat-data': 7.27.5
      '@babel/helper-validator-option': 7.27.1
      browserslist: 4.25.1
      lru-cache: 5.1.1
      semver: 6.3.1
    dev: true

  /@babel/helper-module-imports@7.27.1:
    resolution: {integrity: sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==, tarball: https://registry.npmmirror.com/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
    transitivePeerDependencies:
      - supports-color

  /@babel/helper-module-transforms@7.27.3(@babel/core@7.27.4):
    resolution: {integrity: sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==, tarball: https://registry.npmmirror.com/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.27.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-plugin-utils@7.27.1:
    resolution: {integrity: sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==, tarball: https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-string-parser@7.27.1:
    resolution: {integrity: sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==, tarball: https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  /@babel/helper-validator-identifier@7.27.1:
    resolution: {integrity: sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==, tarball: https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  /@babel/helper-validator-option@7.27.1:
    resolution: {integrity: sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==, tarball: https://registry.npmmirror.com/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helpers@7.27.6:
    resolution: {integrity: sha512-muE8Tt8M22638HU31A3CgfSUciwz1fhATfoVai05aPXGor//CdWDCbnlY1yvBPo07njuVOCNGCSp/GTt12lIug==, tarball: https://registry.npmmirror.com/@babel/helpers/-/helpers-7.27.6.tgz}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.27.2
      '@babel/types': 7.27.6
    dev: true

  /@babel/parser@7.27.5:
    resolution: {integrity: sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==, tarball: https://registry.npmmirror.com/@babel/parser/-/parser-7.27.5.tgz}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': 7.27.6

  /@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.4):
    resolution: {integrity: sha512-y8YTNIeKoyhGd9O0Jiyzyyqk8gdjnumGTQPsz0xOZOQ2RmkVJeZ1vmmfIvFEKqucBG6axJGBZDE/7iI5suUI/w==, tarball: https://registry.npmmirror.com/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-react-jsx-development@7.27.1(@babel/core@7.27.4):
    resolution: {integrity: sha512-ykDdF5yI4f1WrAolLqeF3hmYU12j9ntLQl/AOG1HAS21jxyg1Q0/J/tpREuYLfatGdGmXp/3yS0ZA76kOlVq9Q==, tarball: https://registry.npmmirror.com/@babel/plugin-transform-react-jsx-development/-/plugin-transform-react-jsx-development-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.4
      '@babel/plugin-transform-react-jsx': 7.27.1(@babel/core@7.27.4)
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.27.4):
    resolution: {integrity: sha512-6UzkCs+ejGdZ5mFFC/OCUrv028ab2fp1znZmCZjAOBKiBK2jXD1O+BPSfX8X2qjJ75fZBMSnQn3Rq2mrBJK2mw==, tarball: https://registry.npmmirror.com/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.27.4):
    resolution: {integrity: sha512-zbwoTsBruTeKB9hSq73ha66iFeJHuaFkUbwvqElnygoNbj/jHRsSeokowZFN3CZ64IvEqcmmkVe89OPXc7ldAw==, tarball: https://registry.npmmirror.com/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1
    dev: true

  /@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.27.4):
    resolution: {integrity: sha512-2KH4LWGSrJIkVf5tSiBFYuXDAoWRq2MMwgivCf+93dd0GQi8RXLjKA/0EvRnVV5G0hrHczsquXuD01L8s6dmBw==, tarball: https://registry.npmmirror.com/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-syntax-jsx': 7.27.1(@babel/core@7.27.4)
      '@babel/types': 7.27.6
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/runtime@7.27.6:
    resolution: {integrity: sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==, tarball: https://registry.npmmirror.com/@babel/runtime/-/runtime-7.27.6.tgz}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/template@7.27.2:
    resolution: {integrity: sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==, tarball: https://registry.npmmirror.com/@babel/template/-/template-7.27.2.tgz}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/parser': 7.27.5
      '@babel/types': 7.27.6

  /@babel/traverse@7.27.4:
    resolution: {integrity: sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==, tarball: https://registry.npmmirror.com/@babel/traverse/-/traverse-7.27.4.tgz}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.27.5
      '@babel/parser': 7.27.5
      '@babel/template': 7.27.2
      '@babel/types': 7.27.6
      debug: 4.4.1
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  /@babel/types@7.27.6:
    resolution: {integrity: sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==, tarball: https://registry.npmmirror.com/@babel/types/-/types-7.27.6.tgz}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1

  /@date-io/core@1.3.13:
    resolution: {integrity: sha512-AlEKV7TxjeK+jxWVKcCFrfYAk8spX9aCyiToFIiLPtfQbsjmRGLIhb5VZgptQcJdHtLXo7+m0DuurwFgUToQuA==, tarball: https://registry.npmmirror.com/@date-io/core/-/core-1.3.13.tgz}
    dev: false

  /@egjs/hammerjs@2.0.17:
    resolution: {integrity: sha512-XQsZgjm2EcVUiZQf11UBJQfmZeEmOW8DpI1gsFeln6w0ae0ii4dMQEQ0kjl6DspdWX1aGY1/loyXnP0JS06e/A==, tarball: https://registry.npmmirror.com/@egjs/hammerjs/-/hammerjs-2.0.17.tgz}
    engines: {node: '>=0.8.0'}
    dependencies:
      '@types/hammerjs': 2.0.46
    dev: false

  /@emotion/babel-plugin@11.13.5:
    resolution: {integrity: sha512-pxHCpT2ex+0q+HH91/zsdHkw/lXd468DIN2zvfvLtPKLLMo6gQj7oLObq8PhkrxOZb/gGCq03S3Z7PDhS8pduQ==, tarball: https://registry.npmmirror.com/@emotion/babel-plugin/-/babel-plugin-11.13.5.tgz}
    dependencies:
      '@babel/helper-module-imports': 7.27.1
      '@babel/runtime': 7.27.6
      '@emotion/hash': 0.9.2
      '@emotion/memoize': 0.9.0
      '@emotion/serialize': 1.3.3
      babel-plugin-macros: 3.1.0
      convert-source-map: 1.9.0
      escape-string-regexp: 4.0.0
      find-root: 1.1.0
      source-map: 0.5.7
      stylis: 4.2.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@emotion/cache@11.14.0:
    resolution: {integrity: sha512-L/B1lc/TViYk4DcpGxtAVbx0ZyiKM5ktoIyafGkH6zg/tj+mA+NE//aPYKG0k8kCHSHVJrpLpcAlOBEXQ3SavA==, tarball: https://registry.npmmirror.com/@emotion/cache/-/cache-11.14.0.tgz}
    dependencies:
      '@emotion/memoize': 0.9.0
      '@emotion/sheet': 1.4.0
      '@emotion/utils': 1.4.2
      '@emotion/weak-memoize': 0.4.0
      stylis: 4.2.0
    dev: false

  /@emotion/hash@0.8.0:
    resolution: {integrity: sha512-kBJtf7PH6aWwZ6fka3zQ0p6SBYzx4fl1LoZXE2RrnYST9Xljm7WfKJrU4g/Xr3Beg72MLrp1AWNUmuYJTL7Cow==, tarball: https://registry.npmmirror.com/@emotion/hash/-/hash-0.8.0.tgz}
    dev: false

  /@emotion/hash@0.9.2:
    resolution: {integrity: sha512-MyqliTZGuOm3+5ZRSaaBGP3USLw6+EGykkwZns2EPC5g8jJ4z9OrdZY9apkl3+UP9+sdz76YYkwCKP5gh8iY3g==, tarball: https://registry.npmmirror.com/@emotion/hash/-/hash-0.9.2.tgz}
    dev: false

  /@emotion/is-prop-valid@1.3.1:
    resolution: {integrity: sha512-/ACwoqx7XQi9knQs/G0qKvv5teDMhD7bXYns9N/wM8ah8iNb8jZ2uNO0YOgiq2o2poIvVtJS2YALasQuMSQ7Kw==, tarball: https://registry.npmmirror.com/@emotion/is-prop-valid/-/is-prop-valid-1.3.1.tgz}
    dependencies:
      '@emotion/memoize': 0.9.0
    dev: false

  /@emotion/memoize@0.9.0:
    resolution: {integrity: sha512-30FAj7/EoJ5mwVPOWhAyCX+FPfMDrVecJAM+Iw9NRoSl4BBAQeqj4cApHHUXOVvIPgLVDsCFoz/hGD+5QQD1GQ==, tarball: https://registry.npmmirror.com/@emotion/memoize/-/memoize-0.9.0.tgz}
    dev: false

  /@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1):
    resolution: {integrity: sha512-O000MLDBDdk/EohJPFUqvnp4qnHeYkVP5B0xEG0D/L7cOKP9kefu2DXn8dj74cQfsEzUqh+sr1RzFqiL1o+PpA==, tarball: https://registry.npmmirror.com/@emotion/react/-/react-11.14.0.tgz}
    peerDependencies:
      '@types/react': '*'
      react: '>=16.8.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.27.6
      '@emotion/babel-plugin': 11.13.5
      '@emotion/cache': 11.14.0
      '@emotion/serialize': 1.3.3
      '@emotion/use-insertion-effect-with-fallbacks': 1.2.0(react@18.3.1)
      '@emotion/utils': 1.4.2
      '@emotion/weak-memoize': 0.4.0
      '@types/react': 18.3.23
      hoist-non-react-statics: 3.3.2
      react: 18.3.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@emotion/serialize@1.3.3:
    resolution: {integrity: sha512-EISGqt7sSNWHGI76hC7x1CksiXPahbxEOrC5RjmFRJTqLyEK9/9hZvBbiYn70dw4wuwMKiEMCUlR6ZXTSWQqxA==, tarball: https://registry.npmmirror.com/@emotion/serialize/-/serialize-1.3.3.tgz}
    dependencies:
      '@emotion/hash': 0.9.2
      '@emotion/memoize': 0.9.0
      '@emotion/unitless': 0.10.0
      '@emotion/utils': 1.4.2
      csstype: 3.1.3
    dev: false

  /@emotion/sheet@1.4.0:
    resolution: {integrity: sha512-fTBW9/8r2w3dXWYM4HCB1Rdp8NLibOw2+XELH5m5+AkWiL/KqYX6dc0kKYlaYyKjrQ6ds33MCdMPEwgs2z1rqg==, tarball: https://registry.npmmirror.com/@emotion/sheet/-/sheet-1.4.0.tgz}
    dev: false

  /@emotion/styled@11.14.0(@emotion/react@11.14.0)(@types/react@18.3.23)(react@18.3.1):
    resolution: {integrity: sha512-XxfOnXFffatap2IyCeJyNov3kiDQWoR08gPUQxvbL7fxKryGBKUZUkG6Hz48DZwVrJSVh9sJboyV1Ds4OW6SgA==, tarball: https://registry.npmmirror.com/@emotion/styled/-/styled-11.14.0.tgz}
    peerDependencies:
      '@emotion/react': ^11.0.0-rc.0
      '@types/react': '*'
      react: '>=16.8.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.27.6
      '@emotion/babel-plugin': 11.13.5
      '@emotion/is-prop-valid': 1.3.1
      '@emotion/react': 11.14.0(@types/react@18.3.23)(react@18.3.1)
      '@emotion/serialize': 1.3.3
      '@emotion/use-insertion-effect-with-fallbacks': 1.2.0(react@18.3.1)
      '@emotion/utils': 1.4.2
      '@types/react': 18.3.23
      react: 18.3.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@emotion/unitless@0.10.0:
    resolution: {integrity: sha512-dFoMUuQA20zvtVTuxZww6OHoJYgrzfKM1t52mVySDJnMSEa08ruEvdYQbhvyu6soU+NeLVd3yKfTfT0NeV6qGg==, tarball: https://registry.npmmirror.com/@emotion/unitless/-/unitless-0.10.0.tgz}
    dev: false

  /@emotion/use-insertion-effect-with-fallbacks@1.2.0(react@18.3.1):
    resolution: {integrity: sha512-yJMtVdH59sxi/aVJBpk9FQq+OR8ll5GT8oWd57UpeaKEVGab41JWaCFA7FRLoMLloOZF/c/wsPoe+bfGmRKgDg==, tarball: https://registry.npmmirror.com/@emotion/use-insertion-effect-with-fallbacks/-/use-insertion-effect-with-fallbacks-1.2.0.tgz}
    peerDependencies:
      react: '>=16.8.0'
    dependencies:
      react: 18.3.1
    dev: false

  /@emotion/utils@1.4.2:
    resolution: {integrity: sha512-3vLclRofFziIa3J2wDh9jjbkUz9qk5Vi3IZ/FSTKViB0k+ef0fPV7dYrUIugbgupYDx7v9ud/SjrtEP8Y4xLoA==, tarball: https://registry.npmmirror.com/@emotion/utils/-/utils-1.4.2.tgz}
    dev: false

  /@emotion/weak-memoize@0.4.0:
    resolution: {integrity: sha512-snKqtPW01tN0ui7yu9rGv69aJXr/a/Ywvl11sUjNtEcRc+ng/mQriFL0wLXMef74iHa/EkftbDzU9F8iFbH+zg==, tarball: https://registry.npmmirror.com/@emotion/weak-memoize/-/weak-memoize-0.4.0.tgz}
    dev: false

  /@esbuild/android-arm@0.15.18:
    resolution: {integrity: sha512-5GT+kcs2WVGjVs7+boataCkO5Fg0y4kCjzkB5bAip7H4jfnOS3dA6KPiww9W1OEKTKeAcUVhdZGvgI65OXmUnw==, tarball: https://registry.npmmirror.com/@esbuild/android-arm/-/android-arm-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-loong64@0.15.18:
    resolution: {integrity: sha512-L4jVKS82XVhw2nvzLg/19ClLWg0y27ulRwuP7lcyL6AbUWB5aPglXY3M21mauDQMDfRLs8cQmeT03r/+X3cZYQ==, tarball: https://registry.npmmirror.com/@esbuild/linux-loong64/-/linux-loong64-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@jridgewell/gen-mapping@0.3.8:
    resolution: {integrity: sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==, tarball: https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  /@jridgewell/resolve-uri@3.1.2:
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==, tarball: https://registry.npmmirror.com/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz}
    engines: {node: '>=6.0.0'}

  /@jridgewell/set-array@1.2.1:
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==, tarball: https://registry.npmmirror.com/@jridgewell/set-array/-/set-array-1.2.1.tgz}
    engines: {node: '>=6.0.0'}

  /@jridgewell/sourcemap-codec@1.5.0:
    resolution: {integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==, tarball: https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz}

  /@jridgewell/trace-mapping@0.3.25:
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==, tarball: https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz}
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  /@material-ui/core@4.12.4(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    resolution: {integrity: sha512-tr7xekNlM9LjA6pagJmL8QCgZXaubWUwkJnoYcMKd4gw/t4XiyvnTkjdGrUVicyB2BsdaAv1tvow45bPM4sSwQ==, tarball: https://registry.npmmirror.com/@material-ui/core/-/core-4.12.4.tgz}
    engines: {node: '>=8.0.0'}
    deprecated: Material UI v4 doesn't receive active development since September 2021. See the guide https://mui.com/material-ui/migration/migration-v4/ to upgrade to v5.
    peerDependencies:
      '@types/react': ^16.8.6 || ^17.0.0
      react: ^16.8.0 || ^17.0.0
      react-dom: ^16.8.0 || ^17.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.27.6
      '@material-ui/styles': 4.11.5(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1)
      '@material-ui/system': 4.12.2(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1)
      '@material-ui/types': 5.1.0(@types/react@18.3.23)
      '@material-ui/utils': 4.11.3(react-dom@18.3.1)(react@18.3.1)
      '@types/react': 18.3.23
      '@types/react-transition-group': 4.4.12(@types/react@18.3.23)
      clsx: 1.2.1
      hoist-non-react-statics: 3.3.2
      popper.js: 1.16.1-lts
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-is: 17.0.2
      react-transition-group: 4.4.5(react-dom@18.3.1)(react@18.3.1)
    dev: false

  /@material-ui/icons@4.11.3(@material-ui/core@4.12.4)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    resolution: {integrity: sha512-IKHlyx6LDh8n19vzwH5RtHIOHl9Tu90aAAxcbWME6kp4dmvODM3UvOHJeMIDzUbd4muuJKHmlNoBN+mDY4XkBA==, tarball: https://registry.npmmirror.com/@material-ui/icons/-/icons-4.11.3.tgz}
    engines: {node: '>=8.0.0'}
    peerDependencies:
      '@material-ui/core': ^4.0.0
      '@types/react': ^16.8.6 || ^17.0.0
      react: ^16.8.0 || ^17.0.0
      react-dom: ^16.8.0 || ^17.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.27.6
      '@material-ui/core': 4.12.4(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1)
      '@types/react': 18.3.23
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    dev: false

  /@material-ui/lab@4.0.0-alpha.61(@material-ui/core@4.12.4)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    resolution: {integrity: sha512-rSzm+XKiNUjKegj8bzt5+pygZeckNLOr+IjykH8sYdVk7dE9y2ZuUSofiMV2bJk3qU+JHwexmw+q0RyNZB9ugg==, tarball: https://registry.npmmirror.com/@material-ui/lab/-/lab-4.0.0-alpha.61.tgz}
    engines: {node: '>=8.0.0'}
    deprecated: Material UI v4 doesn't receive active development since September 2021. See the guide https://mui.com/material-ui/migration/migration-v4/ to upgrade to v5.
    peerDependencies:
      '@material-ui/core': ^4.12.1
      '@types/react': ^16.8.6 || ^17.0.0
      react: ^16.8.0 || ^17.0.0
      react-dom: ^16.8.0 || ^17.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.27.6
      '@material-ui/core': 4.12.4(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1)
      '@material-ui/utils': 4.11.3(react-dom@18.3.1)(react@18.3.1)
      '@types/react': 18.3.23
      clsx: 1.2.1
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-is: 17.0.2
    dev: false

  /@material-ui/pickers@3.3.11(@date-io/core@1.3.13)(@material-ui/core@4.12.4)(prop-types@15.8.1)(react-dom@18.3.1)(react@18.3.1):
    resolution: {integrity: sha512-pDYjbjUeabapijS2FpSwK/ruJdk7IGeAshpLbKDa3PRRKRy7Nv6sXxAvUg2F+lID/NwUKgBmCYS5bzrl7Xxqzw==, tarball: https://registry.npmmirror.com/@material-ui/pickers/-/pickers-3.3.11.tgz}
    deprecated: This package no longer supported. It has been relaced by @mui/x-date-pickers
    peerDependencies:
      '@date-io/core': ^1.3.6
      '@material-ui/core': ^4.0.0
      prop-types: ^15.6.0
      react: ^16.8.0 || ^17.0.0
      react-dom: ^16.8.0 || ^17.0.0
    dependencies:
      '@babel/runtime': 7.27.6
      '@date-io/core': 1.3.13
      '@material-ui/core': 4.12.4(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1)
      '@types/styled-jsx': 2.2.9
      clsx: 1.2.1
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-transition-group: 4.4.5(react-dom@18.3.1)(react@18.3.1)
      rifm: 0.7.0(react@18.3.1)
    dev: false

  /@material-ui/styles@4.11.5(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    resolution: {integrity: sha512-o/41ot5JJiUsIETME9wVLAJrmIWL3j0R0Bj2kCOLbSfqEkKf0fmaPt+5vtblUh5eXr2S+J/8J3DaCb10+CzPGA==, tarball: https://registry.npmmirror.com/@material-ui/styles/-/styles-4.11.5.tgz}
    engines: {node: '>=8.0.0'}
    deprecated: Material UI v4 doesn't receive active development since September 2021. See the guide https://mui.com/material-ui/migration/migration-v4/ to upgrade to v5.
    peerDependencies:
      '@types/react': ^16.8.6 || ^17.0.0
      react: ^16.8.0 || ^17.0.0
      react-dom: ^16.8.0 || ^17.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.27.6
      '@emotion/hash': 0.8.0
      '@material-ui/types': 5.1.0(@types/react@18.3.23)
      '@material-ui/utils': 4.11.3(react-dom@18.3.1)(react@18.3.1)
      '@types/react': 18.3.23
      clsx: 1.2.1
      csstype: 2.6.21
      hoist-non-react-statics: 3.3.2
      jss: 10.10.0
      jss-plugin-camel-case: 10.10.0
      jss-plugin-default-unit: 10.10.0
      jss-plugin-global: 10.10.0
      jss-plugin-nested: 10.10.0
      jss-plugin-props-sort: 10.10.0
      jss-plugin-rule-value-function: 10.10.0
      jss-plugin-vendor-prefixer: 10.10.0
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    dev: false

  /@material-ui/system@4.12.2(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    resolution: {integrity: sha512-6CSKu2MtmiJgcCGf6nBQpM8fLkuB9F55EKfbdTC80NND5wpTmKzwdhLYLH3zL4cLlK0gVaaltW7/wMuyTnN0Lw==, tarball: https://registry.npmmirror.com/@material-ui/system/-/system-4.12.2.tgz}
    engines: {node: '>=8.0.0'}
    peerDependencies:
      '@types/react': ^16.8.6 || ^17.0.0
      react: ^16.8.0 || ^17.0.0
      react-dom: ^16.8.0 || ^17.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.27.6
      '@material-ui/utils': 4.11.3(react-dom@18.3.1)(react@18.3.1)
      '@types/react': 18.3.23
      csstype: 2.6.21
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    dev: false

  /@material-ui/types@5.1.0(@types/react@18.3.23):
    resolution: {integrity: sha512-7cqRjrY50b8QzRSYyhSpx4WRw2YuO0KKIGQEVk5J8uoz2BanawykgZGoWEqKm7pVIbzFDN0SpPcVV4IhOFkl8A==, tarball: https://registry.npmmirror.com/@material-ui/types/-/types-5.1.0.tgz}
    peerDependencies:
      '@types/react': '*'
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.3.23
    dev: false

  /@material-ui/utils@4.11.3(react-dom@18.3.1)(react@18.3.1):
    resolution: {integrity: sha512-ZuQPV4rBK/V1j2dIkSSEcH5uT6AaHuKWFfotADHsC0wVL1NLd2WkFCm4ZZbX33iO4ydl6V0GPngKm8HZQ2oujg==, tarball: https://registry.npmmirror.com/@material-ui/utils/-/utils-4.11.3.tgz}
    engines: {node: '>=8.0.0'}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0
      react-dom: ^16.8.0 || ^17.0.0
    dependencies:
      '@babel/runtime': 7.27.6
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-is: 17.0.2
    dev: false

  /@mui/core-downloads-tracker@7.1.2:
    resolution: {integrity: sha512-0gLO1PvbJwSYe5ji021tGj6HFqrtEPMGKK4L1zWwRbhzrWWUumUJvMvJUsIgWQIYQsgOnhq9k2Fc1BxLGHDsAg==, tarball: https://registry.npmmirror.com/@mui/core-downloads-tracker/-/core-downloads-tracker-7.1.2.tgz}
    dev: false

  /@mui/icons-material@7.1.2(@mui/material@7.1.2)(@types/react@18.3.23)(react@18.3.1):
    resolution: {integrity: sha512-slqJByDub7Y1UcokrM17BoMBMvn8n7daXFXVoTv0MEH5k3sHjmsH8ql/Mt3s9vQ20cORDr83UZ448TEGcbrXtw==, tarball: https://registry.npmmirror.com/@mui/icons-material/-/icons-material-7.1.2.tgz}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      '@mui/material': ^7.1.2
      '@types/react': ^17.0.0 || ^18.0.0 || ^19.0.0
      react: ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.27.6
      '@mui/material': 7.1.2(@emotion/react@11.14.0)(@emotion/styled@11.14.0)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1)
      '@types/react': 18.3.23
      react: 18.3.1
    dev: false

  /@mui/material@7.1.2(@emotion/react@11.14.0)(@emotion/styled@11.14.0)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    resolution: {integrity: sha512-Z5PYKkA6Kd8vS04zKxJNpwuvt6IoMwqpbidV7RCrRQQKwczIwcNcS8L6GnN4pzFYfEs+N9v6co27DmG07rcnoA==, tarball: https://registry.npmmirror.com/@mui/material/-/material-7.1.2.tgz}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      '@emotion/react': ^11.5.0
      '@emotion/styled': ^11.3.0
      '@mui/material-pigment-css': ^7.1.1
      '@types/react': ^17.0.0 || ^18.0.0 || ^19.0.0
      react: ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@emotion/react':
        optional: true
      '@emotion/styled':
        optional: true
      '@mui/material-pigment-css':
        optional: true
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.27.6
      '@emotion/react': 11.14.0(@types/react@18.3.23)(react@18.3.1)
      '@emotion/styled': 11.14.0(@emotion/react@11.14.0)(@types/react@18.3.23)(react@18.3.1)
      '@mui/core-downloads-tracker': 7.1.2
      '@mui/system': 7.1.1(@emotion/react@11.14.0)(@emotion/styled@11.14.0)(@types/react@18.3.23)(react@18.3.1)
      '@mui/types': 7.4.3(@types/react@18.3.23)
      '@mui/utils': 7.1.1(@types/react@18.3.23)(react@18.3.1)
      '@popperjs/core': 2.11.8
      '@types/react': 18.3.23
      '@types/react-transition-group': 4.4.12(@types/react@18.3.23)
      clsx: 2.1.1
      csstype: 3.1.3
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-is: 19.1.0
      react-transition-group: 4.4.5(react-dom@18.3.1)(react@18.3.1)
    dev: false

  /@mui/private-theming@7.1.1(@types/react@18.3.23)(react@18.3.1):
    resolution: {integrity: sha512-M8NbLUx+armk2ZuaxBkkMk11ultnWmrPlN0Xe3jUEaBChg/mcxa5HWIWS1EE4DF36WRACaAHVAvyekWlDQf0PQ==, tarball: https://registry.npmmirror.com/@mui/private-theming/-/private-theming-7.1.1.tgz}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      '@types/react': ^17.0.0 || ^18.0.0 || ^19.0.0
      react: ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.27.6
      '@mui/utils': 7.1.1(@types/react@18.3.23)(react@18.3.1)
      '@types/react': 18.3.23
      prop-types: 15.8.1
      react: 18.3.1
    dev: false

  /@mui/styled-engine@7.1.1(@emotion/react@11.14.0)(@emotion/styled@11.14.0)(react@18.3.1):
    resolution: {integrity: sha512-R2wpzmSN127j26HrCPYVQ53vvMcT5DaKLoWkrfwUYq3cYytL6TQrCH8JBH3z79B6g4nMZZVoaXrxO757AlShaw==, tarball: https://registry.npmmirror.com/@mui/styled-engine/-/styled-engine-7.1.1.tgz}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      '@emotion/react': ^11.4.1
      '@emotion/styled': ^11.3.0
      react: ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@emotion/react':
        optional: true
      '@emotion/styled':
        optional: true
    dependencies:
      '@babel/runtime': 7.27.6
      '@emotion/cache': 11.14.0
      '@emotion/react': 11.14.0(@types/react@18.3.23)(react@18.3.1)
      '@emotion/serialize': 1.3.3
      '@emotion/sheet': 1.4.0
      '@emotion/styled': 11.14.0(@emotion/react@11.14.0)(@types/react@18.3.23)(react@18.3.1)
      csstype: 3.1.3
      prop-types: 15.8.1
      react: 18.3.1
    dev: false

  /@mui/system@7.1.1(@emotion/react@11.14.0)(@emotion/styled@11.14.0)(@types/react@18.3.23)(react@18.3.1):
    resolution: {integrity: sha512-Kj1uhiqnj4Zo7PDjAOghtXJtNABunWvhcRU0O7RQJ7WOxeynoH6wXPcilphV8QTFtkKaip8EiNJRiCD+B3eROA==, tarball: https://registry.npmmirror.com/@mui/system/-/system-7.1.1.tgz}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      '@emotion/react': ^11.5.0
      '@emotion/styled': ^11.3.0
      '@types/react': ^17.0.0 || ^18.0.0 || ^19.0.0
      react: ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@emotion/react':
        optional: true
      '@emotion/styled':
        optional: true
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.27.6
      '@emotion/react': 11.14.0(@types/react@18.3.23)(react@18.3.1)
      '@emotion/styled': 11.14.0(@emotion/react@11.14.0)(@types/react@18.3.23)(react@18.3.1)
      '@mui/private-theming': 7.1.1(@types/react@18.3.23)(react@18.3.1)
      '@mui/styled-engine': 7.1.1(@emotion/react@11.14.0)(@emotion/styled@11.14.0)(react@18.3.1)
      '@mui/types': 7.4.3(@types/react@18.3.23)
      '@mui/utils': 7.1.1(@types/react@18.3.23)(react@18.3.1)
      '@types/react': 18.3.23
      clsx: 2.1.1
      csstype: 3.1.3
      prop-types: 15.8.1
      react: 18.3.1
    dev: false

  /@mui/types@7.4.3(@types/react@18.3.23):
    resolution: {integrity: sha512-2UCEiK29vtiZTeLdS2d4GndBKacVyxGvReznGXGr+CzW/YhjIX+OHUdCIczZjzcRAgKBGmE9zCIgoV9FleuyRQ==, tarball: https://registry.npmmirror.com/@mui/types/-/types-7.4.3.tgz}
    peerDependencies:
      '@types/react': ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.27.6
      '@types/react': 18.3.23
    dev: false

  /@mui/utils@7.1.1(@types/react@18.3.23)(react@18.3.1):
    resolution: {integrity: sha512-BkOt2q7MBYl7pweY2JWwfrlahhp+uGLR8S+EhiyRaofeRYUWL2YKbSGQvN4hgSN1i8poN0PaUiii1kEMrchvzg==, tarball: https://registry.npmmirror.com/@mui/utils/-/utils-7.1.1.tgz}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      '@types/react': ^17.0.0 || ^18.0.0 || ^19.0.0
      react: ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': 7.27.6
      '@mui/types': 7.4.3(@types/react@18.3.23)
      '@types/prop-types': 15.7.15
      '@types/react': 18.3.23
      clsx: 2.1.1
      prop-types: 15.8.1
      react: 18.3.1
      react-is: 19.1.0
    dev: false

  /@popperjs/core@2.11.8:
    resolution: {integrity: sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==, tarball: https://registry.npmmirror.com/@popperjs/core/-/core-2.11.8.tgz}
    dev: false

  /@types/echarts@5.0.0:
    resolution: {integrity: sha512-5uc/16BlYpzH8kU/u8aeRRgY2FV6yRY7RjPnYfUFPowl0F3kvNgfaz09PmeVdLkqdAtMft3XkCfqiJPJjG2DNQ==, tarball: https://registry.npmmirror.com/@types/echarts/-/echarts-5.0.0.tgz}
    deprecated: This is a stub types definition. echarts provides its own type definitions, so you do not need this installed.
    dependencies:
      echarts: 5.6.0
    dev: false

  /@types/hammerjs@2.0.46:
    resolution: {integrity: sha512-ynRvcq6wvqexJ9brDMS4BnBLzmr0e14d6ZJTEShTBWKymQiHwlAyGu0ZPEFI2Fh1U53F7tN9ufClWM5KvqkKOw==, tarball: https://registry.npmmirror.com/@types/hammerjs/-/hammerjs-2.0.46.tgz}
    dev: false

  /@types/history@4.7.11:
    resolution: {integrity: sha512-qjDJRrmvBMiTx+jyLxvLfJU7UznFuokDv4f3WRuriHKERccVpFU+8XMQUAbDzoiJCsmexxRExQeMwwCdamSKDA==, tarball: https://registry.npmmirror.com/@types/history/-/history-4.7.11.tgz}
    dev: false

  /@types/parse-json@4.0.2:
    resolution: {integrity: sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw==, tarball: https://registry.npmmirror.com/@types/parse-json/-/parse-json-4.0.2.tgz}
    dev: false

  /@types/prop-types@15.7.15:
    resolution: {integrity: sha512-F6bEyamV9jKGAFBEmlQnesRPGOQqS2+Uwi0Em15xenOxHaf2hv6L8YCVn3rPdPJOiJfPiCnLIRyvwVaqMY3MIw==, tarball: https://registry.npmmirror.com/@types/prop-types/-/prop-types-15.7.15.tgz}

  /@types/react-dom@18.3.7(@types/react@18.3.23):
    resolution: {integrity: sha512-MEe3UeoENYVFXzoXEWsvcpg6ZvlrFNlOQ7EOsvhI3CfAXwzPfO8Qwuxd40nepsYKqyyVQnTdEfv68q91yLcKrQ==, tarball: https://registry.npmmirror.com/@types/react-dom/-/react-dom-18.3.7.tgz}
    peerDependencies:
      '@types/react': ^18.0.0
    dependencies:
      '@types/react': 18.3.23
    dev: true

  /@types/react-router-dom@5.3.3:
    resolution: {integrity: sha512-kpqnYK4wcdm5UaWI3fLcELopqLrHgLqNsdpHauzlQktfkHL3npOSwtj1Uz9oKBAzs7lFtVkV8j83voAz2D8fhw==, tarball: https://registry.npmmirror.com/@types/react-router-dom/-/react-router-dom-5.3.3.tgz}
    dependencies:
      '@types/history': 4.7.11
      '@types/react': 18.3.23
      '@types/react-router': 5.1.20
    dev: false

  /@types/react-router@5.1.20:
    resolution: {integrity: sha512-jGjmu/ZqS7FjSH6owMcD5qpq19+1RS9DeVRqfl1FeBMxTDQAGwlMWOcs52NDoXaNKyG3d1cYQFMs9rCrb88o9Q==, tarball: https://registry.npmmirror.com/@types/react-router/-/react-router-5.1.20.tgz}
    dependencies:
      '@types/history': 4.7.11
      '@types/react': 18.3.23
    dev: false

  /@types/react-transition-group@4.4.12(@types/react@18.3.23):
    resolution: {integrity: sha512-8TV6R3h2j7a91c+1DXdJi3Syo69zzIZbz7Lg5tORM5LEJG7X/E6a1V3drRyBRZq7/utz7A+c4OgYLiLcYGHG6w==, tarball: https://registry.npmmirror.com/@types/react-transition-group/-/react-transition-group-4.4.12.tgz}
    peerDependencies:
      '@types/react': '*'
    dependencies:
      '@types/react': 18.3.23
    dev: false

  /@types/react@18.3.23:
    resolution: {integrity: sha512-/LDXMQh55EzZQ0uVAZmKKhfENivEvWz6E+EYzh+/MCjMhNsotd+ZHhBGIjFDTi6+fz0OhQQQLbTgdQIxxCsC0w==, tarball: https://registry.npmmirror.com/@types/react/-/react-18.3.23.tgz}
    dependencies:
      '@types/prop-types': 15.7.15
      csstype: 3.1.3

  /@types/styled-jsx@2.2.9:
    resolution: {integrity: sha512-W/iTlIkGEyTBGTEvZCey8EgQlQ5l0DwMqi3iOXlLs2kyBwYTXHKEiU6IZ5EwoRwngL8/dGYuzezSup89ttVHLw==, tarball: https://registry.npmmirror.com/@types/styled-jsx/-/styled-jsx-2.2.9.tgz}
    dependencies:
      '@types/react': 18.3.23
    dev: false

  /@types/vis@4.21.27:
    resolution: {integrity: sha512-Ma/W/XgSXu9ltuJfKRqS27CVcYNxf+sM8M76F82zWSyB/s/gT+Vw2f8LZhWN+GSnDQ1XahdBKhTtGZi4IK3pzA==, tarball: https://registry.npmmirror.com/@types/vis/-/vis-4.21.27.tgz}
    dependencies:
      moment: 2.30.1
    dev: false

  /@vitejs/plugin-react@2.2.0(vite@3.2.11):
    resolution: {integrity: sha512-FFpefhvExd1toVRlokZgxgy2JtnBOdp4ZDsq7ldCWaqGSGn9UhWMAVm/1lxPL14JfNS5yGz+s9yFrQY6shoStA==, tarball: https://registry.npmmirror.com/@vitejs/plugin-react/-/plugin-react-2.2.0.tgz}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      vite: ^3.0.0
    dependencies:
      '@babel/core': 7.27.4
      '@babel/plugin-transform-react-jsx': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-react-jsx-development': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-react-jsx-self': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-react-jsx-source': 7.27.1(@babel/core@7.27.4)
      magic-string: 0.26.7
      react-refresh: 0.14.2
      vite: 3.2.11(less@4.3.0)
    transitivePeerDependencies:
      - supports-color
    dev: true

  /asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==, tarball: https://registry.npmmirror.com/asynckit/-/asynckit-0.4.0.tgz}
    dev: false

  /attr-accept@2.2.5:
    resolution: {integrity: sha512-0bDNnY/u6pPwHDMoF0FieU354oBi0a8rD9FcsLwzcGWbc8KS8KPIi7y+s13OlVY+gMWc/9xEMUgNE6Qm8ZllYQ==, tarball: https://registry.npmmirror.com/attr-accept/-/attr-accept-2.2.5.tgz}
    engines: {node: '>=4'}
    dev: false

  /axios@1.10.0:
    resolution: {integrity: sha512-/1xYAC4MP/HEG+3duIhFr4ZQXR4sQXOIe+o6sdqzeykGLx6Upp/1p8MHqhINOvGeP7xyNHe7tsiJByc4SSVUxw==, tarball: https://registry.npmmirror.com/axios/-/axios-1.10.0.tgz}
    dependencies:
      follow-redirects: 1.15.9
      form-data: 4.0.3
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug
    dev: false

  /babel-plugin-macros@3.1.0:
    resolution: {integrity: sha512-Cg7TFGpIr01vOQNODXOOaGz2NpCU5gl8x1qJFbb6hbZxR7XrcE2vtbAsTAbJ7/xwJtUuJEw8K8Zr/AE0LHlesg==, tarball: https://registry.npmmirror.com/babel-plugin-macros/-/babel-plugin-macros-3.1.0.tgz}
    engines: {node: '>=10', npm: '>=6'}
    dependencies:
      '@babel/runtime': 7.27.6
      cosmiconfig: 7.1.0
      resolve: 1.22.10
    dev: false

  /browserslist@4.25.1:
    resolution: {integrity: sha512-KGj0KoOMXLpSNkkEI6Z6mShmQy0bc1I+T7K9N81k4WWMrfz+6fQ6es80B/YLAeRoKvjYE1YSHHOW1qe9xIVzHw==, tarball: https://registry.npmmirror.com/browserslist/-/browserslist-4.25.1.tgz}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001726
      electron-to-chromium: 1.5.176
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.25.1)
    dev: true

  /call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==, tarball: https://registry.npmmirror.com/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2
    dev: false

  /callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==, tarball: https://registry.npmmirror.com/callsites/-/callsites-3.1.0.tgz}
    engines: {node: '>=6'}
    dev: false

  /caniuse-lite@1.0.30001726:
    resolution: {integrity: sha512-VQAUIUzBiZ/UnlM28fSp2CRF3ivUn1BWEvxMcVTNwpw91Py1pGbPIyIKtd+tzct9C3ouceCVdGAXxZOpZAsgdw==, tarball: https://registry.npmmirror.com/caniuse-lite/-/caniuse-lite-1.0.30001726.tgz}
    dev: true

  /clsx@1.2.1:
    resolution: {integrity: sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg==, tarball: https://registry.npmmirror.com/clsx/-/clsx-1.2.1.tgz}
    engines: {node: '>=6'}
    dev: false

  /clsx@2.1.1:
    resolution: {integrity: sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==, tarball: https://registry.npmmirror.com/clsx/-/clsx-2.1.1.tgz}
    engines: {node: '>=6'}
    dev: false

  /combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==, tarball: https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz}
    engines: {node: '>= 0.8'}
    dependencies:
      delayed-stream: 1.0.0
    dev: false

  /component-emitter@2.0.0:
    resolution: {integrity: sha512-4m5s3Me2xxlVKG9PkZpQqHQR7bgpnN7joDMJ4yvVkVXngjoITG76IaZmzmywSeRTeTpc6N6r3H3+KyUurV8OYw==, tarball: https://registry.npmmirror.com/component-emitter/-/component-emitter-2.0.0.tgz}
    engines: {node: '>=18'}
    dev: false

  /convert-source-map@1.9.0:
    resolution: {integrity: sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==, tarball: https://registry.npmmirror.com/convert-source-map/-/convert-source-map-1.9.0.tgz}
    dev: false

  /convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==, tarball: https://registry.npmmirror.com/convert-source-map/-/convert-source-map-2.0.0.tgz}
    dev: true

  /cookie@1.0.2:
    resolution: {integrity: sha512-9Kr/j4O16ISv8zBBhJoi4bXOYNTkFLOqSL3UDB0njXxCXNezjeyVrJyGOWtgfs/q2km1gwBcfH8q1yEGoMYunA==, tarball: https://registry.npmmirror.com/cookie/-/cookie-1.0.2.tgz}
    engines: {node: '>=18'}
    dev: false

  /copy-anything@2.0.6:
    resolution: {integrity: sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw==, tarball: https://registry.npmmirror.com/copy-anything/-/copy-anything-2.0.6.tgz}
    dependencies:
      is-what: 3.14.1

  /cosmiconfig@7.1.0:
    resolution: {integrity: sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==, tarball: https://registry.npmmirror.com/cosmiconfig/-/cosmiconfig-7.1.0.tgz}
    engines: {node: '>=10'}
    dependencies:
      '@types/parse-json': 4.0.2
      import-fresh: 3.3.1
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2
    dev: false

  /css-vendor@2.0.8:
    resolution: {integrity: sha512-x9Aq0XTInxrkuFeHKbYC7zWY8ai7qJ04Kxd9MnvbC1uO5DagxoHQjm4JvG+vCdXOoFtCjbL2XSZfxmoYa9uQVQ==, tarball: https://registry.npmmirror.com/css-vendor/-/css-vendor-2.0.8.tgz}
    dependencies:
      '@babel/runtime': 7.27.6
      is-in-browser: 1.1.3
    dev: false

  /csstype@2.6.21:
    resolution: {integrity: sha512-Z1PhmomIfypOpoMjRQB70jfvy/wxT50qW08YXO5lMIJkrdq4yOTR+AW7FqutScmB9NkLwxo+jU+kZLbofZZq/w==, tarball: https://registry.npmmirror.com/csstype/-/csstype-2.6.21.tgz}
    dev: false

  /csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==, tarball: https://registry.npmmirror.com/csstype/-/csstype-3.1.3.tgz}

  /dayjs@1.11.13:
    resolution: {integrity: sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==, tarball: https://registry.npmmirror.com/dayjs/-/dayjs-1.11.13.tgz}
    dev: false

  /debug@4.4.1:
    resolution: {integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==, tarball: https://registry.npmmirror.com/debug/-/debug-4.4.1.tgz}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.3

  /delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==, tarball: https://registry.npmmirror.com/delayed-stream/-/delayed-stream-1.0.0.tgz}
    engines: {node: '>=0.4.0'}
    dev: false

  /dom-helpers@5.2.1:
    resolution: {integrity: sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==, tarball: https://registry.npmmirror.com/dom-helpers/-/dom-helpers-5.2.1.tgz}
    dependencies:
      '@babel/runtime': 7.27.6
      csstype: 3.1.3
    dev: false

  /dunder-proto@1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==, tarball: https://registry.npmmirror.com/dunder-proto/-/dunder-proto-1.0.1.tgz}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0
    dev: false

  /echarts-for-react@3.0.2(echarts@5.6.0)(react@18.3.1):
    resolution: {integrity: sha512-DRwIiTzx8JfwPOVgGttDytBqdp5VzCSyMRIxubgU/g2n9y3VLUmF2FK7Icmg/sNVkv4+rktmrLN9w22U2yy3fA==, tarball: https://registry.npmmirror.com/echarts-for-react/-/echarts-for-react-3.0.2.tgz}
    peerDependencies:
      echarts: ^3.0.0 || ^4.0.0 || ^5.0.0
      react: ^15.0.0 || >=16.0.0
    dependencies:
      echarts: 5.6.0
      fast-deep-equal: 3.1.3
      react: 18.3.1
      size-sensor: 1.0.2
    dev: false

  /echarts@5.6.0:
    resolution: {integrity: sha512-oTbVTsXfKuEhxftHqL5xprgLoc0k7uScAwtryCgWF6hPYFLRwOUHiFmHGCBKP5NPFNkDVopOieyUqYGH8Fa3kA==, tarball: https://registry.npmmirror.com/echarts/-/echarts-5.6.0.tgz}
    dependencies:
      tslib: 2.3.0
      zrender: 5.6.1
    dev: false

  /electron-to-chromium@1.5.176:
    resolution: {integrity: sha512-2nDK9orkm7M9ZZkjO3PjbEd3VUulQLyg5T9O3enJdFvUg46Hzd4DUvTvAuEgbdHYXyFsiG4A5sO9IzToMH1cDg==, tarball: https://registry.npmmirror.com/electron-to-chromium/-/electron-to-chromium-1.5.176.tgz}
    dev: true

  /errno@0.1.8:
    resolution: {integrity: sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==, tarball: https://registry.npmmirror.com/errno/-/errno-0.1.8.tgz}
    hasBin: true
    requiresBuild: true
    dependencies:
      prr: 1.0.1
    optional: true

  /error-ex@1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==, tarball: https://registry.npmmirror.com/error-ex/-/error-ex-1.3.2.tgz}
    dependencies:
      is-arrayish: 0.2.1
    dev: false

  /es-define-property@1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==, tarball: https://registry.npmmirror.com/es-define-property/-/es-define-property-1.0.1.tgz}
    engines: {node: '>= 0.4'}
    dev: false

  /es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==, tarball: https://registry.npmmirror.com/es-errors/-/es-errors-1.3.0.tgz}
    engines: {node: '>= 0.4'}
    dev: false

  /es-object-atoms@1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==, tarball: https://registry.npmmirror.com/es-object-atoms/-/es-object-atoms-1.1.1.tgz}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
    dev: false

  /es-set-tostringtag@2.1.0:
    resolution: {integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==, tarball: https://registry.npmmirror.com/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2
    dev: false

  /esbuild-android-64@0.15.18:
    resolution: {integrity: sha512-wnpt3OXRhcjfIDSZu9bnzT4/TNTDsOUvip0foZOUBG7QbSt//w3QV4FInVJxNhKc/ErhUxc5z4QjHtMi7/TbgA==, tarball: https://registry.npmmirror.com/esbuild-android-64/-/esbuild-android-64-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-android-arm64@0.15.18:
    resolution: {integrity: sha512-G4xu89B8FCzav9XU8EjsXacCKSG2FT7wW9J6hOc18soEHJdtWu03L3TQDGf0geNxfLTtxENKBzMSq9LlbjS8OQ==, tarball: https://registry.npmmirror.com/esbuild-android-arm64/-/esbuild-android-arm64-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-darwin-64@0.15.18:
    resolution: {integrity: sha512-2WAvs95uPnVJPuYKP0Eqx+Dl/jaYseZEUUT1sjg97TJa4oBtbAKnPnl3b5M9l51/nbx7+QAEtuummJZW0sBEmg==, tarball: https://registry.npmmirror.com/esbuild-darwin-64/-/esbuild-darwin-64-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-darwin-arm64@0.15.18:
    resolution: {integrity: sha512-tKPSxcTJ5OmNb1btVikATJ8NftlyNlc8BVNtyT/UAr62JFOhwHlnoPrhYWz09akBLHI9nElFVfWSTSRsrZiDUA==, tarball: https://registry.npmmirror.com/esbuild-darwin-arm64/-/esbuild-darwin-arm64-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-freebsd-64@0.15.18:
    resolution: {integrity: sha512-TT3uBUxkteAjR1QbsmvSsjpKjOX6UkCstr8nMr+q7zi3NuZ1oIpa8U41Y8I8dJH2fJgdC3Dj3CXO5biLQpfdZA==, tarball: https://registry.npmmirror.com/esbuild-freebsd-64/-/esbuild-freebsd-64-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-freebsd-arm64@0.15.18:
    resolution: {integrity: sha512-R/oVr+X3Tkh+S0+tL41wRMbdWtpWB8hEAMsOXDumSSa6qJR89U0S/PpLXrGF7Wk/JykfpWNokERUpCeHDl47wA==, tarball: https://registry.npmmirror.com/esbuild-freebsd-arm64/-/esbuild-freebsd-arm64-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-32@0.15.18:
    resolution: {integrity: sha512-lphF3HiCSYtaa9p1DtXndiQEeQDKPl9eN/XNoBf2amEghugNuqXNZA/ZovthNE2aa4EN43WroO0B85xVSjYkbg==, tarball: https://registry.npmmirror.com/esbuild-linux-32/-/esbuild-linux-32-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-64@0.15.18:
    resolution: {integrity: sha512-hNSeP97IviD7oxLKFuii5sDPJ+QHeiFTFLoLm7NZQligur8poNOWGIgpQ7Qf8Balb69hptMZzyOBIPtY09GZYw==, tarball: https://registry.npmmirror.com/esbuild-linux-64/-/esbuild-linux-64-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-arm64@0.15.18:
    resolution: {integrity: sha512-54qr8kg/6ilcxd+0V3h9rjT4qmjc0CccMVWrjOEM/pEcUzt8X62HfBSeZfT2ECpM7104mk4yfQXkosY8Quptug==, tarball: https://registry.npmmirror.com/esbuild-linux-arm64/-/esbuild-linux-arm64-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-arm@0.15.18:
    resolution: {integrity: sha512-UH779gstRblS4aoS2qpMl3wjg7U0j+ygu3GjIeTonCcN79ZvpPee12Qun3vcdxX+37O5LFxz39XeW2I9bybMVA==, tarball: https://registry.npmmirror.com/esbuild-linux-arm/-/esbuild-linux-arm-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-mips64le@0.15.18:
    resolution: {integrity: sha512-Mk6Ppwzzz3YbMl/ZZL2P0q1tnYqh/trYZ1VfNP47C31yT0K8t9s7Z077QrDA/guU60tGNp2GOwCQnp+DYv7bxQ==, tarball: https://registry.npmmirror.com/esbuild-linux-mips64le/-/esbuild-linux-mips64le-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-ppc64le@0.15.18:
    resolution: {integrity: sha512-b0XkN4pL9WUulPTa/VKHx2wLCgvIAbgwABGnKMY19WhKZPT+8BxhZdqz6EgkqCLld7X5qiCY2F/bfpUUlnFZ9w==, tarball: https://registry.npmmirror.com/esbuild-linux-ppc64le/-/esbuild-linux-ppc64le-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-riscv64@0.15.18:
    resolution: {integrity: sha512-ba2COaoF5wL6VLZWn04k+ACZjZ6NYniMSQStodFKH/Pu6RxzQqzsmjR1t9QC89VYJxBeyVPTaHuBMCejl3O/xg==, tarball: https://registry.npmmirror.com/esbuild-linux-riscv64/-/esbuild-linux-riscv64-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-linux-s390x@0.15.18:
    resolution: {integrity: sha512-VbpGuXEl5FCs1wDVp93O8UIzl3ZrglgnSQ+Hu79g7hZu6te6/YHgVJxCM2SqfIila0J3k0csfnf8VD2W7u2kzQ==, tarball: https://registry.npmmirror.com/esbuild-linux-s390x/-/esbuild-linux-s390x-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-netbsd-64@0.15.18:
    resolution: {integrity: sha512-98ukeCdvdX7wr1vUYQzKo4kQ0N2p27H7I11maINv73fVEXt2kyh4K4m9f35U1K43Xc2QGXlzAw0K9yoU7JUjOg==, tarball: https://registry.npmmirror.com/esbuild-netbsd-64/-/esbuild-netbsd-64-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-openbsd-64@0.15.18:
    resolution: {integrity: sha512-yK5NCcH31Uae076AyQAXeJzt/vxIo9+omZRKj1pauhk3ITuADzuOx5N2fdHrAKPxN+zH3w96uFKlY7yIn490xQ==, tarball: https://registry.npmmirror.com/esbuild-openbsd-64/-/esbuild-openbsd-64-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-sunos-64@0.15.18:
    resolution: {integrity: sha512-On22LLFlBeLNj/YF3FT+cXcyKPEI263nflYlAhz5crxtp3yRG1Ugfr7ITyxmCmjm4vbN/dGrb/B7w7U8yJR9yw==, tarball: https://registry.npmmirror.com/esbuild-sunos-64/-/esbuild-sunos-64-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-windows-32@0.15.18:
    resolution: {integrity: sha512-o+eyLu2MjVny/nt+E0uPnBxYuJHBvho8vWsC2lV61A7wwTWC3jkN2w36jtA+yv1UgYkHRihPuQsL23hsCYGcOQ==, tarball: https://registry.npmmirror.com/esbuild-windows-32/-/esbuild-windows-32-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-windows-64@0.15.18:
    resolution: {integrity: sha512-qinug1iTTaIIrCorAUjR0fcBk24fjzEedFYhhispP8Oc7SFvs+XeW3YpAKiKp8dRpizl4YYAhxMjlftAMJiaUw==, tarball: https://registry.npmmirror.com/esbuild-windows-64/-/esbuild-windows-64-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild-windows-arm64@0.15.18:
    resolution: {integrity: sha512-q9bsYzegpZcLziq0zgUi5KqGVtfhjxGbnksaBFYmWLxeV/S1fK4OLdq2DFYnXcLMjlZw2L0jLsk1eGoB522WXQ==, tarball: https://registry.npmmirror.com/esbuild-windows-arm64/-/esbuild-windows-arm64-0.15.18.tgz}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /esbuild@0.15.18:
    resolution: {integrity: sha512-x/R72SmW3sSFRm5zrrIjAhCeQSAWoni3CmHEqfQrZIQTM3lVCdehdwuIqaOtfC2slvpdlLa62GYoN8SxT23m6Q==, tarball: https://registry.npmmirror.com/esbuild/-/esbuild-0.15.18.tgz}
    engines: {node: '>=12'}
    hasBin: true
    requiresBuild: true
    optionalDependencies:
      '@esbuild/android-arm': 0.15.18
      '@esbuild/linux-loong64': 0.15.18
      esbuild-android-64: 0.15.18
      esbuild-android-arm64: 0.15.18
      esbuild-darwin-64: 0.15.18
      esbuild-darwin-arm64: 0.15.18
      esbuild-freebsd-64: 0.15.18
      esbuild-freebsd-arm64: 0.15.18
      esbuild-linux-32: 0.15.18
      esbuild-linux-64: 0.15.18
      esbuild-linux-arm: 0.15.18
      esbuild-linux-arm64: 0.15.18
      esbuild-linux-mips64le: 0.15.18
      esbuild-linux-ppc64le: 0.15.18
      esbuild-linux-riscv64: 0.15.18
      esbuild-linux-s390x: 0.15.18
      esbuild-netbsd-64: 0.15.18
      esbuild-openbsd-64: 0.15.18
      esbuild-sunos-64: 0.15.18
      esbuild-windows-32: 0.15.18
      esbuild-windows-64: 0.15.18
      esbuild-windows-arm64: 0.15.18
    dev: true

  /escalade@3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==, tarball: https://registry.npmmirror.com/escalade/-/escalade-3.2.0.tgz}
    engines: {node: '>=6'}
    dev: true

  /escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==, tarball: https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz}
    engines: {node: '>=10'}
    dev: false

  /fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==, tarball: https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz}
    dev: false

  /file-selector@0.1.19:
    resolution: {integrity: sha512-kCWw3+Aai8Uox+5tHCNgMFaUdgidxvMnLWO6fM5sZ0hA2wlHP5/DHGF0ECe84BiB95qdJbKNEJhWKVDvMN+JDQ==, tarball: https://registry.npmmirror.com/file-selector/-/file-selector-0.1.19.tgz}
    engines: {node: '>= 10'}
    dependencies:
      tslib: 2.8.1
    dev: false

  /find-root@1.1.0:
    resolution: {integrity: sha512-NKfW6bec6GfKc0SGx1e07QZY9PE99u0Bft/0rzSD5k3sO/vwkVUpDUKVm5Gpp5Ue3YfShPFTX2070tDs5kB9Ng==, tarball: https://registry.npmmirror.com/find-root/-/find-root-1.1.0.tgz}
    dev: false

  /follow-redirects@1.15.9:
    resolution: {integrity: sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==, tarball: https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.9.tgz}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true
    dev: false

  /form-data@4.0.3:
    resolution: {integrity: sha512-qsITQPfmvMOSAdeyZ+12I1c+CKSstAFAwu+97zrnWAbIr5u8wfsExUzCesVLC8NgHuRUqNN4Zy6UPWUTRGslcA==, tarball: https://registry.npmmirror.com/form-data/-/form-data-4.0.3.tgz}
    engines: {node: '>= 6'}
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      hasown: 2.0.2
      mime-types: 2.1.35
    dev: false

  /fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==, tarball: https://registry.npmmirror.com/fsevents/-/fsevents-2.3.3.tgz}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==, tarball: https://registry.npmmirror.com/function-bind/-/function-bind-1.1.2.tgz}

  /gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==, tarball: https://registry.npmmirror.com/gensync/-/gensync-1.0.0-beta.2.tgz}
    engines: {node: '>=6.9.0'}
    dev: true

  /get-intrinsic@1.3.0:
    resolution: {integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==, tarball: https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.3.0.tgz}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0
    dev: false

  /get-proto@1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==, tarball: https://registry.npmmirror.com/get-proto/-/get-proto-1.0.1.tgz}
    engines: {node: '>= 0.4'}
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1
    dev: false

  /globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==, tarball: https://registry.npmmirror.com/globals/-/globals-11.12.0.tgz}
    engines: {node: '>=4'}

  /gopd@1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==, tarball: https://registry.npmmirror.com/gopd/-/gopd-1.2.0.tgz}
    engines: {node: '>= 0.4'}
    dev: false

  /graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==, tarball: https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.11.tgz}
    requiresBuild: true
    optional: true

  /has-symbols@1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==, tarball: https://registry.npmmirror.com/has-symbols/-/has-symbols-1.1.0.tgz}
    engines: {node: '>= 0.4'}
    dev: false

  /has-tostringtag@1.0.2:
    resolution: {integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==, tarball: https://registry.npmmirror.com/has-tostringtag/-/has-tostringtag-1.0.2.tgz}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.1.0
    dev: false

  /hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==, tarball: https://registry.npmmirror.com/hasown/-/hasown-2.0.2.tgz}
    engines: {node: '>= 0.4'}
    dependencies:
      function-bind: 1.1.2

  /hoist-non-react-statics@3.3.2:
    resolution: {integrity: sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==, tarball: https://registry.npmmirror.com/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz}
    dependencies:
      react-is: 16.13.1
    dev: false

  /hyphenate-style-name@1.1.0:
    resolution: {integrity: sha512-WDC/ui2VVRrz3jOVi+XtjqkDjiVjTtFaAGiW37k6b+ohyQ5wYDOGkvCZa8+H0nx3gyvv0+BST9xuOgIyGQ00gw==, tarball: https://registry.npmmirror.com/hyphenate-style-name/-/hyphenate-style-name-1.1.0.tgz}
    dev: false

  /iconv-lite@0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==, tarball: https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.6.3.tgz}
    engines: {node: '>=0.10.0'}
    requiresBuild: true
    dependencies:
      safer-buffer: 2.1.2
    optional: true

  /image-size@0.5.5:
    resolution: {integrity: sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ==, tarball: https://registry.npmmirror.com/image-size/-/image-size-0.5.5.tgz}
    engines: {node: '>=0.10.0'}
    hasBin: true
    requiresBuild: true
    optional: true

  /import-fresh@3.3.1:
    resolution: {integrity: sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==, tarball: https://registry.npmmirror.com/import-fresh/-/import-fresh-3.3.1.tgz}
    engines: {node: '>=6'}
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0
    dev: false

  /is-arrayish@0.2.1:
    resolution: {integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==, tarball: https://registry.npmmirror.com/is-arrayish/-/is-arrayish-0.2.1.tgz}
    dev: false

  /is-core-module@2.16.1:
    resolution: {integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==, tarball: https://registry.npmmirror.com/is-core-module/-/is-core-module-2.16.1.tgz}
    engines: {node: '>= 0.4'}
    dependencies:
      hasown: 2.0.2

  /is-in-browser@1.1.3:
    resolution: {integrity: sha512-FeXIBgG/CPGd/WUxuEyvgGTEfwiG9Z4EKGxjNMRqviiIIfsmgrpnHLffEDdwUHqNva1VEW91o3xBT/m8Elgl9g==, tarball: https://registry.npmmirror.com/is-in-browser/-/is-in-browser-1.1.3.tgz}
    dev: false

  /is-what@3.14.1:
    resolution: {integrity: sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA==, tarball: https://registry.npmmirror.com/is-what/-/is-what-3.14.1.tgz}

  /js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==, tarball: https://registry.npmmirror.com/js-tokens/-/js-tokens-4.0.0.tgz}

  /jsesc@3.1.0:
    resolution: {integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==, tarball: https://registry.npmmirror.com/jsesc/-/jsesc-3.1.0.tgz}
    engines: {node: '>=6'}
    hasBin: true

  /json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==, tarball: https://registry.npmmirror.com/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz}
    dev: false

  /json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==, tarball: https://registry.npmmirror.com/json5/-/json5-2.2.3.tgz}
    engines: {node: '>=6'}
    hasBin: true
    dev: true

  /jss-plugin-camel-case@10.10.0:
    resolution: {integrity: sha512-z+HETfj5IYgFxh1wJnUAU8jByI48ED+v0fuTuhKrPR+pRBYS2EDwbusU8aFOpCdYhtRc9zhN+PJ7iNE8pAWyPw==, tarball: https://registry.npmmirror.com/jss-plugin-camel-case/-/jss-plugin-camel-case-10.10.0.tgz}
    dependencies:
      '@babel/runtime': 7.27.6
      hyphenate-style-name: 1.1.0
      jss: 10.10.0
    dev: false

  /jss-plugin-default-unit@10.10.0:
    resolution: {integrity: sha512-SvpajxIECi4JDUbGLefvNckmI+c2VWmP43qnEy/0eiwzRUsafg5DVSIWSzZe4d2vFX1u9nRDP46WCFV/PXVBGQ==, tarball: https://registry.npmmirror.com/jss-plugin-default-unit/-/jss-plugin-default-unit-10.10.0.tgz}
    dependencies:
      '@babel/runtime': 7.27.6
      jss: 10.10.0
    dev: false

  /jss-plugin-global@10.10.0:
    resolution: {integrity: sha512-icXEYbMufiNuWfuazLeN+BNJO16Ge88OcXU5ZDC2vLqElmMybA31Wi7lZ3lf+vgufRocvPj8443irhYRgWxP+A==, tarball: https://registry.npmmirror.com/jss-plugin-global/-/jss-plugin-global-10.10.0.tgz}
    dependencies:
      '@babel/runtime': 7.27.6
      jss: 10.10.0
    dev: false

  /jss-plugin-nested@10.10.0:
    resolution: {integrity: sha512-9R4JHxxGgiZhurDo3q7LdIiDEgtA1bTGzAbhSPyIOWb7ZubrjQe8acwhEQ6OEKydzpl8XHMtTnEwHXCARLYqYA==, tarball: https://registry.npmmirror.com/jss-plugin-nested/-/jss-plugin-nested-10.10.0.tgz}
    dependencies:
      '@babel/runtime': 7.27.6
      jss: 10.10.0
      tiny-warning: 1.0.3
    dev: false

  /jss-plugin-props-sort@10.10.0:
    resolution: {integrity: sha512-5VNJvQJbnq/vRfje6uZLe/FyaOpzP/IH1LP+0fr88QamVrGJa0hpRRyAa0ea4U/3LcorJfBFVyC4yN2QC73lJg==, tarball: https://registry.npmmirror.com/jss-plugin-props-sort/-/jss-plugin-props-sort-10.10.0.tgz}
    dependencies:
      '@babel/runtime': 7.27.6
      jss: 10.10.0
    dev: false

  /jss-plugin-rule-value-function@10.10.0:
    resolution: {integrity: sha512-uEFJFgaCtkXeIPgki8ICw3Y7VMkL9GEan6SqmT9tqpwM+/t+hxfMUdU4wQ0MtOiMNWhwnckBV0IebrKcZM9C0g==, tarball: https://registry.npmmirror.com/jss-plugin-rule-value-function/-/jss-plugin-rule-value-function-10.10.0.tgz}
    dependencies:
      '@babel/runtime': 7.27.6
      jss: 10.10.0
      tiny-warning: 1.0.3
    dev: false

  /jss-plugin-vendor-prefixer@10.10.0:
    resolution: {integrity: sha512-UY/41WumgjW8r1qMCO8l1ARg7NHnfRVWRhZ2E2m0DMYsr2DD91qIXLyNhiX83hHswR7Wm4D+oDYNC1zWCJWtqg==, tarball: https://registry.npmmirror.com/jss-plugin-vendor-prefixer/-/jss-plugin-vendor-prefixer-10.10.0.tgz}
    dependencies:
      '@babel/runtime': 7.27.6
      css-vendor: 2.0.8
      jss: 10.10.0
    dev: false

  /jss@10.10.0:
    resolution: {integrity: sha512-cqsOTS7jqPsPMjtKYDUpdFC0AbhYFLTcuGRqymgmdJIeQ8cH7+AgX7YSgQy79wXloZq2VvATYxUOUQEvS1V/Zw==, tarball: https://registry.npmmirror.com/jss/-/jss-10.10.0.tgz}
    dependencies:
      '@babel/runtime': 7.27.6
      csstype: 3.1.3
      is-in-browser: 1.1.3
      tiny-warning: 1.0.3
    dev: false

  /keycharm@0.4.0:
    resolution: {integrity: sha512-TyQTtsabOVv3MeOpR92sIKk/br9wxS+zGj4BG7CR8YbK4jM3tyIBaF0zhzeBUMx36/Q/iQLOKKOT+3jOQtemRQ==, tarball: https://registry.npmmirror.com/keycharm/-/keycharm-0.4.0.tgz}
    dev: false

  /less-loader@12.3.0(less@4.3.0):
    resolution: {integrity: sha512-0M6+uYulvYIWs52y0LqN4+QM9TqWAohYSNTo4htE8Z7Cn3G/qQMEmktfHmyJT23k+20kU9zHH2wrfFXkxNLtVw==, tarball: https://registry.npmmirror.com/less-loader/-/less-loader-12.3.0.tgz}
    engines: {node: '>= 18.12.0'}
    peerDependencies:
      '@rspack/core': 0.x || 1.x
      less: ^3.5.0 || ^4.0.0
      webpack: ^5.0.0
    peerDependenciesMeta:
      '@rspack/core':
        optional: true
      webpack:
        optional: true
    dependencies:
      less: 4.3.0
    dev: true

  /less@4.3.0:
    resolution: {integrity: sha512-X9RyH9fvemArzfdP8Pi3irr7lor2Ok4rOttDXBhlwDg+wKQsXOXgHWduAJE1EsF7JJx0w0bcO6BC6tCKKYnXKA==, tarball: https://registry.npmmirror.com/less/-/less-4.3.0.tgz}
    engines: {node: '>=14'}
    hasBin: true
    dependencies:
      copy-anything: 2.0.6
      parse-node-version: 1.0.1
      tslib: 2.8.1
    optionalDependencies:
      errno: 0.1.8
      graceful-fs: 4.2.11
      image-size: 0.5.5
      make-dir: 2.1.0
      mime: 1.6.0
      needle: 3.3.1
      source-map: 0.6.1

  /lines-and-columns@1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==, tarball: https://registry.npmmirror.com/lines-and-columns/-/lines-and-columns-1.2.4.tgz}
    dev: false

  /loose-envify@1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==, tarball: https://registry.npmmirror.com/loose-envify/-/loose-envify-1.4.0.tgz}
    hasBin: true
    dependencies:
      js-tokens: 4.0.0
    dev: false

  /lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==, tarball: https://registry.npmmirror.com/lru-cache/-/lru-cache-5.1.1.tgz}
    dependencies:
      yallist: 3.1.1
    dev: true

  /magic-string@0.26.7:
    resolution: {integrity: sha512-hX9XH3ziStPoPhJxLq1syWuZMxbDvGNbVchfrdCtanC7D13888bMFow61x8axrx+GfHLtVeAx2kxL7tTGRl+Ow==, tarball: https://registry.npmmirror.com/magic-string/-/magic-string-0.26.7.tgz}
    engines: {node: '>=12'}
    dependencies:
      sourcemap-codec: 1.4.8
    dev: true

  /make-dir@2.1.0:
    resolution: {integrity: sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==, tarball: https://registry.npmmirror.com/make-dir/-/make-dir-2.1.0.tgz}
    engines: {node: '>=6'}
    requiresBuild: true
    dependencies:
      pify: 4.0.1
      semver: 5.7.2
    optional: true

  /material-ui-dropzone@3.5.0(@material-ui/core@4.12.4)(@material-ui/icons@4.11.3)(prop-types@15.8.1)(react-dom@18.3.1)(react@18.3.1):
    resolution: {integrity: sha512-3BC6mz/4OEM4ZpbqMfuMN065JQyqfEbifT6/VzIua7Zj4b0DaR5YPCgpN+fL/e8yBgTs9MGBZJQY06p5pfKwvw==, tarball: https://registry.npmmirror.com/material-ui-dropzone/-/material-ui-dropzone-3.5.0.tgz}
    engines: {node: '>=8', yarn: '>=1'}
    peerDependencies:
      '@material-ui/core': ^4.0.0
      '@material-ui/icons': ^4.0.0
      prop-types: ^15.7.2
      react: '>= 16.8'
      react-dom: '>= 16.8'
    dependencies:
      '@babel/runtime': 7.27.6
      '@material-ui/core': 4.12.4(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1)
      '@material-ui/icons': 4.11.3(@material-ui/core@4.12.4)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1)
      clsx: 1.2.1
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-dropzone: 10.2.2(react@18.3.1)
    dev: false

  /math-intrinsics@1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==, tarball: https://registry.npmmirror.com/math-intrinsics/-/math-intrinsics-1.1.0.tgz}
    engines: {node: '>= 0.4'}
    dev: false

  /mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==, tarball: https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz}
    engines: {node: '>= 0.6'}
    dev: false

  /mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==, tarball: https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.52.0
    dev: false

  /mime@1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==, tarball: https://registry.npmmirror.com/mime/-/mime-1.6.0.tgz}
    engines: {node: '>=4'}
    hasBin: true
    requiresBuild: true
    optional: true

  /moment@2.30.1:
    resolution: {integrity: sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==, tarball: https://registry.npmmirror.com/moment/-/moment-2.30.1.tgz}
    dev: false

  /ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==, tarball: https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz}

  /nanoid@3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==, tarball: https://registry.npmmirror.com/nanoid/-/nanoid-3.3.11.tgz}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true
    dev: true

  /needle@3.3.1:
    resolution: {integrity: sha512-6k0YULvhpw+RoLNiQCRKOl09Rv1dPLr8hHnVjHqdolKwDrdNyk+Hmrthi4lIGPPz3r39dLx0hsF5s40sZ3Us4Q==, tarball: https://registry.npmmirror.com/needle/-/needle-3.3.1.tgz}
    engines: {node: '>= 4.4.x'}
    hasBin: true
    requiresBuild: true
    dependencies:
      iconv-lite: 0.6.3
      sax: 1.4.1
    optional: true

  /node-releases@2.0.19:
    resolution: {integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==, tarball: https://registry.npmmirror.com/node-releases/-/node-releases-2.0.19.tgz}
    dev: true

  /object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==, tarball: https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz}
    engines: {node: '>=0.10.0'}
    dev: false

  /parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==, tarball: https://registry.npmmirror.com/parent-module/-/parent-module-1.0.1.tgz}
    engines: {node: '>=6'}
    dependencies:
      callsites: 3.1.0
    dev: false

  /parse-json@5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==, tarball: https://registry.npmmirror.com/parse-json/-/parse-json-5.2.0.tgz}
    engines: {node: '>=8'}
    dependencies:
      '@babel/code-frame': 7.27.1
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4
    dev: false

  /parse-node-version@1.0.1:
    resolution: {integrity: sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA==, tarball: https://registry.npmmirror.com/parse-node-version/-/parse-node-version-1.0.1.tgz}
    engines: {node: '>= 0.10'}

  /path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==, tarball: https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz}

  /path-type@4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==, tarball: https://registry.npmmirror.com/path-type/-/path-type-4.0.0.tgz}
    engines: {node: '>=8'}
    dev: false

  /picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==, tarball: https://registry.npmmirror.com/picocolors/-/picocolors-1.1.1.tgz}

  /pify@4.0.1:
    resolution: {integrity: sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==, tarball: https://registry.npmmirror.com/pify/-/pify-4.0.1.tgz}
    engines: {node: '>=6'}
    requiresBuild: true
    optional: true

  /popper.js@1.16.1-lts:
    resolution: {integrity: sha512-Kjw8nKRl1m+VrSFCoVGPph93W/qrSO7ZkqPpTf7F4bk/sqcfWK019dWBUpE/fBOsOQY1dks/Bmcbfn1heM/IsA==, tarball: https://registry.npmmirror.com/popper.js/-/popper.js-1.16.1-lts.tgz}
    dev: false

  /postcss@8.5.6:
    resolution: {integrity: sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==, tarball: https://registry.npmmirror.com/postcss/-/postcss-8.5.6.tgz}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1
    dev: true

  /prop-types@15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==, tarball: https://registry.npmmirror.com/prop-types/-/prop-types-15.8.1.tgz}
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1
    dev: false

  /proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==, tarball: https://registry.npmmirror.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz}
    dev: false

  /prr@1.0.1:
    resolution: {integrity: sha512-yPw4Sng1gWghHQWj0B3ZggWUm4qVbPwPFcRG8KyxiU7J2OHFSoEHKS+EZ3fv5l1t9CyCiop6l/ZYeWbrgoQejw==, tarball: https://registry.npmmirror.com/prr/-/prr-1.0.1.tgz}
    requiresBuild: true
    optional: true

  /react-dom@18.3.1(react@18.3.1):
    resolution: {integrity: sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==, tarball: https://registry.npmmirror.com/react-dom/-/react-dom-18.3.1.tgz}
    peerDependencies:
      react: ^18.3.1
    dependencies:
      loose-envify: 1.4.0
      react: 18.3.1
      scheduler: 0.23.2
    dev: false

  /react-dropzone@10.2.2(react@18.3.1):
    resolution: {integrity: sha512-U5EKckXVt6IrEyhMMsgmHQiWTGLudhajPPG77KFSvgsMqNEHSyGpqWvOMc5+DhEah/vH4E1n+J5weBNLd5VtyA==, tarball: https://registry.npmmirror.com/react-dropzone/-/react-dropzone-10.2.2.tgz}
    engines: {node: '>= 8'}
    peerDependencies:
      react: '>= 16.8'
    dependencies:
      attr-accept: 2.2.5
      file-selector: 0.1.19
      prop-types: 15.8.1
      react: 18.3.1
    dev: false

  /react-is@16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==, tarball: https://registry.npmmirror.com/react-is/-/react-is-16.13.1.tgz}
    dev: false

  /react-is@17.0.2:
    resolution: {integrity: sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==, tarball: https://registry.npmmirror.com/react-is/-/react-is-17.0.2.tgz}
    dev: false

  /react-is@19.1.0:
    resolution: {integrity: sha512-Oe56aUPnkHyyDxxkvqtd7KkdQP5uIUfHxd5XTb3wE9d/kRnZLmKbDB0GWk919tdQ+mxxPtG6EAs6RMT6i1qtHg==, tarball: https://registry.npmmirror.com/react-is/-/react-is-19.1.0.tgz}
    dev: false

  /react-refresh@0.14.2:
    resolution: {integrity: sha512-jCvmsr+1IUSMUyzOkRcvnVbX3ZYC6g9TDrDbFuFmRDq7PD4yaGbLKNQL6k2jnArV8hjYxh7hVhAZB6s9HDGpZA==, tarball: https://registry.npmmirror.com/react-refresh/-/react-refresh-0.14.2.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /react-router-dom@7.6.2(react-dom@18.3.1)(react@18.3.1):
    resolution: {integrity: sha512-Q8zb6VlTbdYKK5JJBLQEN06oTUa/RAbG/oQS1auK1I0TbJOXktqm+QENEVJU6QvWynlXPRBXI3fiOQcSEA78rA==, tarball: https://registry.npmmirror.com/react-router-dom/-/react-router-dom-7.6.2.tgz}
    engines: {node: '>=20.0.0'}
    peerDependencies:
      react: '>=18'
      react-dom: '>=18'
    dependencies:
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-router: 7.6.2(react-dom@18.3.1)(react@18.3.1)
    dev: false

  /react-router@7.6.2(react-dom@18.3.1)(react@18.3.1):
    resolution: {integrity: sha512-U7Nv3y+bMimgWjhlT5CRdzHPu2/KVmqPwKUCChW8en5P3znxUqwlYFlbmyj8Rgp1SF6zs5X4+77kBVknkg6a0w==, tarball: https://registry.npmmirror.com/react-router/-/react-router-7.6.2.tgz}
    engines: {node: '>=20.0.0'}
    peerDependencies:
      react: '>=18'
      react-dom: '>=18'
    peerDependenciesMeta:
      react-dom:
        optional: true
    dependencies:
      cookie: 1.0.2
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      set-cookie-parser: 2.7.1
    dev: false

  /react-transition-group@4.4.5(react-dom@18.3.1)(react@18.3.1):
    resolution: {integrity: sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==, tarball: https://registry.npmmirror.com/react-transition-group/-/react-transition-group-4.4.5.tgz}
    peerDependencies:
      react: '>=16.6.0'
      react-dom: '>=16.6.0'
    dependencies:
      '@babel/runtime': 7.27.6
      dom-helpers: 5.2.1
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    dev: false

  /react@18.3.1:
    resolution: {integrity: sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==, tarball: https://registry.npmmirror.com/react/-/react-18.3.1.tgz}
    engines: {node: '>=0.10.0'}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==, tarball: https://registry.npmmirror.com/resolve-from/-/resolve-from-4.0.0.tgz}
    engines: {node: '>=4'}
    dev: false

  /resolve@1.22.10:
    resolution: {integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==, tarball: https://registry.npmmirror.com/resolve/-/resolve-1.22.10.tgz}
    engines: {node: '>= 0.4'}
    hasBin: true
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  /rifm@0.7.0(react@18.3.1):
    resolution: {integrity: sha512-DSOJTWHD67860I5ojetXdEQRIBvF6YcpNe53j0vn1vp9EUb9N80EiZTxgP+FkDKorWC8PZw052kTF4C1GOivCQ==, tarball: https://registry.npmmirror.com/rifm/-/rifm-0.7.0.tgz}
    peerDependencies:
      react: '>=16.8'
    dependencies:
      '@babel/runtime': 7.27.6
      react: 18.3.1
    dev: false

  /rollup@2.79.2:
    resolution: {integrity: sha512-fS6iqSPZDs3dr/y7Od6y5nha8dW1YnbgtsyotCVvoFGKbERG++CVRFv1meyGDE1SNItQA8BrnCw7ScdAhRJ3XQ==, tarball: https://registry.npmmirror.com/rollup/-/rollup-2.79.2.tgz}
    engines: {node: '>=10.0.0'}
    hasBin: true
    optionalDependencies:
      fsevents: 2.3.3
    dev: true

  /safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==, tarball: https://registry.npmmirror.com/safer-buffer/-/safer-buffer-2.1.2.tgz}
    requiresBuild: true
    optional: true

  /sax@1.4.1:
    resolution: {integrity: sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg==, tarball: https://registry.npmmirror.com/sax/-/sax-1.4.1.tgz}
    requiresBuild: true
    optional: true

  /scheduler@0.23.2:
    resolution: {integrity: sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==, tarball: https://registry.npmmirror.com/scheduler/-/scheduler-0.23.2.tgz}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /semver@5.7.2:
    resolution: {integrity: sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==, tarball: https://registry.npmmirror.com/semver/-/semver-5.7.2.tgz}
    hasBin: true
    requiresBuild: true
    optional: true

  /semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==, tarball: https://registry.npmmirror.com/semver/-/semver-6.3.1.tgz}
    hasBin: true
    dev: true

  /set-cookie-parser@2.7.1:
    resolution: {integrity: sha512-IOc8uWeOZgnb3ptbCURJWNjWUPcO3ZnTTdzsurqERrP6nPyv+paC55vJM0LpOlT2ne+Ix+9+CRG1MNLlyZ4GjQ==, tarball: https://registry.npmmirror.com/set-cookie-parser/-/set-cookie-parser-2.7.1.tgz}
    dev: false

  /size-sensor@1.0.2:
    resolution: {integrity: sha512-2NCmWxY7A9pYKGXNBfteo4hy14gWu47rg5692peVMst6lQLPKrVjhY+UTEsPI5ceFRJSl3gVgMYaUi/hKuaiKw==, tarball: https://registry.npmmirror.com/size-sensor/-/size-sensor-1.0.2.tgz}
    dev: false

  /source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==, tarball: https://registry.npmmirror.com/source-map-js/-/source-map-js-1.2.1.tgz}
    engines: {node: '>=0.10.0'}
    dev: true

  /source-map@0.5.7:
    resolution: {integrity: sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==, tarball: https://registry.npmmirror.com/source-map/-/source-map-0.5.7.tgz}
    engines: {node: '>=0.10.0'}
    dev: false

  /source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==, tarball: https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz}
    engines: {node: '>=0.10.0'}
    requiresBuild: true
    optional: true

  /sourcemap-codec@1.4.8:
    resolution: {integrity: sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA==, tarball: https://registry.npmmirror.com/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz}
    deprecated: Please use @jridgewell/sourcemap-codec instead
    dev: true

  /stylis@4.2.0:
    resolution: {integrity: sha512-Orov6g6BB1sDfYgzWfTHDOxamtX1bE/zo104Dh9e6fqJ3PooipYyfJ0pUmrZO2wAvO8YbEyeFrkV91XTsGMSrw==, tarball: https://registry.npmmirror.com/stylis/-/stylis-4.2.0.tgz}
    dev: false

  /supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==, tarball: https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz}
    engines: {node: '>= 0.4'}

  /tiny-warning@1.0.3:
    resolution: {integrity: sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA==, tarball: https://registry.npmmirror.com/tiny-warning/-/tiny-warning-1.0.3.tgz}
    dev: false

  /tslib@2.3.0:
    resolution: {integrity: sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg==, tarball: https://registry.npmmirror.com/tslib/-/tslib-2.3.0.tgz}
    dev: false

  /tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==, tarball: https://registry.npmmirror.com/tslib/-/tslib-2.8.1.tgz}

  /typescript@4.9.5:
    resolution: {integrity: sha512-1FXk9E2Hm+QzZQ7z+McJiHL4NW1F2EzMu9Nq9i3zAaGqibafqYwCVU6WyWAuyQRRzOlxou8xZSyXLEN8oKj24g==, tarball: https://registry.npmmirror.com/typescript/-/typescript-4.9.5.tgz}
    engines: {node: '>=4.2.0'}
    hasBin: true
    dev: true

  /update-browserslist-db@1.1.3(browserslist@4.25.1):
    resolution: {integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==, tarball: https://registry.npmmirror.com/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: 4.25.1
      escalade: 3.2.0
      picocolors: 1.1.1
    dev: true

  /uuid@9.0.1:
    resolution: {integrity: sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==, tarball: https://registry.npmmirror.com/uuid/-/uuid-9.0.1.tgz}
    hasBin: true
    dev: false

  /vis-data@7.1.9(uuid@9.0.1)(vis-util@5.0.7):
    resolution: {integrity: sha512-COQsxlVrmcRIbZMMTYwD+C2bxYCFDNQ2EHESklPiInbD/Pk3JZ6qNL84Bp9wWjYjAzXfSlsNaFtRk+hO9yBPWA==, tarball: https://registry.npmmirror.com/vis-data/-/vis-data-7.1.9.tgz}
    peerDependencies:
      uuid: ^3.4.0 || ^7.0.0 || ^8.0.0 || ^9.0.0
      vis-util: ^5.0.1
    dependencies:
      uuid: 9.0.1
      vis-util: 5.0.7(@egjs/hammerjs@2.0.17)(component-emitter@2.0.0)
    dev: false

  /vis-network@9.1.12(@egjs/hammerjs@2.0.17)(component-emitter@2.0.0)(keycharm@0.4.0)(uuid@9.0.1)(vis-data@7.1.9)(vis-util@5.0.7):
    resolution: {integrity: sha512-kZ7eOwfK1hGwsnCsGm02M7c5WBjgwTQ7BzF9bX7JaRqntJINWyjRiFBPqeDCOoOdgLEmwCXNk/VXYcIoK0ndyw==, tarball: https://registry.npmmirror.com/vis-network/-/vis-network-9.1.12.tgz}
    peerDependencies:
      '@egjs/hammerjs': ^2.0.0
      component-emitter: ^1.3.0 || ^2.0.0
      keycharm: ^0.2.0 || ^0.3.0 || ^0.4.0
      uuid: ^3.4.0 || ^7.0.0 || ^8.0.0 || ^9.0.0 || ^10.0.0 || ^11.0.0
      vis-data: ^6.3.0 || ^7.0.0
      vis-util: ^5.0.1
    dependencies:
      '@egjs/hammerjs': 2.0.17
      component-emitter: 2.0.0
      keycharm: 0.4.0
      uuid: 9.0.1
      vis-data: 7.1.9(uuid@9.0.1)(vis-util@5.0.7)
      vis-util: 5.0.7(@egjs/hammerjs@2.0.17)(component-emitter@2.0.0)
    dev: false

  /vis-util@5.0.7(@egjs/hammerjs@2.0.17)(component-emitter@2.0.0):
    resolution: {integrity: sha512-E3L03G3+trvc/X4LXvBfih3YIHcKS2WrP0XTdZefr6W6Qi/2nNCqZfe4JFfJU6DcQLm6Gxqj2Pfl+02859oL5A==, tarball: https://registry.npmmirror.com/vis-util/-/vis-util-5.0.7.tgz}
    engines: {node: '>=8'}
    peerDependencies:
      '@egjs/hammerjs': ^2.0.0
      component-emitter: ^1.3.0 || ^2.0.0
    dependencies:
      '@egjs/hammerjs': 2.0.17
      component-emitter: 2.0.0
    dev: false

  /vite@3.2.11(less@4.3.0):
    resolution: {integrity: sha512-K/jGKL/PgbIgKCiJo5QbASQhFiV02X9Jh+Qq0AKCRCRKZtOTVi4t6wh75FDpGf2N9rYOnzH87OEFQNaFy6pdxQ==, tarball: https://registry.npmmirror.com/vite/-/vite-3.2.11.tgz}
    engines: {node: ^14.18.0 || >=16.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': '>= 14'
      less: '*'
      sass: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.4.0
    peerDependenciesMeta:
      '@types/node':
        optional: true
      less:
        optional: true
      sass:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
    dependencies:
      esbuild: 0.15.18
      less: 4.3.0
      postcss: 8.5.6
      resolve: 1.22.10
      rollup: 2.79.2
    optionalDependencies:
      fsevents: 2.3.3
    dev: true

  /yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==, tarball: https://registry.npmmirror.com/yallist/-/yallist-3.1.1.tgz}
    dev: true

  /yaml@1.10.2:
    resolution: {integrity: sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==, tarball: https://registry.npmmirror.com/yaml/-/yaml-1.10.2.tgz}
    engines: {node: '>= 6'}
    dev: false

  /zrender@5.6.1:
    resolution: {integrity: sha512-OFXkDJKcrlx5su2XbzJvj/34Q3m6PvyCZkVPHGYpcCJ52ek4U/ymZyfuV1nKE23AyBJ51E/6Yr0mhZ7xGTO4ag==, tarball: https://registry.npmmirror.com/zrender/-/zrender-5.6.1.tgz}
    dependencies:
      tslib: 2.3.0
    dev: false
