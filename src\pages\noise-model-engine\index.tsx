import React, { useState, useRef, useEffect } from "react";
import { Typography, Breadcrum<PERSON>, Link, Box } from "@mui/material";
import ConfigList from "./component/ConfigList";
import ConfigForm from "./component/ConfigForm";
import { ConfigType } from "./types/types";
import axios from "axios";
import { use } from "echarts";
import apiClient from "../apis/apiClient";
import { MessageOutlined } from "@material-ui/icons";
import { showSnackbar } from "./component/myMessageBar";


// 假数据数组，结构完全对齐你的 ConfigType


export default function NoiseModelEngine() {
  const [editConfig, setEditConfig] = useState<ConfigType | undefined>(undefined);
  const [formOpen, setFormOpen] = useState(false);
  const configListRef = useRef<{ reload: () => void }>(null);

  // 编辑
  const handleEdit = (config: ConfigType) => {
    setEditConfig(config);
    setFormOpen(true);
  };

  // 新增
  const handleAdd = () => {
    setEditConfig(undefined);
    setFormOpen(true);
  };

  // 删除
  const handleDelete = async (id: number | undefined) => {
    if (!id) return;
    apiClient.delete(`/api/noise-reduction/configs/${id}`).then(res=> {
      showSnackbar('删除成功', 'success');
      configListRef.current?.reload();
    },err => {
      showSnackbar(err?.response?.data?.error || err?.message || "删除失败！", "error");
    });
  };

  // 保存（新增/编辑）
  const handleSave = async (config: ConfigType) => {
    if (config.id) {
      // 编辑
      apiClient.put(`/api/noise-reduction/configs/${config.id}`, config).then(() => {
        setFormOpen(false);
        setEditConfig(undefined);
        configListRef.current?.reload();
        showSnackbar("保存成功！", 'success');
      }, err => {
        console.log(err)
        showSnackbar(err?.response?.data?.error || err?.message || "保存失败！", "error")
      })
    } else {
      // 新增
      apiClient.post(`/api/noise-reduction/configs/`, config).then(() => {
        setFormOpen(false);
        setEditConfig(undefined);
        configListRef.current?.reload();
        showSnackbar("保存成功！", 'success')
      }, err => {
        showSnackbar(err?.response?.data?.error || err?.message || "保存失败！", "error")
      });
    }
  };

  // 取消
  const handleCancel = () => {
    setFormOpen(false);
    setEditConfig(undefined);
  };

  useEffect(() => {
    if (formOpen == false) {
      setEditConfig(undefined)
    }
  }, [open]);

  return (
    <div style={{ display: 'flex', flexDirection: 'column',width: "100%", minHeight: "calc(100vh - 60px)", paddingBottom: 60, borderRadius: 8 }}>
      {/* <Box p={2}>
        <Typography variant="h5" color="success" fontWeight="bold" gutterBottom>
          智能降噪配置
        </Typography>
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
          <Link underline="hover" color="inherit" href="#">
            智能降噪中心
          </Link>
          <Typography color="text.primary">智能降噪配置</Typography>
        </Breadcrumbs>
      </Box> */}
      <div style={{flex: 1, height:"100%", display:"flex", flexDirection:"column"}}>
        <div style={{ padding: "0px 16px", flex: 1, backgroundColor: "#fff", display: "flex", flexDirection: "column" }}>
        <ConfigList
          onEdit={handleEdit}
          onDelete={handleDelete}
          onAdd={handleAdd}
          ref={configListRef}
        />
      </div>
      <ConfigForm
        open={formOpen}
        config={editConfig}
        onSave={handleSave}
        onClose={handleCancel}
      />
      </div>
      
    </div>
  );
}
