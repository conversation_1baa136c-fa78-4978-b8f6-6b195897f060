import React, { useState, ChangeEvent, useEffect, useRef } from 'react';
import {
  <PERSON>,
  Tabs,
  Tab,
  <PERSON><PERSON><PERSON>,
  TextField,
  MenuItem,
  Button,
  Grid,
  Paper,
  GridProps,
  Popover,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  Pagination,
  InputAdornment,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Divider,
  Card,
  CardContent,
} from '@mui/material';
import { ArrowDropDown } from '@mui/icons-material';
import AdvancedFilterPage from '../advanced-filter/index';
import SingleAlertPage from '../OneAlter/index';
import { styled } from '@mui/material/styles';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

// 配置项接口
interface ConfigItem {
  id: number;
  config_name: string;
  config_type: string;
  description: string;
  is_active: boolean;
  is_default: boolean;
}

// 分析结果接口
interface AnalysisResult {
  alert_format: string;
  alert_text: string;
  analysis_result: {
    alert: {
      alert_type: string;
      description: string;
      extra_metadata: Record<string, any>;
      id: string;
      raw_alert: string;
      severity: string;
      source: string;
      timestamp: string;
      title: string;
    };
    result: {
      action: string;
      alert_id: string;
      confidence: number;
      merge_with: string | null;
      metadata: Record<string, any>;
      new_severity: string | null;
      reason: string;
      suppression_rule: string | null;
    };
  };
  code: number;
  config_id: number;
  config_name: string;
  message: string;
  success: boolean;
  system_info: {
    confidence_threshold: number;
    model_name: string;
    model_type: string;
  };
}

const PREFIX = 'AlertAnalysis';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  paddingLeft: theme.spacing(3),
  paddingRight: theme.spacing(3),
  borderRadius: 10,
  backgroundColor: '#ffffff',
}));

const TabPanel = ({ children, value, index, ...other }: TabPanelProps) => (
  <div role="tabpanel" hidden={value !== index} {...other}>
    {value === index && <Box sx={{ mt: 2 }}>{children}</Box>}
  </div>
);

const AlertAnalysisPage: React.FC = () => {
  const [tabIndex, setTabIndex] = useState<number>(0);
  const [singleText, setSingleText] = useState<string>('');
  const [format, setFormat] = useState<string>('auto');
  const [config, setConfig] = useState<string>('default');
  const [taskName, setTaskName] = useState<string>('');
  const [filePreview, setFilePreview] = useState<string>('文件内容将在此处显示前5行...');
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [batchLoading, setBatchLoading] = useState<boolean>(false);
  const [configs, setConfigs] = useState<ConfigItem[]>([]);
  const [configsLoading, setConfigsLoading] = useState<boolean>(false);

  // 分页相关状态
  const [configPage, setConfigPage] = useState<number>(1);
  const [configTotalPages, setConfigTotalPages] = useState<number>(1);
  const [configPopoverAnchor, setConfigPopoverAnchor] = useState<HTMLElement | null>(null);
  const configsPerPage = 10;

  // 分析结果模态框状态
  const [analysisResultOpen, setAnalysisResultOpen] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);

  // TextField引用，用于获取准确的宽度
  const configTextFieldRef = useRef<HTMLDivElement>(null);
  const [popoverWidth, setPopoverWidth] = useState<number>(0);

  const api = import.meta.env.VITE_BACK_URL; // 接口url

  // 获取配置列表（分页）
  const fetchConfigs = async (page: number = 1) => {
    try {
      setConfigsLoading(true);
      const response = await fetch(`${api}/api/noise-reduction/configs/?page=${page}&per_page=${configsPerPage}`);
      const data = await response.json();

      if (response.ok && data.code === 200) {
        setConfigs(data.configs || []);
        if (data.pagination) {
          setConfigTotalPages(data.pagination.pages || 1);
        }
      } else {
        console.error('获取配置列表失败:', data.message);
        setConfigs([]);
      }
    } catch (error) {
      console.error('获取配置列表请求失败:', error);
      setConfigs([]);
    } finally {
      setConfigsLoading(false);
    }
  };

  // 组件挂载时获取配置列表
  useEffect(() => {
    fetchConfigs();
  }, []);

  // 处理配置分页变化
  const handleConfigPageChange = (_: React.ChangeEvent<unknown>, page: number) => {
    setConfigPage(page);
    fetchConfigs(page);
  };

  // 处理配置选择框点击
  const handleConfigClick = (event: React.MouseEvent<HTMLElement>) => {
    const target = event.currentTarget;
    setConfigPopoverAnchor(target);

    // 设置Popover宽度为TextField的宽度
    // 优先使用ref，如果不可用则使用事件目标的宽度
    if (configTextFieldRef.current) {
      setPopoverWidth(configTextFieldRef.current.offsetWidth);
    } else if (target) {
      setPopoverWidth(target.offsetWidth);
    } else {
      setPopoverWidth(300); // 默认宽度
    }

    if (configs.length === 0) {
      fetchConfigs(1);
    }
  };

  // 关闭配置选择弹出框
  const handleConfigClose = () => {
    setConfigPopoverAnchor(null);
  };

  // 选择配置
  const handleConfigSelect = (configId: string, configName: string) => {
    setConfig(configId);
    handleConfigClose();
  };

  // 获取当前选中配置的名称
  const getSelectedConfigName = () => {
    if (config === 'default') return '-- 请选择 --';
    const selectedConfig = configs.find(c => c.id.toString() === config);
    if (selectedConfig) {
      // 如果配置名称超过14个字符，进行省略显示
      const configName = selectedConfig.config_name;
      return configName.length > 14 ? configName.substring(0, 14) + '...' : configName;
    }
    return '-- 请选择 --';
  };

  const handleReset = () => {
    setFormat('auto');
    setConfig('default');
    setTaskName('');
    setFilePreview('文件内容将在此处显示前5行...');
    setFile(null);
  };

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabIndex(newValue);
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      const reader = new FileReader();
      reader.onload = (event) => {
        const text = event.target?.result as string;
        const preview = text.split('\n').slice(0, 5).join('\n');
        setFilePreview(preview);
      };
      reader.readAsText(selectedFile);
    }
  };

  const analyzeSingleAlert = async () => {
    try {
      setLoading(true);
      
      // 根据所选格式构建请求格式
      let formatValue = format;
      if (format === 'format1') formatValue = 'syslog';
      else if (format === 'format2') formatValue = 'json';
      
      // 构建请求参数
      const payload = {
        alert_text: singleText,
        alert_format: formatValue,
        config_id: config === 'default' ? 1 : parseInt(config)
      };
      
      // 发送请求
      const response = await fetch(`${api}/api/noise-reduction/alerts/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });
      
      const data = await response.json();
      console.log('分析结果:', data);

      // 如果分析成功，显示结果模态框
      if (data.success) {
        setAnalysisResult(data);
        setAnalysisResultOpen(true);
      }
    } catch (error) {
      console.error('分析告警失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const analyzeBatchAlerts = async () => {
    if (!file) return;

    try {
      setBatchLoading(true);
      
      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', file);
      
      // 根据所选格式设置alert_format
      if (format !== 'auto') {
        let formatValue = format;
        if (format === 'format1') formatValue = 'syslog';
        else if (format === 'format2') formatValue = 'json';
        formData.append('alert_format', formatValue);
      }
      
      // 如果选择了配置，添加config_id
      if (config !== 'default') {
        const configId = parseInt(config);
        formData.append('config_id', configId.toString());
      }
      
      // 如果有任务名称，添加task_name
      if (taskName) {
        formData.append('task_name', taskName);
      }
      
      // 发送请求
      const response = await fetch(`${api}/api/noise-reduction/alerts/batch-analyze`, {
        method: 'POST',
        body: formData, // FormData会自动设置Content-Type: multipart/form-data
      });
      
      const data = await response.json();
      console.log('批量分析结果:', data);
      
      if (response.ok) {
        // 请求成功
        console.log('任务ID:', data.task_id);
        console.log('文件信息:', data.file_info);
      } else {
        // 请求失败
        console.error('批量分析失败:', data.error);
      }
      
    } catch (error) {
      console.error('批量告警分析请求失败:', error);
    } finally {
      setBatchLoading(false);
    }
  };

  return (
    <Box sx={{ width: '100%', p: 2, backgroundColor: '#e0e0e0', minHeight: '100vh' }}>
      <Tabs
        value={tabIndex}
        onChange={handleTabChange}
        aria-label="alert analysis tabs"
        sx={{
            backgroundColor: 'transparent',
            minHeight: 48,
            '& .MuiTabs-indicator': {
            display: 'none', // 移除下划线
            },
        }}
        >
        <Tab
            label="单个告警降噪"
            sx={{
            fontWeight: 'bold',
            color: 'green',
            minHeight: 48,
            textTransform: 'none',
            borderRadius: '6px 6px 0 0',
            '&.Mui-selected': {
                backgroundColor: '#ffffff',
                color: 'green',
                fontWeight: 'bold',
                boxShadow: '0 -2px 5px rgba(0,0,0,0.1)',
            },
            }}
        />
        <Tab
            label="批量告警降噪"
            sx={{
            fontWeight: 'bold',
            color: 'green',
            minHeight: 48,
            textTransform: 'none',
            borderRadius: '6px 6px 0 0',
            '&.Mui-selected': {
                backgroundColor: '#ffffff',
                color: 'green',
                fontWeight: 'bold',
                boxShadow: '0 -2px 5px rgba(0,0,0,0.1)',
            },
            }}
        />
        </Tabs>

      {/* 单个告警分析 */}
      <TabPanel value={tabIndex} index={0}>
        <StyledPaper>
          <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'green' }}>
            告警文本 *
          </Typography>
          <TextField
            fullWidth
            multiline
            minRows={5}
            placeholder="请输入告警文本内容..."
            value={singleText}
            onChange={(e) => setSingleText(e.target.value)}
            sx={{ mt: 1, ml: 0 }}
          />
          <Typography variant="body2" sx={{ mt: 1, color: 'gray' }}>
            示例: [2023-05-15 10:23:45] WARNING: Unauthorized access attempt detected from 192.168.1.100
          </Typography>

          <Grid container spacing={0} sx={{ mt: 2, pl: 0 }}>
            <Grid
                item
                xs={12}
                sm={6}
                component="div"
                {...({} as GridProps)}
                sx={{ pl: 0, ml: 0, pr: { xs: 0, sm: 1 } }}
            >
                <Typography sx={{ fontWeight: 'bold', color: 'green' }}>告警格式</Typography>
                <TextField
                select
                fullWidth
                value={format}
                onChange={(e) => setFormat(e.target.value)}
                sx={{ ml: 0 }}
                >
                <MenuItem value="auto">自动检测 (auto)</MenuItem>
                <MenuItem value="format1">格式1</MenuItem>
                <MenuItem value="format2">格式2</MenuItem>
                </TextField>
            </Grid>

            <Grid
                item
                xs={12}
                sm={6}
                component="div"
                {...({} as GridProps)}
                sx={{ pl: 0, ml: 0, pr: { xs: 0, sm: 1 } }}
            >
                <Typography sx={{ fontWeight: 'bold', color: 'green' }}>配置选择 *</Typography>
                <TextField
                ref={configTextFieldRef}
                fullWidth
                value={getSelectedConfigName()}
                onClick={handleConfigClick}
                sx={{
                  ml: 0,
                  cursor: 'pointer',
                  '& .MuiInputBase-input': {
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }
                }}
                disabled={configsLoading}
                slotProps={{
                  input: {
                    readOnly: true,
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton edge="end">
                          <ArrowDropDown />
                        </IconButton>
                      </InputAdornment>
                    ),
                  }
                }}
                />
            </Grid>
        </Grid>

          <Button
            variant="contained"
            color="success"
            sx={{ mt: 3, ml: 0 }}
            disabled={!singleText || config === 'default' || loading}
            onClick={analyzeSingleAlert}
          >
            {loading ? '分析中...' : '分析告警'}
          </Button>
        </StyledPaper>

        {/* 单个告警分析的筛选和表格组件 */}
        <Box sx={{ mt: 4 }}>
          <StyledPaper>
            <SingleAlertPage />
          </StyledPaper>
        </Box>
      </TabPanel>

      {/* 批量告警分析 */}
      <TabPanel value={tabIndex} index={1}>
        <StyledPaper>
          <Box
            sx={{
              border: '2px dashed #8bc34a',
              borderRadius: 2,
              p: 4,
              ml: 0,
              textAlign: 'center',
              color: '#4caf50',
              cursor: 'pointer',
            }}
            onClick={() => document.getElementById('file-input')?.click()}
          >
            <Typography variant="h6">拖拽文件到此处或点击选择文件</Typography>
            <Typography variant="body2">支持格式: .txt, .log, .json, .csv</Typography>
            <input
              id="file-input"
              type="file"
              accept=".txt,.log,.json,.csv"
              style={{ display: 'none' }}
              onChange={handleFileChange}
            />
          </Box>

          <Grid container spacing={0} sx={{ mt: 2, pl: 0 }}>
            <Grid
                item
                xs={6}
                component="div"
                {...({} as GridProps)}
                sx={{ minWidth: 300 }}
            >
                <Typography sx={{ fontWeight: 'bold', color: 'green' }}>告警格式</Typography>
                <TextField
                select
                fullWidth
                value={format}
                onChange={(e) => setFormat(e.target.value)}
                sx={{ ml: 0 }}
                >
                <MenuItem value="auto">自动检测 (auto)</MenuItem>
                <MenuItem value="format1">格式1</MenuItem>
                <MenuItem value="format2">格式2</MenuItem>
                </TextField>
            </Grid>

            <Grid
                item
                xs={6}
                component="div"
                {...({} as GridProps)}
                sx={{ minWidth: 300 }}
            >
                <Typography sx={{ fontWeight: 'bold', color: 'green' }}>配置选择 *</Typography>
                <TextField
                fullWidth
                value={getSelectedConfigName()}
                onClick={handleConfigClick}
                sx={{
                  ml: 0,
                  cursor: 'pointer',
                  '& .MuiInputBase-input': {
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }
                }}
                disabled={configsLoading}
                slotProps={{
                  input: {
                    readOnly: true,
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton edge="end">
                          <ArrowDropDown />
                        </IconButton>
                      </InputAdornment>
                    ),
                  }
                }}
                />
            </Grid>
        </Grid>

          <Typography sx={{ mt: 2, fontWeight: 'bold', color: 'green' }}>任务名称</Typography>
          <TextField
            fullWidth
            placeholder="例如：2023-05-网络安全告警分析"
            value={taskName}
            onChange={(e) => setTaskName(e.target.value)}
            sx={{ ml: 0 }}
          />
          <Typography variant="body2" sx={{ mt: 1 }}>
            将自动生成任务ID: task-20230515-001
          </Typography>

          <Typography sx={{ mt: 2, fontWeight: 'bold', color: 'green' }}>文件预览</Typography>
          <TextField
            fullWidth
            multiline
            minRows={4}
            value={filePreview}
            sx={{ ml: 0 }}
          />

          <Typography sx={{ mt: 2 }}>格式检测结果：未检测</Typography>

          <Grid container spacing={0} sx={{ mt: 2, pl: 0 }}>
            <Grid item xs={12} sm={6} component={'div'} {...({} as GridProps)} sx={{ pl: 0, ml: 0, pr: { xs: 0, sm: 1 } }}>
              <Button 
                fullWidth 
                variant="outlined" 
                color="success"
                onClick={handleReset}
              >
                重置
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} component={'div'} {...({} as GridProps)} sx={{ pl: 0, ml: 0, pr: { xs: 0, sm: 1 } }}>
              <Button
                fullWidth
                variant="contained"
                color="success"
                disabled={!file || config === 'default' || batchLoading}
                onClick={analyzeBatchAlerts}
              >
                {batchLoading ? '分析中...' : '开始批量分析'}
              </Button>
            </Grid>
          </Grid>
        </StyledPaper>

        {/* 批量告警分析的筛选和表格组件 */}
        <Box sx={{ mt: 4 }}>
          <StyledPaper>
            <AdvancedFilterPage />
          </StyledPaper>
        </Box>
      </TabPanel>

      {/* 共享的配置选择Popover */}
      <Popover
        open={Boolean(configPopoverAnchor)}
        anchorEl={configPopoverAnchor}
        onClose={handleConfigClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        slotProps={{
          paper: {
            sx: {
              width: popoverWidth > 0 ? `${popoverWidth}px` : 'auto',
              maxHeight: 400,
              overflow: 'hidden',
              // 隐藏滚动条 - 应用到所有子元素
              '&::-webkit-scrollbar': {
                display: 'none'
              },
              '-ms-overflow-style': 'none',
              'scrollbar-width': 'none',
              '& *': {
                '&::-webkit-scrollbar': {
                  display: 'none'
                },
                '-ms-overflow-style': 'none',
                'scrollbar-width': 'none'
              }
            }
          }
        }}
      >
        <Box sx={{
          p: 1,
          maxHeight: 400,
          overflowY: 'auto',
          overflowX: 'hidden',
          // 隐藏滚动条 - 更强制的方式
          '&::-webkit-scrollbar': {
            width: '0px',
            background: 'transparent'
          },
          '&::-webkit-scrollbar-thumb': {
            background: 'transparent'
          },
          '-ms-overflow-style': 'none',
          'scrollbar-width': 'none',
          // 确保所有子元素也隐藏滚动条
          '& *': {
            '&::-webkit-scrollbar': {
              width: '0px',
              background: 'transparent'
            },
            '-ms-overflow-style': 'none',
            'scrollbar-width': 'none'
          }
        }}>
          <List dense>
            <ListItem disablePadding>
              <ListItemButton
                onClick={() => handleConfigSelect('default', '-- 请选择 --')}
                selected={config === 'default'}
              >
                <ListItemText primary="-- 请选择 --" />
              </ListItemButton>
            </ListItem>
            {configs.map((configItem) => (
              <ListItem key={configItem.id} disablePadding>
                <ListItemButton
                  onClick={() => handleConfigSelect(configItem.id.toString(), configItem.config_name)}
                  selected={config === configItem.id.toString()}
                >
                  <ListItemText
                    primary={configItem.config_name}
                    slotProps={{
                      primary: {
                        sx: {
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          maxWidth: '100%'
                        }
                      }
                    }}
                  />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
          {configTotalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 1 }}>
              <Pagination
                count={configTotalPages}
                page={configPage}
                onChange={handleConfigPageChange}
                size="small"
              />
            </Box>
          )}
        </Box>
      </Popover>



      {/* 分析结果模态框 */}
      <Dialog
        open={analysisResultOpen}
        onClose={() => setAnalysisResultOpen(false)}
        maxWidth="lg"
        fullWidth
        slotProps={{
          paper: {
            sx: {
              borderRadius: 2,
              maxHeight: '90vh'
            }
          }
        }}
      >
        <DialogTitle sx={{
          backgroundColor: '#f8f9fa',
          borderBottom: '1px solid #e9ecef',
          py: 2
        }}>
          <Typography variant="h5" component="div" sx={{ fontWeight: 600, color: '#2c3e50' }}>
            告警分析结果
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ p: 3, backgroundColor: '#fafafa' }}>
          {analysisResult && (
            <Box>
              {/* 配置信息 */}
              <Card sx={{ mb: 2, boxShadow: '0 2px 4px rgba(0,0,0,0.1)', border: '1px solid #e9ecef' }}>
                <CardContent sx={{ p: 2 }}>
                  <Typography variant="h6" gutterBottom sx={{ color: '#2196f3', fontWeight: 600, mb: 2 }}>
                    使用配置
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <Box sx={{ flex: 1, mr: 2 }}>
                        <Typography variant="body2" sx={{ color: '#6c757d', fontWeight: 500, mb: 0.5 }}>
                          配置名称
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {analysisResult.config_name}
                        </Typography>
                      </Box>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body2" sx={{ color: '#6c757d', fontWeight: 500, mb: 0.5 }}>
                          配置ID
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {analysisResult.config_id}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </CardContent>
              </Card>

              {/* 系统信息 */}
              <Card sx={{ mb: 2, boxShadow: '0 2px 4px rgba(0,0,0,0.1)', border: '1px solid #e9ecef' }}>
                <CardContent sx={{ p: 2 }}>
                  <Typography variant="h6" gutterBottom sx={{ color: '#2196f3', fontWeight: 600, mb: 2 }}>
                    系统信息
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <Box sx={{ flex: 1, mr: 2 }}>
                        <Typography variant="body2" sx={{ color: '#6c757d', fontWeight: 500, mb: 0.5 }}>
                          模型名称
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {analysisResult.system_info.model_name}
                        </Typography>
                      </Box>
                      <Box sx={{ flex: 1, mr: 2 }}>
                        <Typography variant="body2" sx={{ color: '#6c757d', fontWeight: 500, mb: 0.5 }}>
                          模型类型
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {analysisResult.system_info.model_type}
                        </Typography>
                      </Box>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body2" sx={{ color: '#6c757d', fontWeight: 500, mb: 0.5 }}>
                          置信度阈值
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {analysisResult.system_info.confidence_threshold}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </CardContent>
              </Card>

              {/* 告警信息 */}
              <Card sx={{ mb: 2, boxShadow: '0 2px 4px rgba(0,0,0,0.1)', border: '1px solid #e9ecef' }}>
                <CardContent sx={{ p: 2 }}>
                  <Typography variant="h6" gutterBottom sx={{ color: '#2196f3', fontWeight: 600, mb: 2 }}>
                    告警信息
                  </Typography>

                  {/* 告警内容 */}
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="body2" sx={{ color: '#6c757d', fontWeight: 500, mb: 1 }}>
                      告警内容
                    </Typography>
                    <Box sx={{
                      backgroundColor: '#f8f9fa',
                      border: '1px solid #e9ecef',
                      borderRadius: 1,
                      p: 2,
                      fontFamily: 'monospace',
                      fontSize: '0.875rem',
                      color: '#495057',
                      wordBreak: 'break-all',
                      maxHeight: '120px',
                      overflowY: 'auto'
                    }}>
                      {analysisResult.analysis_result.alert.raw_alert}
                    </Box>
                  </Box>

                  {/* 告警详细信息 - 使用统一的行布局确保对齐 */}
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {/* 第一行：告警类型和严重程度 */}
                    <Box sx={{ display: 'flex', gap: 4, alignItems: 'flex-start' }}>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body2" sx={{ color: '#6c757d', fontWeight: 500, mb: 1 }}>
                          告警类型
                        </Typography>
                        <Chip
                          label={analysisResult.analysis_result.alert.alert_type || 'UNKNOWN'}
                          size="medium"
                          sx={{
                            backgroundColor: '#e3f2fd',
                            color: '#1976d2',
                            fontWeight: 500
                          }}
                        />
                      </Box>

                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body2" sx={{ color: '#6c757d', fontWeight: 500, mb: 1 }}>
                          严重程度
                        </Typography>
                        <Chip
                          label={analysisResult.analysis_result.alert.severity || 'MEDIUM'}
                          size="medium"
                          sx={{
                            backgroundColor:
                              analysisResult.analysis_result.alert.severity === 'HIGH' ? '#ffebee' :
                              analysisResult.analysis_result.alert.severity === 'MEDIUM' ? '#fff3e0' : '#e8f5e8',
                            color:
                              analysisResult.analysis_result.alert.severity === 'HIGH' ? '#d32f2f' :
                              analysisResult.analysis_result.alert.severity === 'MEDIUM' ? '#f57c00' : '#388e3c',
                            fontWeight: 500
                          }}
                        />
                      </Box>
                    </Box>

                    {/* 第二行：来源和时间戳 */}
                    <Box sx={{ display: 'flex', gap: 4, alignItems: 'flex-start' }}>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body2" sx={{ color: '#6c757d', fontWeight: 500, mb: 0.5 }}>
                          来源
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {analysisResult.analysis_result.alert.source}
                        </Typography>
                      </Box>

                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body2" sx={{ color: '#6c757d', fontWeight: 500, mb: 0.5 }}>
                          时间戳
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {(() => {
                            try {
                              const timestamp = analysisResult.analysis_result.alert.timestamp;
                              // 尝试解析时间戳并格式化
                              const date = new Date(timestamp);
                              if (!isNaN(date.getTime())) {
                                return date.toLocaleString('zh-CN', {
                                  year: 'numeric',
                                  month: '2-digit',
                                  day: '2-digit',
                                  hour: '2-digit',
                                  minute: '2-digit',
                                  second: '2-digit',
                                  hour12: false
                                });
                              }
                              return timestamp; // 如果解析失败，返回原始值
                            } catch (error) {
                              return analysisResult.analysis_result.alert.timestamp;
                            }
                          })()}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </CardContent>
              </Card>

              {/* 分析结果 */}
              <Card sx={{ boxShadow: '0 2px 4px rgba(0,0,0,0.1)', border: '1px solid #e9ecef' }}>
                <CardContent sx={{ p: 2 }}>
                  <Typography variant="h6" gutterBottom sx={{ color: '#2196f3', fontWeight: 600, mb: 2 }}>
                    分析结果
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <Box sx={{ flex: 1, mr: 2 }}>
                        <Typography variant="body2" sx={{ color: '#6c757d', fontWeight: 500, mb: 1 }}>
                          建议操作
                        </Typography>
                        <Chip
                          label={analysisResult.analysis_result.result.action || 'KEEP'}
                          size="medium"
                          sx={{
                            backgroundColor:
                              analysisResult.analysis_result.result.action === 'SUPPRESS' ? '#fff3e0' :
                              analysisResult.analysis_result.result.action === 'ESCALATE' ? '#ffebee' : '#e8f5e8',
                            color:
                              analysisResult.analysis_result.result.action === 'SUPPRESS' ? '#f57c00' :
                              analysisResult.analysis_result.result.action === 'ESCALATE' ? '#d32f2f' : '#388e3c',
                            fontWeight: 600
                          }}
                        />
                      </Box>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body2" sx={{ color: '#6c757d', fontWeight: 500, mb: 0.5 }}>
                          置信度
                        </Typography>
                        <Typography variant="h6" sx={{
                          fontWeight: 600,
                          color: analysisResult.analysis_result.result.confidence >= 0.8 ? '#388e3c' :
                                 analysisResult.analysis_result.result.confidence >= 0.6 ? '#f57c00' : '#d32f2f'
                        }}>
                          {(analysisResult.analysis_result.result.confidence * 100).toFixed(1)}%
                        </Typography>
                      </Box>
                    </Box>
                    <Box>
                      <Typography variant="body2" sx={{ color: '#6c757d', fontWeight: 500, mb: 1 }}>
                        分析原因
                      </Typography>
                      <Box sx={{
                        backgroundColor: '#f8f9fa',
                        border: '1px solid #e9ecef',
                        borderRadius: 1,
                        p: 2,
                        color: '#495057',
                        lineHeight: 1.6
                      }}>
                        {analysisResult.analysis_result.result.reason}
                      </Box>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{
          backgroundColor: '#f8f9fa',
          borderTop: '1px solid #e9ecef',
          p: 2
        }}>
          <Button
            onClick={() => setAnalysisResultOpen(false)}
            variant="contained"
            color="primary"
            sx={{
              minWidth: 100,
              fontWeight: 500
            }}
          >
            关闭
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AlertAnalysisPage;
