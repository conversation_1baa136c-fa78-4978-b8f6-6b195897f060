import { Button, FormControl, FormHelperText, Input, InputAdornment, NativeSelect, OutlinedInput, TextField } from "@material-ui/core";


import styles from '../less/search.module.less'
import React from "react";
import { AccountCircle, SearchOutlined } from "@material-ui/icons";

interface Props { 
    setIpAddress: React.Dispatch<React.SetStateAction<string>>,
    setNodeStatus: React.Dispatch<React.SetStateAction<string>>,
    getData: Function
}

export default function Search({setIpAddress, setNodeStatus, getData}:  Props) {
    const [state, setState] = React.useState<{ node_status: string | number; node_status_name: string }>({
        node_status: '',
        node_status_name: '全部',
    });
 
    const handleChange = (event: React.ChangeEvent<{ name?: string; value: unknown }>) => {
        const name = event.target.name as keyof typeof state;
        setState({
            ...state,
            [name]: event.target.value,
        });
    };
    return (
        <div className={styles.search_card} style={{ marginBottom: 16 }}>
            <div>
                <Input
                    className={styles.search_input}
                    id="outlined-adornment-weight"
                    startAdornment={
                        <InputAdornment position="start">
                            <SearchOutlined />
                        </InputAdornment>
                    }
                    
                    placeholder="搜索节点IP，支持模糊搜索"
                />
            </div>
            <div className={styles.search_select_bar}>
                <div className={styles.search_select_bar_left}>
                    <div className={styles.search_select_bar_left_item}>
                        <div>节点状态：</div>
                        <div>
                            <FormControl className={styles.formControl}>
                                <NativeSelect
                                    value={state.node_status}
                                    onChange={handleChange}
                                    name="node_status"
                                    className={styles.selectEmpty}
                                    inputProps={{ 'aria-label': 'noide_status' }}
                                >
                                    <option value="">全部</option>
                                    <option value={"online"}>在线</option>
                                    <option value={"offline"}>离线</option>
                                </NativeSelect>
                            </FormControl>
                        </div>
                    </div>
                </div>
                <div className={styles.search_select_bar_right}>
                    <Button className={styles.search_select_reload_button}>重置</Button>
                </div>
            </div>
        </div>
    )
}