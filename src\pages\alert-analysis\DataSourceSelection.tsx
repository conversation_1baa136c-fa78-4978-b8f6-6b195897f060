import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Button,
  Paper,
  Grid,
  makeStyles,
  FormControl,
  Select,
  MenuItem,
  Breadcrumbs,
  Link,
  TextField,
  Collapse
} from '@material-ui/core';
import { Storage, ArrowForward, DateRange } from '@material-ui/icons';

const useStyles = makeStyles((theme) => ({
  root: {
    flexGrow: 1,
    padding: theme.spacing(3),
    backgroundColor: '#f5f7fa',
    minHeight: 'calc(100vh - 64px)',
  },
  header: {
    marginBottom: theme.spacing(3),
  },
  title: {
    fontWeight: 'bold',
    color: '#2c3e50',
    margin: theme.spacing(2, 0),
  },
  breadcrumbs: {
    marginBottom: theme.spacing(2),
  },
  paper: {
    padding: theme.spacing(3),
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    marginBottom: theme.spacing(3),
  },
  dataSourceContainer: {
    padding: theme.spacing(3),
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
  },
  titleWithIcon: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(3),
    '& svg': {
      marginRight: theme.spacing(1),
      color: '#137D3E',
      fontSize: 24,
    },
  },
  formLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: 500,
    marginBottom: theme.spacing(1),
  },
  selectControl: {
    width: '100%',
    marginBottom: theme.spacing(2),
    '& .MuiOutlinedInput-root': {
      borderRadius: 4,
    },
  },
  select: {
    padding: '10px 14px',
    '&:focus': {
      backgroundColor: 'transparent',
    },
  },
  buttonContainer: {
    display: 'flex',
    justifyContent: 'center',
    marginTop: theme.spacing(4),
  },
  submitButton: {
    backgroundColor: '#137D3E',
    color: 'white',
    '&:hover': {
      backgroundColor: '#0e6431',
    },
    padding: theme.spacing(1, 4),
  },
  datePickerContainer: {
    marginTop: theme.spacing(2),
  },
  datePicker: {
    width: '100%',
    marginBottom: theme.spacing(2),
  },
  dateRangeIcon: {
    color: '#137D3E',
    marginRight: theme.spacing(1),
  }
}));

const DataSourceSelection: React.FC = () => {
  const classes = useStyles();
  const navigate = useNavigate();
  const [dataSource, setDataSource] = useState<string>('');
  const [timeRange, setTimeRange] = useState<string>('');
  const [startDate, setStartDate] = useState<string>('');
  const [startTime, setStartTime] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [endTime, setEndTime] = useState<string>('');

  const handleSubmit = () => {
    // 这里可以添加数据源和时间范围的验证
    if (!dataSource) {
      alert('请选择数据源');
      return;
    }
    
    if (!timeRange) {
      alert('请选择时间范围');
      return;
    }
    
    if (timeRange === 'custom' && (!startDate || !startTime || !endDate || !endTime)) {
      alert('请选择完整的开始时间和结束时间');
      return;
    }
    
    // 可以在这里保存选择的数据源和时间范围，例如存储到localStorage
    localStorage.setItem('selectedDataSource', dataSource);
    localStorage.setItem('selectedTimeRange', timeRange);
    
    // 如果选择了自定义时间范围，则存储开始时间和结束时间
    if (timeRange === 'custom') {
      localStorage.setItem('customStartDate', `${startDate}T${startTime}:00`);
      localStorage.setItem('customEndDate', `${endDate}T${endTime}:00`);
    }
    
    // 跳转到告警分析任务页面
    navigate('/noiseAnalysisTask');
  };

  // 获取当前日期，格式为YYYY-MM-DD
  const getCurrentDate = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // 获取当前时间，格式为HH:MM
  const getCurrentTime = () => {
    const now = new Date();
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    return `${hours}:${minutes}`;
  };

  // 当选择自定义时间范围时，初始化日期和时间
  React.useEffect(() => {
    if (timeRange === 'custom' && !startDate) {
      setStartDate(getCurrentDate());
      setStartTime(getCurrentTime());
      setEndDate(getCurrentDate());
      setEndTime(getCurrentTime());
    }
  }, [timeRange]);

  return (
    <div className={classes.root}>
      <Container maxWidth="lg">
        <div className={classes.header}>
          <Typography variant="h4" className={classes.title}>
            数据源选择
          </Typography>
          <Breadcrumbs aria-label="breadcrumb" className={classes.breadcrumbs}>
            <Link color="inherit" href="/noiseModelEngine">
              智能降噪中心
            </Link>
            <Typography color="textPrimary">数据源选择</Typography>
          </Breadcrumbs>
        </div>

        <Paper className={classes.dataSourceContainer}>
          <Box className={classes.titleWithIcon}>
            <Storage />
            <Typography variant="h6">数据源选择</Typography>
          </Box>

          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Typography className={classes.formLabel}>告警数据来源</Typography>
              <FormControl variant="outlined" className={classes.selectControl}>
                <Select
                  value={dataSource}
                  onChange={(e) => setDataSource(e.target.value as string)}
                  displayEmpty
                  className={classes.select}
                >
                  <MenuItem value="" disabled>
                    请选择数据源
                  </MenuItem>
                  <MenuItem value="SIEM系统">SIEM系统</MenuItem>
                  <MenuItem value="CloudWatch">CloudWatch</MenuItem>
                  <MenuItem value="Grafana">Grafana</MenuItem>
                  <MenuItem value="Kibana">Kibana</MenuItem>
                  <MenuItem value="文件上传">文件上传</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography className={classes.formLabel}>时间范围</Typography>
              <FormControl variant="outlined" className={classes.selectControl}>
                <Select
                  value={timeRange}
                  onChange={(e) => setTimeRange(e.target.value as string)}
                  displayEmpty
                  className={classes.select}
                >
                  <MenuItem value="" disabled>
                    选择时间范围
                  </MenuItem>
                  <MenuItem value="last_hour">最近一小时</MenuItem>
                  <MenuItem value="last_day">最近24小时</MenuItem>
                  <MenuItem value="last_week">最近一周</MenuItem>
                  <MenuItem value="last_month">最近一个月</MenuItem>
                  <MenuItem value="custom">自定义时间范围</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          {/* 自定义时间选择器 */}
          <Collapse in={timeRange === 'custom'}>
            <Box className={classes.datePickerContainer}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" style={{ display: 'flex', alignItems: 'center' }}>
                    <DateRange className={classes.dateRangeIcon} />
                    自定义时间范围
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography className={classes.formLabel}>开始日期</Typography>
                  <TextField
                    type="date"
                    variant="outlined"
                    fullWidth
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    InputLabelProps={{
                      shrink: true,
                    }}
                    className={classes.datePicker}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography className={classes.formLabel}>开始时间</Typography>
                  <TextField
                    type="time"
                    variant="outlined"
                    fullWidth
                    value={startTime}
                    onChange={(e) => setStartTime(e.target.value)}
                    InputLabelProps={{
                      shrink: true,
                    }}
                    className={classes.datePicker}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography className={classes.formLabel}>结束日期</Typography>
                  <TextField
                    type="date"
                    variant="outlined"
                    fullWidth
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    InputLabelProps={{
                      shrink: true,
                    }}
                    className={classes.datePicker}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography className={classes.formLabel}>结束时间</Typography>
                  <TextField
                    type="time"
                    variant="outlined"
                    fullWidth
                    value={endTime}
                    onChange={(e) => setEndTime(e.target.value)}
                    InputLabelProps={{
                      shrink: true,
                    }}
                    className={classes.datePicker}
                  />
                </Grid>
              </Grid>
            </Box>
          </Collapse>

          <Box className={classes.buttonContainer}>
            <Button
              variant="contained"
              className={classes.submitButton}
              onClick={handleSubmit}
              endIcon={<ArrowForward />}
              disabled={!dataSource || !timeRange || (timeRange === 'custom' && (!startDate || !startTime || !endDate || !endTime))}
            >
              确定
            </Button>
          </Box>
        </Paper>
      </Container>
    </div>
  );
};

export default DataSourceSelection; 