import React, { useEffect, useRef, useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, createStyles, createTheme, Fab, FormControl, Input, InputAdornment, InputLabel, Link, makeStyles, MenuItem, Select, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, Theme, ThemeProvider, Typography, withStyles } from '@material-ui/core';
import Paper from '@material-ui/core/Paper';
import CheckCircleOutlineIcon from '@material-ui/icons/CheckCircleOutline';
import ErrorOutlineIcon from '@material-ui/icons/ErrorOutline';
import { SearchOutlined } from '@material-ui/icons';
import { Pagination } from '@material-ui/lab';
import Detail from './detail';
import { use } from 'echarts';
import apiClient from '../apis/apiClient';
const StyledTableCell = withStyles((theme) => ({
    head: {
      backgroundColor: '#babcbb',
      color: "white",
      textAlign: 'center',
    },
    body: {
      fontSize: 14,
      textAlign: 'center',
    },
  }))(TableCell)
  
const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    root: {
      display: 'flex',
      flexWrap: 'wrap',
      gap: '20px', 
    },
    item: {
      flex: '1 1 200px', 
      height: theme.spacing(13),
      margin:"20px",
      backgroundColor: "#F9FAFC",
    },
  }),
);

const StyledTableRow = withStyles(() => ({
    root: {
      height: 52,
      '&:nth-of-type(odd)': {
        backgroundColor: '#f9f9f9',
      },
    },
  }))(TableRow);


const mockData = [
    {
        id: 1,
        alert_id: "123456",
        task_id: 1,
        timestamp: "2022-01-01T00:00:00",
        severity: "HIGH",
        source: "honeypot",
        alert_type: "网络攻击",
        title: "SSH登录尝试",
        description: "主机*************的SSH登录尝试",
        raw_alert: "SSH登录尝试",
        source_ip: "*************",
        destination_ip: "***********",
        source_port: 22,
        destination_port: 62001,
        protocol: "TCP",
        hostname: "主机*************",
        username: "root",
        process_name: "sshd",
        command_line: "sshd: root [priv]",
        file_path: "/var/log/auth.log",
        file_hash: "1234567890",
        geo_city: "北京市",
        geo_latitude: 39.90469,
        geo_longitude: 116.40717,
        threat_level: "CRITICAL",
        threat_type: "网络攻击",
        threat_score: 90,
        attack_category: "入侵",
        mitre_tactics: ["TA0001"],
        mitre_techniques: ["T1071"],
        cwe_id: "CWE-732",
        extra_metadata: {},
        created_at: "2022-01-01T00:00:00",
    },
    {
        id: 2,
        alert_id: "123457",
        task_id: 1,
        timestamp: "2022-01-01T00:00:00",
        severity: "MEDIUM",
        source: "honeypot",
        alert_type: "网络攻击",
        title: "SSH登录尝试",
        description: "主机*************的SSH登录尝试",
        raw_alert: "SSH登录尝试",
        source_ip: "*************",
        destination_ip: "***********",  
        source_port: 22,
        destination_port: 62001,
        protocol: "TCP",
        hostname: "主机*************",
        username: "root",
        process_name: "sshd",
        command_line: "sshd: root [priv]",
        file_path: "/var/log/auth.log",
        file_hash: "1234567890",
        geo_city: "北京市",
        geo_latitude: 39.90469,
        geo_longitude: 116.40717,
        threat_level: "HIGH",
        threat_type: "网络攻击",
        threat_score: 80,
        attack_category: "入侵",
        mitre_tactics: ["TA0001"],
        mitre_techniques: ["T1071"],
        cwe_id: "CWE-732",
        extra_metadata: {},
        created_at: "2022-01-01T00:00:00",
    },
    {
        id: 3,
        alert_id: "123458",
        task_id: 1,
        timestamp: "2022-01-01T00:00:00",
        severity: "LOW",
        source: "honeypot",
        alert_type: "网络攻击",
        title: "SSH登录尝试",
        description: "主机*************的SSH登录尝试",
        raw_alert: "SSH登录尝试",
        source_ip: "*************",
        destination_ip: "***********",
        source_port: 22,
        destination_port: 62001,
        protocol: "TCP",
        hostname: "主机*************",
        username: "root",  
        process_name: "sshd",
        command_line: "sshd: root [priv]",
        file_path: "/var/log/auth.log",
        file_hash: "1234567890",
        geo_city: "北京市",
        geo_latitude: 39.90469,
        geo_longitude: 116.40717,
        threat_level: "MEDIUM",
        threat_type: "网络攻击",
        threat_score: 70,
        attack_category: "入侵",
        mitre_tactics: ["TA0001"],
        mitre_techniques: ["T1071"],
        cwe_id: "CWE-732",
        extra_metadata: {},
        created_at: "2022-01-01T00:00:00",
    },
]
export default function AlertMonitor() {
  const classes = useStyles();
  const [severity, setSeverity] = useState('ALL');
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [data, setData] = useState();
  const [hostname, setHostname] = useState("");
  const [title, setTitle] = useState("");
  const fetchalertlist = async () => {
    const response = await apiClient.get('/api/alert_monitor/all_alert',{
      params: {
        page: page+1,
        per_page: 10,
        severity: severity,
        hostname: hostname,
        title: title,
      },
    });
    console.log(response.data.items );
    setData(response.data);
    setTotal(response.data.pagination.total);
  }
  useEffect(() => {
    fetchalertlist();
  }, [severity])
  useEffect(() => {
    fetchalertlist();
  }, [page, pageSize]);
  return (
    <div style={{backgroundColor: "white",width: "100%",minHeight: "100vh"}}>
      <div style={{paddingTop:"20px",paddingLeft: "20px",display: "flex",justifyContent: "space-between"}}>
          <div>
            <Select displayEmpty value={severity}  onChange={(event) => (setSeverity(event.target.value),setPage(0))} color="primary" variant ="outlined" style={{height: 40,width: 150,marginRight: 20}}>
                <MenuItem value={"ALL"}>全部严重级</MenuItem>
                <MenuItem value={"HIGH"}>严重</MenuItem>
                <MenuItem value={"MEDIUM"}>警告</MenuItem>
                <MenuItem value={"LOW"}>提示</MenuItem>
                <MenuItem value={"INFO"}>信息</MenuItem>
            </Select>
            <TextField size="small" variant="outlined" placeholder="请输入主机名称" style={{marginRight: 20,width: 200}} onChange={(event) => setHostname(event.target.value)}/>
            <TextField size="small" variant="outlined" placeholder="请输入告警名称" style={{marginRight: 20,width: 200}} onChange={(event) => setTitle(event.target.value)}/>
            <Button color='primary' variant='contained'style={{marginRight: 20,height: 40,width: 80}} onClick={() => { setPage(0),fetchalertlist()}}>搜索</Button>
          </div>
      </div>
      <div className={classes.root}>
            <Paper elevation={1} className={classes.item}>
                <div style={{color: "#929292",padding:"0 15px",fontSize:"18px",marginTop: "20px"}}>总告警数</div>
                <div style={{color: "#5D9CEC",paddingLeft:"15px",fontSize:"35px",fontWeight:"bold"}}>{total}</div>
            </Paper>
            <Paper elevation={1} className={classes.item}>
                <div style={{color: "#929292",padding:"0 15px",fontSize:"18px",marginTop: "20px"}}>有效告警</div>
                <div style={{color: "#FF7E7E",paddingLeft:"15px",fontSize:"35px",fontWeight:"bold"}}>214</div>
            </Paper>
            <Paper elevation={1} className={classes.item}>
                <div style={{color: "#929292",padding:"0 15px",fontSize:"18px",marginTop: "20px"}}>降噪率</div>
                <div style={{color: "#3FC379",paddingLeft:"15px",fontSize:"35px",fontWeight:"bold"}}>86.2%</div>
            </Paper>
      </div>
      <div style={{margin: "0 20px"}}>
            <TableContainer component={Paper} >
                <Table aria-label="simple table">
                  <TableHead>
                    <TableRow>
                       <StyledTableCell>ID</StyledTableCell>
                       <StyledTableCell>告警ID</StyledTableCell>
                       <StyledTableCell>告警名称</StyledTableCell>
                       <StyledTableCell>严重级</StyledTableCell>
                       <StyledTableCell>主机</StyledTableCell>
                       <StyledTableCell>区域</StyledTableCell>
                       <StyledTableCell>状态</StyledTableCell>
                       <StyledTableCell>时间戳</StyledTableCell>
                       <StyledTableCell>操作</StyledTableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                       {data?.items?.map((row: any,index: number) => (
                            <StyledTableRow key={row.id}>
                            <TableCell align="center">{index+1+pageSize*(page)}</TableCell>
                            <TableCell align="center">{row.alert_id}</TableCell>
                            <TableCell align="center">
                               <span style={{width: 60,height: 30,display: 'inline-flex',justifyContent: 'center',alignItems: 'center'}}>
                                 {row.title}
                               </span>
                            </TableCell>
                            <TableCell align="center" >
                               <span  style={{width: 60,height: 30,display: 'inline-flex',justifyContent: 'center',alignItems: 'center',borderRadius: 15,padding: "0 5px",
                                backgroundColor: row.severity === "HIGH"?"#FFCDD2": row.severity === "MEDIUM"?"#FFE0B2": row.severity === "LOW"?"#C8E6C9": row.severity === "INFO"?"#BBDEFB":"",
                                color: row.severity === "HIGH"?"#C62828": row.severity === "MEDIUM"?"#E65100": row.severity === "LOW"?"#2E7D32": row.severity === "INFO"?"#1565C0":"",}}>
                                {row.severity === "HIGH"?"严重": row.severity === "MEDIUM"?"警告": row.severity === "LOW"?"提示": row.severity === "INFO"?"信息":""}
                               </span>
                            </TableCell>
                            <TableCell align="center">{row.hostname}</TableCell>
                            <TableCell align="center">{row.geo_country}_{row.geo_city}</TableCell>
                            <TableCell align="center">
                              <span style={{width: 60,height: 30,display: 'inline-flex',justifyContent: 'center',alignItems: 'center',borderRadius: 15,padding: "0 5px",
                                backgroundColor:row.status === 1?"#BBDEFB":row.status === 2?"#C8E6C9":"",
                                color:row.status === 1?"#1565C0":row.status === 2?"#2E7D32":"",}}>
                                {row.status === 1?"新告警":row.status === 2?"已聚合":""}
                              </span>
                            </TableCell>
                            <TableCell align="center">{row.timestamp?.replace('T', ' ')}</TableCell>
                            <TableCell align="center">
                              <Detail data={row}/>
                              <Button>标记为噪声</Button>
                            </TableCell>
                          </StyledTableRow>))
                       }
                  </TableBody>
                </Table>
            </TableContainer>
            <div className="attack-list-pagination-div" style={{marginBottom:"20px"}}>
                  <div>共 {total} 条数据</div>
                       <Pagination
                         className="attack-list-pagination"
                         showFirstButton
                         count={Math.ceil(total/ pageSize)}
                         page={page + 1}
                         onChange={(event, value) => (setPage(value - 1),console.log(page))}
                       />
                  </div>
        </div>
     </div>
)
}