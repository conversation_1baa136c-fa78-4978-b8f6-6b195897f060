// ConfigPanel.js
import React from 'react';
import { makeStyles, Paper, Box, Typography, TextField, Select, MenuItem, InputLabel, FormControl, Switch, FormControlLabel, Button } from '@material-ui/core';
import LabelIcon from '@material-ui/icons/Label';
import FilterListIcon from '@material-ui/icons/FilterList';
import DeviceHubIcon from '@material-ui/icons/DeviceHub';
import CodeIcon from '@material-ui/icons/Code';
import PowerSettingsNewIcon from '@material-ui/icons/PowerSettingsNew';
import DescriptionIcon from '@material-ui/icons/Description';
import SaveIcon from '@material-ui/icons/Save';
import AutorenewIcon from '@material-ui/icons/Autorenew';
import CloseIcon from '@material-ui/icons/Close';


export default function ConfigPanel() {
    const classes = useStyles();
    const [enabled, setEnabled] = React.useState(true);
    return (
        <Paper className={classes.root}>
            <Box className={classes.header}>
                <DeviceHubIcon />
                <Typography className={classes.h3}>降噪规则配置</Typography>
            </Box>
            <Box className={classes.content}>
                <form>
                    <Box className={classes.formGrid}>
                        <Box className={classes.formGroup}>
                            <div className={classes.label}><LabelIcon fontSize="small" /> 规则名称</div>
                            <TextField fullWidth variant="outlined" defaultValue="排除备份系统告警" />
                        </Box>
                        <Box className={classes.formGroup}>
                            <div className={classes.label}><FilterListIcon fontSize="small" /> 规则类型</div>
                            <FormControl variant="outlined" fullWidth>
                                <Select defaultValue="白名单规则">
                                    <MenuItem value="误报模式识别">误报模式识别</MenuItem>
                                    <MenuItem value="白名单规则">白名单规则</MenuItem>
                                    <MenuItem value="时间序列规则">时间序列规则</MenuItem>
                                    <MenuItem value="模型生成规则">模型生成规则</MenuItem>
                                </Select>
                            </FormControl>
                        </Box>
                        <Box className={classes.formGroup}>
                            <div className={classes.label}><DeviceHubIcon fontSize="small" /> 应用范围</div>
                            <FormControl variant="outlined" fullWidth>
                                <Select multiple defaultValue={['核心防火墙', 'IDS系统']} renderValue={(selected: unknown) => {
                                    // 类型判断或直接断言（假设数据源可控）
                                    if (Array.isArray(selected)) {
                                        return selected.join(', ');
                                    }
                                    return '';
                                }}>
                                    <MenuItem value="核心防火墙">核心防火墙</MenuItem>
                                    <MenuItem value="IDS系统">IDS系统</MenuItem>
                                    <MenuItem value="数据库集群">数据库集群</MenuItem>
                                    <MenuItem value="应用服务器">应用服务器</MenuItem>
                                    <MenuItem value="网络设备">网络设备</MenuItem>
                                </Select>
                            </FormControl>
                        </Box>
                        <Box className={classes.formGroup}>
                            <div className={classes.label}><CodeIcon fontSize="small" /> 条件表达式</div>
                            <TextField
                                fullWidth
                                variant="outlined"
                                multiline
                                rows={5}
                                defaultValue={`(source_ip IN ["*************", "*************"]) AND (log_type == "BACKUP_ALERT")`}
                            />
                        </Box>
                        <Box className={classes.formGroup}>
                            <div className={classes.label}><PowerSettingsNewIcon fontSize="small" /> 规则状态</div>
                            <FormControlLabel
                                control={
                                    <Switch checked={enabled} onChange={() => setEnabled(!enabled)} color="primary" />
                                }
                                label="规则已启用"
                            />
                        </Box>
                        <Box className={classes.formGroup}>
                            <div className={classes.label}><DescriptionIcon fontSize="small" /> 规则描述</div>
                            <TextField
                                fullWidth
                                variant="outlined"
                                multiline
                                rows={3}
                                defaultValue="排除备份系统在计划任务执行期间产生的常规告警"
                            />
                        </Box>
                    </Box>
                    <Box className={classes.actions}>
                        <Button
                            variant="contained"
                            color="primary"
                            className={classes.btnLarge}
                            startIcon={<SaveIcon />}
                            disableElevation
                        >保存规则</Button>
                        <Button
                            variant="contained"
                            className={classes.btnLarge}
                            startIcon={<AutorenewIcon />}
                        >测试规则</Button>
                        <Button
                            variant="contained"
                            className={classes.btnLarge}
                            startIcon={<CloseIcon />}
                        >取消</Button>
                    </Box>
                </form>
            </Box>
        </Paper>
    );
}

const useStyles = makeStyles((theme) => ({
    root: {
        background: '#fff',
        borderRadius: 12,
        overflow: 'hidden',
        boxShadow: '0 6px 15px rgba(0,0,0,0.08)',
        marginTop: 20,
        borderTop: '4px solid #2E7D32',
    },
    header: {
        padding: '20px 30px',
        background: 'linear-gradient(to right, rgba(46,125,50,0.1), rgba(76,175,80,0.05))',
        display: 'flex',
        alignItems: 'center',
        gap: 15,
        borderBottom: '1px solid #e0e0e0',
    },
    h3: {
        fontSize: '1.6rem',
        color: '#1B5E20',
    },
    content: {
        padding: 30,
    },
    formGrid: {
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: 25,
        [theme.breakpoints.down('sm')]: {
            gridTemplateColumns: '1fr',
        },
    },
    formGroup: {
        marginBottom: 25,
    },
    label: {
        display: 'flex',
        alignItems: 'center',
        gap: 8,
        fontWeight: 600,
        color: '#1B5E20',
        marginBottom: 12,
    },
    actions: {
        display: 'flex',
        gap: 15,
        marginTop: 20,
    },
    btnLarge: {
        padding: '16px 25px',
        fontSize: '1.1rem',
    },
}));
