import React, { useRef, useState } from 'react';
import {
  Paper,
  Typography,
  Button,
  TextField,
  Box,
  Stack,
} from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import SettingsIcon from '@mui/icons-material/Settings';
import DeleteSweepIcon from '@mui/icons-material/DeleteSweep';

const LogInputPanel: React.FC = () => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);

  // ✅ 处理文件上传
  const handleFiles = (files: FileList | null) => {
    if (!files) return;

    const acceptedExtensions = ['.log', '.txt', '.csv'];

    const validFiles = Array.from(files).filter((file) => {
      const ext = file.name.slice(file.name.lastIndexOf('.')).toLowerCase();
      return acceptedExtensions.includes(ext);
    });

    if (validFiles.length > 0) {
      console.log('上传的文件：', validFiles);
      // TODO: 你可以在这里处理上传逻辑，比如调用 API
    } else {
      alert('仅支持 .log, .txt, .csv 文件');
    }
  };

  // ✅ 拖拽事件处理
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = () => {
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFiles(e.dataTransfer.files);
  };

  // ✅ 点击上传
  const handleClick = () => {
    inputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFiles(e.target.files);
    e.target.value = ''; // 清空 input，允许重复上传同一文件
  };

  return (
    <Paper
    elevation={2}
    sx={{
      p: 2,
      height: '100%', // ✅ 关键：撑满父容器
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
    }}
  >
      {/* 标题 */}
      <Typography variant="h6" sx={{ color: '#065f46', fontWeight: 'bold' }}>
        日志输入
      </Typography>

      {/* 绿色下划线 */}
      <Box
        sx={{
          height: '2px',
          backgroundColor: '#bbf7d0',
          width: '100%',
          my: 1.5,
          borderRadius: 1,
        }}
      />

      {/* 拖拽上传区域 */}
      <Box
        onClick={handleClick}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        sx={{
          border: '2px dashed #4caf50',
          borderRadius: 2,
          backgroundColor: isDragOver ? '#e8f5e9' : '#f5f5f5',
          textAlign: 'center',
          p: 4,
          cursor: 'pointer',
          transition: '0.3s',
          '&:hover': {
            backgroundColor: '#e8f5e9',
          },
        }}
      >
        <input
          type="file"
          accept=".log,.txt,.csv"
          multiple
          hidden
          ref={inputRef}
          onChange={handleFileChange}
        />
        <CloudUploadIcon sx={{ fontSize: 48, color: '#4caf50', mb: 1 }} />
        <Typography variant="body1" sx={{ fontWeight: 500, mb: 1 }}>
          点击或拖放日志文件到此处
        </Typography>
        <Typography variant="caption" color="textSecondary">
          支持 .log、.txt、.csv 格式
        </Typography>
      </Box>

      {/* 手动输入框 */}
      <TextField
        label="或手动输入日志内容"
        multiline
        rows={10}
        fullWidth
        margin="normal"
        variant="outlined"
      />

      {/* 操作按钮 */}
      <Stack direction="row" spacing={2} sx={{ paddingTop: 1}}>
        {/* 智能解析按钮 */}
        <Button
          variant="contained"
          color="success"
          startIcon={<SettingsIcon sx={{ color: 'white' }} />}
          sx={{ mt: 1, flex: 2, fontWeight: 'bold' }}
        >
          智能解析日志
        </Button>

        {/* 清除按钮 */}
        <Button
          variant="contained"
          startIcon={<DeleteSweepIcon sx={{ color: 'white' }} />}
          sx={{
            mt: 1,
            flex: 1,
            backgroundColor: '#475569',
            color: '#ffffff',
            fontWeight: 'bold',
            '&:hover': {
              backgroundColor: '#334155',
            },
          }}
        >
          清除
        </Button>
      </Stack>
    </Paper>
  );
};

export default LogInputPanel;
