import React, { useState, useEffect } from "react";
import {
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  TextField,
  Button,
  makeStyles,
} from "@material-ui/core";
import AddIcon from "@material-ui/icons/Add";
import EditIcon from "@material-ui/icons/Edit";
import DeleteIcon from "@material-ui/icons/Delete";
import SearchIcon from "@material-ui/icons/Search";

const useStyles = makeStyles((theme) => ({
  root: {
    flexGrow: 1,
    width: "100%",
    padding: theme.spacing(3),
    margin: "0 auto",
    maxWidth: "100%",
  },
  title: {
    marginBottom: theme.spacing(3),
  },
  paper: {
    width: "100%",
    marginBottom: theme.spacing(2),
    overflowX: "auto",
  },
  table: {
    minWidth: "85vw",
    width: "100%",
  },
  searchContainer: {
    display: "flex",
    alignItems: "center",
    marginBottom: theme.spacing(2),
  },
  searchInput: {
    marginRight: theme.spacing(2),
    width: 300,
  },
  addButton: {
    marginLeft: "auto",
  },
  statusActive: {
    color: theme.palette.success.main,
  },
  statusInactive: {
    color: theme.palette.error.main,
  },
}));

interface HoneypotData {
  id: number;
  name: string;
  type: string;
  location: string;
  status: string;
  visitCount: number;
}

const HoneypotManagement: React.FC = () => {
  const classes = useStyles();
  const [honeypots, setHoneypots] = useState<HoneypotData[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [searchTerm, setSearchTerm] = useState("");

  // 模拟数据 - 实际项目中应该从API获取
  useEffect(() => {
    const mockHoneypots = [
      {
        id: 1,
        name: "管理系统",
        type: "admin",
        location: "总机房",
        status: "active",
        visitCount: 0,
      },
      {
        id: 2,
        name: "ssh-1",
        type: "SSH",
        location: "管理业务系统2",
        status: "active",
        visitCount: 280,
      },
      {
        id: 3,
        name: "ftp-3",
        type: "FTP",
        location: "分类分级业务系统",
        status: "inactive",
        visitCount: 130,
      },
      {
        id: 4,
        name: "pop-0",
        type: "邮件",
        location: "内部邮件系统",
        status: "inactive",
        visitCount: 20,
      },
    ];
    setHoneypots(mockHoneypots);
  }, []);

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  const handleAdd = () => {
    // 实现添加蜜罐的功能
    console.log("Add honeypot");
  };

  const handleEdit = (id: number) => {
    // 实现编辑蜜罐的功能
    console.log("Edit honeypot:", id);
  };

  const handleDelete = (id: number) => {
    // 实现删除蜜罐的功能
    console.log("Delete honeypot:", id);
  };

  const filteredHoneypots = honeypots.filter((honeypot) =>
    Object.values(honeypot).some((value) =>
      value.toString().toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  return (
    <div className={classes.root}>
      <Typography variant="h4" className={classes.title}>
        蜜罐管理
      </Typography>

      <div className={classes.searchContainer}>
        <TextField
          className={classes.searchInput}
          variant="outlined"
          size="small"
          placeholder="搜索蜜罐..."
          value={searchTerm}
          onChange={handleSearch}
          InputProps={{
            startAdornment: <SearchIcon />,
          }}
        />
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          className={classes.addButton}
          onClick={handleAdd}
        >
          添加蜜罐
        </Button>
      </div>

      <Paper className={classes.paper}>
        <TableContainer>
          <Table className={classes.table}>
            <TableHead>
              <TableRow>
                <TableCell>名称</TableCell>
                <TableCell>类型</TableCell>
                <TableCell>位置</TableCell>
                <TableCell>状态</TableCell>
                <TableCell align="right">访问次数</TableCell>
                <TableCell align="right">操作</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredHoneypots
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((honeypot) => (
                  <TableRow key={honeypot.id}>
                    <TableCell>{honeypot.name}</TableCell>
                    <TableCell>{honeypot.type}</TableCell>
                    <TableCell>{honeypot.location}</TableCell>
                    <TableCell>
                      <span
                        className={
                          honeypot.status === "active"
                            ? classes.statusActive
                            : classes.statusInactive
                        }
                      >
                        {honeypot.status === "active" ? "活跃" : "停用"}
                      </span>
                    </TableCell>
                    <TableCell align="right">{honeypot.visitCount}</TableCell>
                    <TableCell align="right">
                      <IconButton
                        size="small"
                        onClick={() => handleEdit(honeypot.id)}
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleDelete(honeypot.id)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredHoneypots.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>
    </div>
  );
};

export default HoneypotManagement;
