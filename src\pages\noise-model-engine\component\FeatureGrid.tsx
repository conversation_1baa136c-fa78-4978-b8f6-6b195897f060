// FeatureGrid.js
import React from 'react';
import { makeStyles, Grid, Paper, Box, Typography, Button } from '@material-ui/core';
import BrainIcon from '@material-ui/icons/EmojiObjects';
import ListAltIcon from '@material-ui/icons/ListAlt';
import TimelineIcon from '@material-ui/icons/Timeline';
import AndroidIcon from '@material-ui/icons/Android';
import BarChartIcon from '@material-ui/icons/BarChart';
import AddCircleIcon from '@material-ui/icons/AddCircle';
import FileCopyIcon from '@material-ui/icons/FileCopy';
import HistoryIcon from '@material-ui/icons/History';
import AutorenewIcon from '@material-ui/icons/Autorenew';
import MagicIcon from '@material-ui/icons/Stars';
import LightbulbIcon from '@material-ui/icons/WbIncandescent';



const features = [
  {
    icon: <BrainIcon style={{ fontSize: 32 }} />,
    title: '误报模式识别',
    status: '已启用',
    desc: '基于历史数据学习典型误报特征，建立动态的误报模式库。系统能够自动识别重复出现的误报模式，减少相同类型的误报再次出现。',
    stats: [
      ['已识别模式', '38个'],
      ['本月误报减少率', '74.5%'],
      ['最近更新', '2023-10-15'],
    ],
    actions: [
      { text: '查看报告', icon: <BarChartIcon />, primary: true },
      { text: '配置', icon: <AutorenewIcon />, primary: false },
    ],
  },
  {
    icon: <ListAltIcon style={{ fontSize: 32 }} />,
    title: '白名单管理',
    status: '已启用',
    desc: '维护可信IP/设备/行为白名单库，支持对可信实体和行为的配置与管理。系统自动忽略白名单内的告警，显著降低监控噪音。',
    stats: [
      ['可信IP数量', '142个'],
      ['可信设备数量', '87个'],
      ['可信行为规则', '12条'],
    ],
    actions: [
      { text: '添加条目', icon: <AddCircleIcon />, primary: true },
      { text: '导出', icon: <FileCopyIcon />, primary: false },
    ],
  },
  {
    icon: <TimelineIcon style={{ fontSize: 32 }} />,
    title: '时间序列分析',
    status: '已启用',
    desc: '识别周期性干扰（如备份系统每小时扫描）引起的告警干扰。系统自动学习正常运营模式，在预期时间内忽略计划内活动产生的告警。',
    stats: [
      ['周期模式已识别', '5种'],
      ['周期性告警减少率', '92.3%'],
      ['系统置信度', '98.7%'],
    ],
    actions: [
      { text: '时间线', icon: <HistoryIcon />, primary: true },
      { text: '重新学习', icon: <AutorenewIcon />, primary: false },
    ],
  },
  {
    icon: <AndroidIcon style={{ fontSize: 32 }} />,
    title: '大模型辅助规则生成',
    status: '测试中',
    desc: '利用大型语言模型分析告警上下文，辅助生成屏蔽与降噪规则。系统基于自然语言描述自动生成复杂过滤规则，大幅减少手动配置时间。',
    stats: [
      ['已生成规则', '24条'],
      ['规则有效度', '89.2%'],
      ['配置时间节省', '65%'],
    ],
    actions: [
      { text: '生成规则', icon: <MagicIcon />, primary: true },
      { text: '示例', icon: <LightbulbIcon />, primary: false },
    ],
  },
];

export default function FeatureGrid() {
  const classes = useStyles();
  return (
    <Grid container spacing={3} className={classes.grid}>
      {features.map((f, idx) => (
        <Grid item xs={12} sm={6} md={3} key={f.title}>
          <Paper className={classes.card}>
            <Box className={classes.header}>
              <Box className={classes.icon}>{f.icon}</Box>
              <Box className={classes.title}>
                <Typography className={classes.h3}>{f.title}</Typography>
                <span className={classes.status}>{f.status}</span>
              </Box>
            </Box>
            <Box className={classes.content}>
              <Typography style={{ color: '#666', marginBottom: 15, lineHeight: 1.7 }}>{f.desc}</Typography>
              <Box className={classes.stats}>
                {f.stats.map(([k, v]) => (
                  <p key={k}><span>{k}</span><strong>{v}</strong></p>
                ))}
              </Box>
            </Box>
            <Box className={classes.actions}>
              {f.actions.map((a, i) =>
                <Button
                  key={a.text}
                  className={a.primary ? classes.btnPrimary : classes.btnSecondary}
                  startIcon={a.icon}
                  disableElevation
                  size="medium"
                >{a.text}</Button>
              )}
            </Box>
          </Paper>
        </Grid>
      ))}
    </Grid>
  );
}


const useStyles = makeStyles((theme) => ({
  grid: {
    marginBottom: 35,
  },
  card: {
    background: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
    boxShadow: '0 6px 15px rgba(0,0,0,0.08)',
    borderTop: '4px solid #2E7D32',
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    transition: 'all 0.4s',
    '&:hover': {
      transform: 'translateY(-8px)',
      boxShadow: '0 12px 25px rgba(0,0,0,0.15)',
    },
  },
  header: {
    padding: 20,
    background: 'linear-gradient(to right, rgba(46,125,50,0.1), rgba(76,175,80,0.05))',
    display: 'flex',
    alignItems: 'center',
    gap: 15,
    borderBottom: '1px solid #e0e0e0',
  },
  icon: {
    width: 60,
    height: 60,
    borderRadius: 12,
    background: 'linear-gradient(45deg, #2E7D32, #8BC34A)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#fff',
    fontSize: 28,
  },
  title: {
    flexGrow: 1,
  },
  h3: {
    fontSize: '1.4rem',
    color: '#1B5E20',
    marginBottom: 5,
  },
  status: {
    fontSize: '0.85rem',
    fontWeight: 500,
    background: 'rgba(139,195,74,0.2)',
    color: '#4CAF50',
    padding: '3px 10px',
    borderRadius: 30,
    display: 'inline-block',
  },
  content: {
    padding: 20,
    flexGrow: 1,
  },
  stats: {
    background: '#f9fbf9',
    padding: '15px 20px',
    borderRadius: 8,
    marginTop: 15,
    '& p': {
      display: 'flex',
      justifyContent: 'space-between',
      marginBottom: 10,
      fontSize: '0.95rem',
    },
    '& strong': {
      color: '#1B5E20',
    },
  },
  actions: {
    display: 'flex',
    gap: 12,
    padding: '0 20px 20px',
  },
  btnPrimary: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    border: 'none',
    fontWeight: 500,
    cursor: 'pointer',
    background: 'linear-gradient(to right, #2E7D32, #4CAF50)',
    color: '#fff',
    boxShadow: '0 4px 10px rgba(76,175,80,0.4)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    '&:hover': {
      background: 'linear-gradient(to right, #1b5e20, #388e3c)',
      boxShadow: '0 6px 15px rgba(76,175,80,0.5)',
    },
  },
  btnSecondary: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    border: 'none',
    fontWeight: 500,
    cursor: 'pointer',
    background: '#f5f7f9',
    color: '#333',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    '&:hover': {
      background: '#e0e0e0',
    },
  },
}));