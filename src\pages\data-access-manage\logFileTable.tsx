import React, { useEffect, useRef, useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Fab, Input, Link, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography, withStyles } from '@material-ui/core';
import Paper from '@material-ui/core/Paper';
import CheckCircleOutlineIcon from '@material-ui/icons/CheckCircleOutline';
import ErrorOutlineIcon from '@material-ui/icons/ErrorOutline';
import DataAccessManageDelete from './delete';
import DataAccessManageDetail from './detail';
import UpdateLogSource from './updateLogSource';
import { row1 } from './mock';
import UpdateLogFile from './updateLogFile';
import LogFileDetail from './detailLogFile';
const StyledTableCell = withStyles((theme) => ({
    head: {
      backgroundColor: '#F5F5F5',
      color: theme.palette.common.black,
      textAlign: 'center',
    },
    body: {
      fontSize: 14,
      textAlign: 'center',
    },
  }))(TableCell)
interface LogSourceTableProps {
    page: number;
    rowsPerPage: number;
}
export default function LogFileTable({ page, rowsPerPage }: LogSourceTableProps) {
  const displayedRows1 = row1.slice(
      (page - 1) * rowsPerPage,
      page * rowsPerPage
      );
  return (
    <>
    <TableContainer component={Paper} style={{marginTop: 20}}>
                      <Table aria-label="simple table">
                        <TableHead>
                          <TableRow>
                             <StyledTableCell style={{ width: 400 }}>日志名称</StyledTableCell>
                             <StyledTableCell style={{ width: 400 }}>告警签名名称</StyledTableCell>
                             <StyledTableCell>告警等级</StyledTableCell>
                             <StyledTableCell>日志时间</StyledTableCell>
                             <StyledTableCell>操作</StyledTableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                             {displayedRows1.map((row) => {
                                console.log(row)
                                return (
                                <TableRow >
                                  <TableCell align="center">{row.log_name}</TableCell>
                                  <TableCell align="center">
                                     <span style={{width: 60,height: 30,justifyContent: 'center',alignItems: 'center'}}>
                                       {row.alert_signature}
                                     </span>
                                  </TableCell>
                                  <TableCell align="center" style={{color: row.severity === 1 ? '#4CAF50' : row.severity === 2 ? '#FFD476': '#FF5722'}} >
                                     <span  style={{display: 'inline-flex',alignItems: 'center',gap: '4px'}}>{row.severity === 1 ? "低":row.severity === 2 ? "中": "高"}</span>
                                  </TableCell>
                                  <TableCell align="center">{row.timestamp}</TableCell>
                                  <TableCell align="center">
                                    <UpdateLogFile row1={row}/>
                                    <LogFileDetail row={row}/>
                                    <DataAccessManageDelete name={row.log_name}/>
                                  </TableCell>
                                </TableRow>)})
                             }
                        </TableBody>
                      </Table>
                  </TableContainer>
    </>
    )}