import React, { useEffect, useRef, useState } from "react";
import { Network } from "vis-network";
import { DataSet } from "vis-data";
import { Paper, makeStyles } from "@material-ui/core";
import { Edge } from "vis-network";

const useStyles = makeStyles((theme) => ({
  root: {
    height: "70vh",
    width: "100%",
    padding: theme.spacing(2),
    marginBottom: theme.spacing(2),
  },
  networkContainer: {
    width: "100%",
    height: "100%",
    background: theme.palette.background.paper,
    border: `1px solid ${theme.palette.divider}`,
    borderRadius: theme.shape.borderRadius,
  },
}));

interface HoneypotNode {
  id: number;
  label: string;
  title?: string;
  group?: string;
  color?: string;
  x?: number;
  y?: number;
}

interface HoneypotEdge extends Edge {
  from: number;
  to: number;
  arrows?: string;
  color?: string;
  width?: number;
}

interface NetworkGraphProps {
  nodes?: HoneypotNode[];
  edges?: HoneypotEdge[];
}

const NetworkGraph: React.FC<NetworkGraphProps> = ({
  nodes = [],
  edges = [],
}) => {
  const classes = useStyles();
  const networkContainer = useRef<HTMLDivElement>(null);
  const networkInstance = useRef<Network | null>(null);
  const [tooltipContent, setTooltipContent] = useState<{
    content: string;
    x: number;
    y: number;
  } | null>(null);

  useEffect(() => {
    if (!networkContainer.current) return;

    const nodesDataSet = new DataSet(nodes);
    const edgesDataSet = new DataSet(edges);

    const data = {
      nodes: nodesDataSet,
      edges: edgesDataSet,
    };

    const options = {
      nodes: {
        shape: "circle",
        size: 40,
        font: {
          size: 16,
          color: "#333",
          face: "arial",
          bold: true,
        },
        borderWidth: 3,
        shadow: true,
        color: {
          border: "#666",
          background: "#fff",
          highlight: {
            border: "#1976d2",
            background: "#e3f2fd",
          },
        },
      },
      edges: {
        width: 2,
        color: { color: "#848484", highlight: "#1976d2" },
        smooth: {
          enabled: true,
          type: "continuous",
          roundness: 0.5,
        },
        arrows: {
          to: { enabled: true, scaleFactor: 1 },
        },
      },
      physics: {
        enabled: true,
        stabilization: {
          enabled: true,
          iterations: 1000,
          updateInterval: 100,
        },
        barnesHut: {
          gravitationalConstant: -2000,
          springConstant: 0.04,
          springLength: 200,
        },
      },
      interaction: {
        hover: true,
        hoverConnectedEdges: true,
        tooltipDelay: 0,
        zoomView: true,
        dragView: true,
      },
    };

    networkInstance.current = new Network(
      networkContainer.current,
      data,
      options
    );

    networkContainer.current.style.width = "85vw";

    networkInstance.current.on("hoverNode", (params) => {
      const node = nodesDataSet.get(params.node);
      if (node && networkContainer.current) {
        const pointer = networkInstance.current?.canvasToDOM(
          params.pointer.canvas
        );
        if (pointer) {
          const containerRect =
            networkContainer.current.getBoundingClientRect();
          setTooltipContent({
            content: node.title || "",
            x: containerRect.left + pointer.x,
            y: containerRect.top + pointer.y - 30, // 向上偏移30px
          });
        }
      }
    });

    networkInstance.current.on("blurNode", () => {
      setTooltipContent(null);
    });

    networkInstance.current.on("click", (params) => {
      if (params.nodes.length > 0) {
        const nodeId = params.nodes[0];
        const node = nodesDataSet.get(nodeId);
        console.log("Clicked node:", node);
      }
    });

    networkInstance.current.once("stabilized", () => {
      networkInstance.current?.fit();
    });

    return () => {
      if (networkInstance.current) {
        networkInstance.current.destroy();
        networkInstance.current = null;
      }
    };
  }, [nodes, edges]);

  return (
    <Paper className={classes.root}>
      <div ref={networkContainer} className={classes.networkContainer} />
      {tooltipContent && (
        <div
          style={{
            position: "fixed",
            left: tooltipContent.x + 5,
            top: tooltipContent.y + 5,
            backgroundColor: "rgba(255, 255, 255, 0.9)",
            padding: "8px",
            borderRadius: "4px",
            boxShadow: "0 2px 4px rgba(0,0,0,0.2)",
            zIndex: 1000,
            maxWidth: "200px",
            fontSize: "14px",
            lineHeight: "1.4",
          }}
          dangerouslySetInnerHTML={{ __html: tooltipContent.content }}
        />
      )}
    </Paper>
  );
};

export default NetworkGraph;
