// src/pages/HoneyPotsOverviewTop.tsx
import React, { useEffect, useState } from 'react';
import {
  makeStyles,
  Paper,
  Typography,
} from '@material-ui/core';
import HoneyPotGraph from './HoneyPotGraph';
import apiClient from '../apis/apiClient';

const useStyles = makeStyles((theme) => ({
  root: {
    height: '100%',
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: '#f5f5f5',
    boxSizing: 'border-box',
  },
  sectionTop1: {
    flex: 0.05,
    display: 'flex',
    flexDirection: 'column',
  },
  sectionTop: {
    flex: 0.95,
    display: 'flex',
    flexDirection: 'column',
    marginTop: theme.spacing(1),
  },
  paper: {
    flex: 1,
    padding: theme.spacing(1),
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
  },
  chartContainer: {
    flex: 1,
    border: '1px solid #ddd',
    borderRadius: theme.shape.borderRadius,
    padding: theme.spacing(1),
    backgroundColor: '#fff',
  },
}));

const HoneyPotsOverviewTop: React.FC = () => {
  const classes = useStyles();
  const [departmentsData, setDepartmentsData] = useState([]);
  const [activeCount, setActiveCount] = useState(0);

  const fetchData = async () => {
    try {
      const res = await apiClient.get('/api/departments/nodes/honeypots');
      if (res.data.code === 200) {
        setDepartmentsData(res.data.data.items);
        setActiveCount(res.data.data.active_honeypot_count);
      }
    } catch (err) {
      console.error('获取蜜罐拓扑数据失败', err);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className={classes.root}>
      {/* 系统概览 */}
      <div className={classes.sectionTop1}>
        <Paper className={classes.paper}>
          <Typography variant="h6" gutterBottom>
            蜜罐概览
          </Typography>
          <Typography variant="body1">
            活跃蜜罐数量: <strong>{activeCount}</strong>
          </Typography>
        </Paper>
      </div>

      {/* 图形展示 */}
      <div className={classes.sectionTop}>
        <Paper className={classes.paper}>
          <div className={classes.chartContainer}>
            <HoneyPotGraph data={departmentsData} />
          </div>
        </Paper>
      </div>
    </div>
  );
};

export default HoneyPotsOverviewTop;
