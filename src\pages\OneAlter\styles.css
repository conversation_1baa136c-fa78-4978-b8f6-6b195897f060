/* 单个告警分析包装器 */
.single-alert-wrapper {
  width: 100%;
}

/* 单个告警分析标题 */
.single-alert-header h2 {
  color: #333;
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

/* 筛选表单 */
.single-alert-filter-form {
  padding: 8px 0;
  width: 100%;
}

/* 筛选区域 */
.single-alert-filter-section {
  margin-bottom: 16px;
}

.single-alert-filter-section h4 {
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 8px 0;
  color: #555;
}

/* 强制复选框单行显示 */
.single-alert-filter-section .MuiBox-root {
  white-space: nowrap;
  overflow-x: auto;
}

.single-alert-filter-section .MuiFormControlLabel-root {
  margin-right: 8px;
  white-space: nowrap;
}

/* 筛选操作按钮 */
.single-alert-filter-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.single-alert-filter-reset-btn {
  color: #666;
  border-color: #ddd;
}

.single-alert-filter-submit-btn {
  background-color: #4caf50;
  color: white;
}

.single-alert-filter-submit-btn:hover {
  background-color: #388e3c;
}

/* 告警表格 */
.single-alert-task-table {
  margin-top: 24px;
  width: 100%;
  overflow-x: auto;
}

/* 表格容器 */
.single-alert-task-table .MuiTableContainer-root {
  width: 100%;
}

/* 表格 */
.single-alert-task-table table {
  min-width: 100%;
}

/* 表格单元格 */
.single-alert-task-table .MuiTableCell-root {
  padding: 12px 16px;
  text-align: center;
}

.single-alert-table-head {
  background-color: #f5f5f5;
}

.single-alert-table-head th {
  font-weight: 600;
  color: #333;
}

.single-alert-table-row:hover {
  background-color: #f9f9f9;
}

/* 状态、类型和严重程度标签 */
.single-alert-status-chip {
  font-size: 12px;
  height: 24px;
  border-radius: 12px;
}

.single-alert-type-chip {
  font-size: 12px;
  height: 24px;
  border-radius: 12px;
}

.single-alert-severity-chip {
  font-size: 12px;
  height: 24px;
  border-radius: 12px;
}

/* 告警操作按钮 */
.single-alert-task-actions {
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  white-space: nowrap;
}

.single-alert-action-btn {
  min-width: auto;
  padding: 2px 6px;
  font-size: 11px;
  white-space: nowrap;
  flex-shrink: 0;
}

/* 操作列特殊样式 - 强制一行显示 */
.single-alert-task-table .MuiTableCell-root:last-child {
  white-space: nowrap;
  overflow: hidden;
}

/* 分页控件 */
.single-alert-pagination {
  margin: 16px 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.single-alert-page-btn {
  min-width: 80px;
  margin: 0 8px;
}

/* 响应式调整 */
@media (max-width: 960px) {
  .single-alert-container {
    padding: 16px;
    width: 98%;
  }
  
  .single-alert-paper {
    padding: 16px;
  }
}

@media (max-width: 600px) {
  .single-alert-container {
    padding: 8px;
    width: 100%;
  }
  
  .single-alert-paper {
    padding: 12px;
  }
  
  .single-alert-filter-section {
    margin-bottom: 12px;
  }
  
  /* 移动端表格优化 */
  .single-alert-task-table {
    overflow-x: auto;
  }
  
  .single-alert-task-table table {
    min-width: 800px;
  }
  
  .single-alert-action-btn {
    font-size: 10px;
    padding: 1px 4px;
  }
}

/* 告警名称文本省略 */
.single-alert-task-table .MuiTableCell-root .MuiTypography-root {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

/* IP地址单元格样式 */
.single-alert-task-table .MuiTableCell-root:nth-child(7),
.single-alert-task-table .MuiTableCell-root:nth-child(8) {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

/* 时间单元格样式 */
.single-alert-task-table .MuiTableCell-root:nth-child(9) {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

/* 加载状态样式 */
.single-alert-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

/* 空数据状态样式 */
.single-alert-empty {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
  color: #999;
}

/* 筛选条件标签样式 */
.single-alert-filter-tags {
  margin: 16px 0;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.single-alert-filter-tag {
  margin: 2px 4px;
  background-color: #e3f2fd;
  color: #1976d2;
}

/* 表格工具栏 */
.single-alert-table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #eee;
}

.single-alert-table-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.single-alert-table-actions {
  display: flex;
  gap: 8px;
}

/* 详情弹窗样式 */
.single-alert-detail-dialog {
  max-width: 800px;
}

.single-alert-detail-content {
  padding: 24px;
}

.single-alert-detail-field {
  margin-bottom: 16px;
}

.single-alert-detail-label {
  font-weight: 500;
  color: #555;
  margin-bottom: 4px;
}

.single-alert-detail-value {
  color: #333;
  word-break: break-all;
}

/* 批量操作样式 */
.single-alert-batch-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.single-alert-batch-btn {
  min-width: 80px;
}

/* 统计信息样式 */
.single-alert-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.single-alert-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80px;
}

.single-alert-stat-number {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.single-alert-stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}
