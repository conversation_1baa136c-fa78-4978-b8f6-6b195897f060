.card {
    
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
    padding: 16px;
    margin-bottom: 16px;
    /* min-height: 89vh; */
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.cardTitle {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #388E3C;
    display: flex;
    align-items: center;
}

.cardTitle i {
    margin-right: 8px;
}

.toolbar {
    display: flex;
    justify-content: space-between;
}

.filterBar {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

.filterItem {
    min-width: 200px;
    display: flex;
    flex-direction: column;
}

.filterItem label {
    font-weight: 500;
    color: #757575;
    margin-bottom: 4px;
    font-size: 14px;
}

.addConfigBtn {
    display: flex;
    align-items: center;
}


.table {
    width: 100%;
    border-collapse: collapse;

}

.table th,
.table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #E0E0E0;
    text-align: center;
}

.table th {
    font-weight: 600;
    color: #757575;
    background: rgba(0, 0, 0, 0.02);
    font-size: 15px;
}

.table tr:hover {
    background: rgba(0, 0, 0, 0.03);
}

.badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 500;
}

.badgePrimary {
    background: #79ce7dc8;
    color: #375539;
}

.badgeSecondary {
    background: #e7e8e4;
    color: #37474F;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 6px 16px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    font-size: 14px;
    height: 36px;
    border: none;
    transition: all 0.3s;
    background: none;
}

.btn i {
    margin-right: 4px;
}

.btnPrimary {
    background: #4CAF50;
    color: #fff;
}

.btnPrimary:hover {
    background: #388E3C;
}

.btnOutline {
    background: transparent;
    border: 1px solid #E0E0E0;
    color: #388E3C;
}

.btnOutline:hover {
    background: rgba(76, 175, 80, 0.08);
}

.btnGroup {
    display: flex;
    gap: 8px;
}


.attack_list_pagination {
  height: 6vh;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.attack_list_pagination_div {
  height: 100%;
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 15px;
}

.tableContainer{
    border-radius: 10px;
    border: #d7dad9ae 1px solid;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-x: auto;
}

.table th {
    background-color: #babcbb;
    font-size: 14px;
    color: white;
    font-weight: 400;
}

.tbody  tr:nth-child(odd) {background-color: #f2f2f2;}


.search_select_reload_button {
    border: 1px solid #c4c4c4 !important;
    padding: 4px 15px !important;
    height: 36px;
    width: 85px;
    margin-top: 10px;
}
.noFocus:focus {
    outline: none !important;
    box-shadow: none !important;
}
