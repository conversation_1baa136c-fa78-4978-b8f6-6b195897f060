// Dashboard.tsx
import React, { useEffect, useState, useRef } from 'react';
import Rose<PERSON><PERSON> from './roseChart';
import ChartDownloadButton from './chartDownloadButton';

type ChartDataItem = { name: string; value: number };
const baseUrl = import.meta.env.VITE_BACK_URL; // 获取全局环境变量
const Dashboard = () => {
  const [ipHoneypotData, setIpHoneypotData] = useState<ChartDataItem[]>([]);
  const [ipNetworkData, setIpNetworkData] = useState<ChartDataItem[]>([]);
  const [portHoneypotData, setPortHoneypotData] = useState<ChartDataItem[]>([]);
  const [portNetworkData, setPortNetworkData] = useState<ChartDataItem[]>([]);
  const [loading, setLoading] = useState(true);

  const ipHoneypotRef = useRef<any>(null);
  const ipNetworkRef = useRef<any>(null);
  const portHoneypotRef = useRef<any>(null);
  const portNetworkRef = useRef<any>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const fetchChartData = async (eventType: string, element: string) => {
          const res = await fetch(`${baseUrl}/api/events/count/groupby/${eventType}/${element}`);
          const result = await res.json();

          return Object.entries(result)
            .map(([name, value]) => ({ name, value: Number(value) }))
            .sort((a, b) => b.value - a.value)
            .slice(0, 10);
        };

        const [ipHoneypot, ipNetwork, portHoneypot, portNetwork] = await Promise.all([
          fetchChartData('honeypot', 'ip_dest'),
          fetchChartData('network', 'ip_dest'),
          fetchChartData('honeypot', 'port_dest'),
          fetchChartData('network', 'port_dest'),
        ]);

        setIpHoneypotData(ipHoneypot);
        setIpNetworkData(ipNetwork);
        setPortHoneypotData(portHoneypot);
        setPortNetworkData(portNetwork);
      } catch (error) {
        console.error('获取图表数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return <div style={{ textAlign: 'center', marginTop: 100 }}>加载中...</div>;
  }

  const chartBlockStyle = {
    height: '420px',
    width: '550px',
    textAlign: 'center' as const,
  };
  return (
    <div
      style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(2, 1fr)',
        columnGap: '30px',
        rowGap: '150px',
        alignItems: 'start',
        justifyItems: 'center', // ✅ 让 grid 子项居中
      }}
    >
      <div style={chartBlockStyle}>
        <RoseChart
          ref={ipHoneypotRef}
          title="蜜罐前十IP"
          data={ipHoneypotData}
          max={1}
          interval={0.1}
        />
        <ChartDownloadButton
          chartRef={ipHoneypotRef}
          filename="honeypot-ip"
          label="下载蜜罐IP图"
        />
      </div>

      <div style={chartBlockStyle}>
        <RoseChart
          ref={ipNetworkRef}
          title="网络前十IP"
          data={ipNetworkData}
          max={1}
          interval={0.1}
        />
        <ChartDownloadButton
          chartRef={ipNetworkRef}
          filename="network-ip"
          label="下载网络IP图"
        />
      </div>

      <div style={chartBlockStyle}>
        <RoseChart
          ref={portHoneypotRef}
          title="蜜罐前十端口"
          data={portHoneypotData}
          max={2.0}
          interval={0.2}
        />
        <ChartDownloadButton
          chartRef={portHoneypotRef}
          filename="honeypot-port"
          label="下载蜜罐端口图"
        />
      </div>

      <div style={chartBlockStyle}>
        <RoseChart
          ref={portNetworkRef}
          title="网络前十端口"
          data={portNetworkData}
          max={1}
          interval={0.1}
        />
        <ChartDownloadButton
          chartRef={portNetworkRef}
          filename="network-port"
          label="下载网络端口图"
        />
      </div>
    </div>
  );
};

export default Dashboard;
