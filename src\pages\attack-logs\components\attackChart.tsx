import React, { useEffect, useRef, useState } from 'react';
import axios from 'axios';
import ReactECharts from 'echarts-for-react';
import { EChartsOption } from 'echarts';
import ChartDownloadButton from './chartDownloadButton'; //  引入封装的按钮

const baseUrl = import.meta.env.VITE_BACK_URL; // 获取全局环境变量
interface AttackChartProps {
  onTotalChange?: (totals: {
    ssh: number;
    mysql: number;
    http: number;
    pop3: number;
  }) => void;
}
const AttackChart: React.FC<AttackChartProps> = ({ onTotalChange }) => {
  const chartRef = useRef<any>(null);

  const [chartData, setChartData] = useState({
    xAxis: [] as string[],
    ssh: [] as number[],
    mysql: [] as number[],
    http: [] as number[],
    pop3: [] as number[],
  });

  const [loading, setLoading] = useState(false);

  // ✅ 获取最近七天的日期
  const getLast7Days = (): string[] => {
    const days: string[] = [];
    const today = new Date();
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      const formatted = date.toISOString().slice(0, 10); // YYYY-MM-DD
      days.push(formatted);
    }
    return days;
  };

  // ✅ 获取整齐的 Y 轴最大值（向上取整为 3 的倍数）
  const getNiceMax = (value: number, interval = 3): number => {
    if (value <= 0) return 1; // 无数据时默认最大值
    return Math.ceil(value / interval) * interval;
  };

  // 请求后端接口数据
  const fetchData = async () => {
    setLoading(true);
    const dates = getLast7Days();
    

    const requests = dates.map((date) => 
      axios.get(`${baseUrl}/api/events/statistics/all/modules`, { params: { date } })
    );
  
    try {
      const responses = await Promise.all(requests);
      const ssh: number[] = [];
      const mysql: number[] = [];
      const http: number[] = [];
      const pop3: number[] = [];
  
      responses.forEach((res) => {
        const data = res.data;
        ssh.push(data.ssh_count || 0);
        mysql.push(data.mysql_count || 0);
        http.push(data.http_count || 0);
        pop3.push(data.pop3_count || 0);
      });
      const sshTotal = ssh.reduce((a, b) => a + b, 0);
      const mysqlTotal = mysql.reduce((a, b) => a + b, 0);
      const httpTotal = http.reduce((a, b) => a + b, 0);
      const pop3Total = pop3.reduce((a, b) => a + b, 0);
  
      onTotalChange?.({
        ssh: sshTotal,
        mysql: mysqlTotal,
        http: httpTotal,
        pop3: pop3Total,
      });
      setChartData({
        xAxis: dates,
        ssh,
        mysql,
        http,
        pop3,
      });
    } catch (error) {
      console.error('Error fetching data:', error);
      setChartData({
        xAxis: dates,
        ssh: Array(7).fill(0),
        mysql: Array(7).fill(0),
        http: Array(7).fill(0),
        pop3: Array(7).fill(0),
      });
    }
  
    setLoading(false);
  };
  

  // 初始化和定时刷新
  useEffect(() => {
    fetchData();
    // const timer = setInterval(fetchData, 30000); // 每 30 秒更新一次
    // return () => clearInterval(timer);
  }, []);

  //  动态生成图表配置
  const getOption = (): EChartsOption => {
    const { ssh, mysql, http, pop3 } = chartData;
    const allValues = [...ssh, ...mysql, ...http, ...pop3];
    const hasData = allValues.some((v) => v > 0);
    const maxValue = Math.max(...allValues);
    const dynamicMax = hasData ? getNiceMax(maxValue, 3) : 1;

    return {
      title: {
        left: 'center',
        top: 50,
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: ['SSH事件', 'MySQL事件', 'HTTP事件', 'POP3事件'],
        top: 20,
      },
      grid: {
        left: '10%',
        right: '10%',
        bottom: '15%',
        top: '20%',
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: chartData.xAxis,
        name: '过 去 一 周 事 件 图 表',
        nameLocation: 'middle',
        nameGap: 30,
        splitLine: {
          show: true,
          lineStyle: {
            width: 1,
            type: 'solid',
          },
        },
      },
      yAxis: {
        type: 'value',
        name: '事件数量',
        min: 0,
        max: dynamicMax,
        interval: hasData ? 3 : 0.2, // 有数据时间隔为3，无数据时为0.2
        splitLine: {
          show: true,
          lineStyle: {
            width: 1,
            type: 'solid',
          },
        },
      },
      series: [
        {
          name: 'SSH事件',
          type: 'line',
          data: ssh,
          itemStyle: { color: 'red' },
          smooth: true,
        },
        {
          name: 'MySQL事件',
          type: 'line',
          data: mysql,
          itemStyle: { color: '#409EFF' },
          smooth: true,
        },
        {
          name: 'HTTP事件',
          type: 'line',
          data: http,
          itemStyle: { color: '#f4c430' },
          smooth: true,
        },
        {
          name: 'POP3事件',
          type: 'line',
          data: pop3,
          itemStyle: { color: '#8e44ad' },
          smooth: true,
        },
      ],
    };
  };

  return (
    <div style={{ textAlign: 'center' }}>
      <ReactECharts
        ref={chartRef}
        option={getOption()}
        style={{ height: 400, width: '100%' }}
        showLoading={loading}
      />
      <ChartDownloadButton chartRef={chartRef} filename="attack-chart" />
    </div>
  );
};

export default AttackChart;
