import { Box, Button, Dialog, DialogActions, DialogContent, DialogTitle, FormControl, FormControlLabel, Grid, InputLabel, makeStyles, MenuItem, Radio, RadioGroup, Slider, Switch, TextField, Typography } from "@material-ui/core";
import React, { useState, useEffect } from "react";
import { showSnackbar } from "../points-manage/component/myMessageBar";
import apiClient from "../apis/apiClient";
import { Add, AttachFile, CloudUpload, Delete } from '@material-ui/icons';
interface TaskPayload {
  task_name: string;
  input_source: string;
  input_config: { file_path: string; encoding: string } 
              | { log_text: string };
  log_format: string;
  analysis_type: string;
  output_format: string;
  priority: number;
  created_by: string;
  ai_model: string;
  model_config: string;
  analysis_config: string;
  config_id?: string;  
}
interface TaskFilePayload {
  file: File;
  task_name: string;
  log_format: string;
  analysis_type: string;
  config_id?: string;  
  ai_model: string;
  model_config: string;
  analysis_config: string;
  input_source: string;
}
type ConfigOption = {
  id: number;
  config_name: string;
};
const defaultForm = {
    task_name: "",
    input_source: "file",
    file_path: "",
    encoding: "utf-8",
    log_text: "",
    log_format: "auto",
    analysis_type: "威胁检测",
    output_format: "JSON",
    priority: 1,
    config_id: "1",
    created_by: "admin",
    ai_model: "local",
    model_config: "",
    analysis_config: "",
    customizeConfig: "no"
};
interface CreateTaskProps {
  fetchTasks: () => void;
}
// 创建样式
const useStyles = makeStyles((theme) => ({
  uploadButton: {
    width: '100%',
    height: 56, // 与TextField高度保持一致
    border: `1px dashed ${theme.palette.grey[400]}`,
    borderRadius: theme.shape.borderRadius,
    backgroundColor: 'transparent',
    textTransform: 'none',
    justifyContent: 'flex-start',
    '&:hover': {
      backgroundColor: theme.palette.action.hover,
      border: `1px dashed ${theme.palette.primary.main}`,
    },
  },
  uploadButtonSelected: {
    border: `1px solid ${theme.palette.primary.main}`,
    backgroundColor: theme.palette.primary.light + '20',
  },
  fileInfo: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: theme.spacing(1),
    backgroundColor: theme.palette.grey[100],
    borderRadius: theme.shape.borderRadius,
    marginTop: theme.spacing(1),
  },
  fileName: {
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(1),
    flex: 1,
    overflow: 'hidden',
  },
  deleteButton: {
    minWidth: 'auto',
    padding: theme.spacing(0.5, 1),
  },
}));
export default function CreateTask(props: CreateTaskProps) {
  const classes = useStyles(); // 使用样式
  const { fetchTasks } = props;
  const [open, setOpen] = useState(false);
  const [file, setFile] = useState<File>();
  const [form, setForm] = useState({
    task_name: "",
    input_source: "file",
    file_path: "",
    encoding: "utf-8",
    log_text: "",
    log_format: "auto",
    analysis_type: "威胁检测",
    output_format: "JSON",
    priority: 1,
    config_id: "111",
    created_by: "admin",
    ai_model: "local",
    model_config: "",
    analysis_config: "",
    customizeConfig: "no"
  });
  useEffect(() => {
      if (!open) setForm(defaultForm);
  }, [open]);
  
  const onClose = () => setOpen(false)
  const [configList, setConfigList] = useState<ConfigOption[]>([]);
  const handleOpen = () => setOpen(true)
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setForm({...form, [event.target.name]: event.target.value });
  };
const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  const selectedFile = e.target.files?.[0];
  if (selectedFile) {
    setFile(selectedFile);
    console.log(selectedFile);
  }
};

const handleFileDelete = () => {
    setFile(undefined);
  };


  const handleConfirm =async() => {
    if (!form.task_name|| !form.input_source ||(form.input_source === "text" &&!form.log_text) 
        || !form.log_format || !form.analysis_type || !form.output_format || !form.priority || !form.config_id || !form.ai_model 
        ||(form.customizeConfig === "yes" && !form.model_config )||(form.customizeConfig === "yes" && !form.analysis_config)) {
      showSnackbar("请填写必填项", "error");
      return;
    } else if (form.input_source === "file" && !file) {
      showSnackbar("请选择文件", "error");
      return;
    }
    console.log(file)
    const formTextData :TaskPayload= {
        task_name: form.task_name,
        input_source: form.input_source,
        input_config: {log_text: form.log_text},
        log_format: form.log_format,
        analysis_type: form.analysis_type,
        output_format: form.output_format,
        priority: form.priority,
        created_by: form.created_by,
        ai_model: form.ai_model,
        model_config: form.model_config,
        analysis_config: form.analysis_config,
    }
    if (form.customizeConfig === "no"){
        formTextData.config_id = form.config_id;
    }
    const formFileData = new FormData();
    if (file) { 
        formFileData.append('file', file);
    }
    formFileData.append('task_name', form.task_name);
    formFileData.append('input_source', form.input_source);
    formFileData.append('log_format', form.log_format);
    formFileData.append('analysis_type', form.analysis_type);
    formFileData.append('ai_model', form.ai_model);
    formFileData.append('model_config', form.model_config);
    formFileData.append('analysis_config', form.analysis_config);    
    if (form.customizeConfig === "no"){
        formFileData.append('config_id', form.config_id)
    }
    try {
        if (form.input_source === "file") {
            console.log("formFileData", formFileData)
            const res = await apiClient.post(`/api/log-analysis/tasks`,formFileData)}
        else {
            const res = await apiClient.post(`/api/log-analysis/tasks`,formTextData)
        }
        showSnackbar("任务创建成功", "success");
        fetchTasks();
        setOpen(false);
    } catch (error) {
        showSnackbar("任务创建失败", "error");
    }
  };

  const fetchConfigList = async () => {
    try {
      const res = await apiClient.get('/api/log-analysis/configs', {
        params: {
          page: 1,
          per_page: 100,
        },
      });
  
      const items = res.data.items || [];
      const configs: ConfigOption[] = items.map((item: any) => ({
        id: item.id,
        config_name: item.config_name,
      }));
      setConfigList(configs);
    } catch (err) {
      console.error('获取配置列表失败', err);
    }
  };
  useEffect(() => {
    fetchConfigList();
  }, []);
  return <>
  <Button variant="contained" color="primary" startIcon={<Add />} onClick={() => handleOpen()} >
      创建新任务
    </Button>
  <Dialog open={open} onClose={onClose}  maxWidth="sm" fullWidth>
            <DialogTitle>创建新任务</DialogTitle>
            <DialogContent>
                <Grid container spacing={2}>
                    <Grid item xs={6}>
                        <TextField label="任务名称" name="task_name" value={form.task_name} onChange={handleChange} margin="dense" required  fullWidth  />
                    </Grid>
                    <Grid item xs={6}>
                        <TextField select label="输入源" name="input_source" value={form.input_source} onChange={handleChange} margin="dense" fullWidth  required >
                            <MenuItem value="file">文件上传</MenuItem>
                            <MenuItem value="text">文本上传</MenuItem>
                        </TextField>
                    </Grid>
                    {
                        form.input_source === "file" ?<>
                      <Grid item xs={6}>
                <FormControl fullWidth margin="dense">
                  
                  <input
                    type="file"
                    accept="*"
                    onChange={handleFileChange}
                    style={{ display: 'none' }}
                    id="file-upload-input"
                  />
                  <label htmlFor="file-upload-input">
                    <Button
                      variant="outlined"
                      component="span"
                      startIcon={<CloudUpload />}
                      className={`${classes.uploadButton} ${file ? classes.uploadButtonSelected : ''}`}
                    >
                      {file ? '重新选择文件' : '点击选择文件'}
                    </Button>
                  </label>
                  
                  {file && (
                    <Box className={classes.fileInfo}>
                      <Box className={classes.fileName}>
                        <AttachFile color="primary" />
                        <Typography variant="body2" noWrap title={file.name}>
                          {file.name}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          ({(file.size / 1024 / 1024).toFixed(2)} MB)
                        </Typography>
                      </Box>
                      <Button
                        size="small"
                        color="secondary"
                        onClick={handleFileDelete}
                        startIcon={<Delete />}
                        className={classes.deleteButton}
                      >
                        删除
                      </Button>
                    </Box>
                  )}
                </FormControl>
              </Grid>


                         </>:<>
                         <Grid item xs={12}>
                            <TextField label="多行文本" name="log_text" rows={3} value={form.log_text} multiline variant="outlined" fullWidth InputProps={{ inputProps: { style: {  resize: 'vertical',  overflow: 'auto' }}}} onChange={handleChange}/>
                         </Grid>
                         <Grid item xs={6}>
                            <TextField select label="输出格式" name="output_format" value={form.output_format} onChange={handleChange} fullWidth margin="dense" required>
                                <MenuItem value="JSON">JSON</MenuItem>
                                <MenuItem value="CSV">CSV</MenuItem>
                                <MenuItem value="XML">XML</MenuItem>
                            </TextField>
                         </Grid>
                    </>
                        
                    }
                    <Grid item xs={6}>
                            <TextField select label="日志格式" name="log_format" value={form.log_format} onChange={handleChange} fullWidth margin="dense" required >
                                <MenuItem value="auto">自动检测</MenuItem>
                                <MenuItem value="apache">Apache</MenuItem>
                                <MenuItem value="nginx">Nginx</MenuItem>
                                <MenuItem value="syslog">Syslog</MenuItem>
                            </TextField>
                        </Grid>
                    <Grid item xs={6}>
                        <TextField label="分析类型" name="analysis_type"  value="威胁检测" onChange={handleChange} margin="dense" required  fullWidth  disabled  />
                    </Grid>
                     
                    <Grid item xs={6}>
                        <TextField select label="使用自定义配置" name="customizeConfig" value={form.customizeConfig} onChange={handleChange} margin="dense" fullWidth  required >
                            <MenuItem value="no">否</MenuItem>
                            <MenuItem value="yes">是</MenuItem>
                        </TextField>
                    </Grid>
                    {
                        form.customizeConfig === "no" ?
                        <Grid item xs={12}>
                            <TextField select label="选择配置" name="config_id" value={form.config_id} onChange={handleChange} fullWidth margin="dense" required >
                                {configList.map((config) => (
                                    <MenuItem key={config.id} value={config.id}>{config.config_name}</MenuItem>
                                ))}
                            </TextField>
                        </Grid>:
                        <>
                        <Grid item xs={12}>
                            <TextField select label="AI模型" name="ai_model" value={form.ai_model} onChange={handleChange} margin="dense" fullWidth  required >
                                <MenuItem value="local">本地模型</MenuItem>
                                <MenuItem value="Foundation-Sec-8B">OpenAI</MenuItem>
                            </TextField>
                        </Grid>
                        <Grid item xs={12}>
                            <TextField label="模型参数" name="model_config"  placeholder='如{"temperature": 0.5,"max_tokens": 1024,"top_p": 0.8}' value={form.model_config} rows={3} multiline variant="outlined" fullWidth InputProps={{ inputProps: { style: {  resize: 'vertical',  overflow: 'auto' }}}} onChange={handleChange}/>
                        </Grid>
                        <Grid item xs={12}>
                            <TextField label="分析参数" name="analysis_config"  placeholder='如{"confidence_threshold": 0.6,"batch_size": 2}' value={form.analysis_config} rows={3} multiline variant="outlined" fullWidth InputProps={{ inputProps: { style: {  resize: 'vertical',  overflow: 'auto' }}}} onChange={handleChange}/>
                        </Grid>
                        </>
                    }
                    {
                        form.input_source === "file" ?<></>:
                        <Grid item xs={12}>
                        <InputLabel shrink > 优先级（1-10） </InputLabel>
                       <Slider
  defaultValue={1}
  aria-labelledby="priority-slider"
  valueLabelDisplay="auto"
  step={1}
  marks
  min={1}
  max={10}
  value={form.priority}
  onChange={(e, v) => {
    if (typeof v === 'number') {
      setForm({ ...form, priority: v });
    }
  }}
/>

                    </Grid>
                        
                    }
                </Grid>
            </DialogContent>
            <DialogActions>
                <Button  onClick={onClose}>取消</Button>
                <Button  onClick={handleConfirm} variant="contained" color="primary">
                    确认
                </Button>
            </DialogActions>
        </Dialog>
        </>;
}