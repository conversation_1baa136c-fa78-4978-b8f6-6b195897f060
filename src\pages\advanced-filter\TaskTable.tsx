import React from 'react'
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  LinearProgress,
  Typography,
  Chip,
  TableFooter,
  TablePagination,
  CircularProgress,
} from '@material-ui/core'
import { TaskData } from './index'
import { useNavigate } from 'react-router-dom'

interface TaskTableProps {
  tasks: TaskData[]
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  onTaskAction: (id: number, action: 'detail' | 'start' | 'stop') => void
  loading?: boolean
}

// 定义列宽样式
const columnStyles = {
  id: { width: '5%', minWidth: '60px' },
  taskName: { width: '14%', minWidth: '140px' },
  type: { width: '8%', minWidth: '80px' },
  status: { width: '8%', minWidth: '80px' },
  progress: { width: '15%', minWidth: '150px' },
  inputSource: { width: '8%', minWidth: '80px' },
  fileInfo: { width: '15%', minWidth: '150px' },
  createTime: { width: '11%', minWidth: '110px' },
  actions: { width: '16%', minWidth: '180px' },
}

const TaskTable: React.FC<TaskTableProps> = ({
  tasks,
  currentPage,
  totalPages,
  onPageChange,
  onTaskAction,
  loading = false,
}) => {
  // 获取任务状态对应的样式
  const getStatusStyle = (status: string) => {
    switch (status) {
      case '运行中':
        return { backgroundColor: '#4caf50', color: 'white' }
      case '待处理':
        return { backgroundColor: '#ff9800', color: 'white' }
      case '已完成':
        return { backgroundColor: '#2196f3', color: 'white' }
      case '已取消':
        return { backgroundColor: '#9e9e9e', color: 'white' }
      case '失败':
        return { backgroundColor: '#f44336', color: 'white' }
      default:
        return { backgroundColor: '#9e9e9e', color: 'white' }
    }
  }

  // 获取任务类型对应的样式
  const getTypeStyle = (type: string) => {
    switch (type) {
      case '批量':
        return { backgroundColor: '#e0f7fa', color: '#00838f' }
      case '实时':
        return { backgroundColor: '#f9fbe7', color: '#827717' }
      case '计划':
        return { backgroundColor: '#e8f5e9', color: '#2e7d32' }
      default:
        return { backgroundColor: '#f5f5f5', color: '#616161' }
    }
  }

  // 获取进度条颜色
  const getProgressColor = (status: string) => {
    switch (status) {
      case '运行中':
        return 'primary'
      case '已完成':
        return 'primary'
      case '待处理':
        return 'secondary'
      case '已取消':
        return 'secondary'
      case '失败':
        return 'secondary'
      default:
        return 'primary'
    }
  }

  // 渲染操作按钮
  const renderActionButtons = (task: TaskData) => {
    const { id, status } = task
    const navigate = useNavigate()

    return (
      <Box className="advanced-filter-task-actions" display="flex" justifyContent="center" alignItems="center" flexWrap="nowrap">
        <Button
          variant="text"
          color="primary"
          onClick={() => onTaskAction(id, 'detail')}
          className="advanced-filter-action-btn">
          详情
        </Button>
        <Button
          variant="text"
          color="primary"
          onClick={() => navigate(`/statusMonitor?taskId=${id}`)}
          className="advanced-filter-action-btn">
          状态监控
        </Button>
        {status === '运行中' && (
          <Button
            variant="text"
            color="secondary"
            onClick={() => onTaskAction(id, 'stop')}
            className="advanced-filter-action-btn">
            停止
          </Button>
        )}
        {status === '待处理' && (
          <Button
            variant="text"
            color="primary"
            onClick={() => onTaskAction(id, 'start')}
            className="advanced-filter-action-btn">
            启动
          </Button>
        )}
      </Box>
    )
  }

  // 渲染空数据提示
  const renderEmptyData = () => {
    return (
      <TableRow>
        <TableCell colSpan={9} align="center" style={{ padding: '40px 0' }}>
          <Typography variant="body1" color="textSecondary">
            暂无数据，请调整筛选条件后重试
          </Typography>
        </TableCell>
      </TableRow>
    )
  }

  // 渲染加载中提示
  const renderLoadingData = () => {
    return (
      <TableRow>
        <TableCell colSpan={9} align="center" style={{ padding: '40px 0' }}>
          <Box display="flex" justifyContent="center" alignItems="center">
            <CircularProgress size={24} style={{ marginRight: 10 }} />
            <Typography variant="body1" color="textSecondary">
              正在加载数据...
            </Typography>
          </Box>
        </TableCell>
      </TableRow>
    )
  }

  return (
    <Box className="advanced-filter-task-table">
      <TableContainer component={Paper}>
        <Table>
          <TableHead className="advanced-filter-table-head">
            <TableRow>
              <TableCell align="center" style={columnStyles.id}>ID</TableCell>
              <TableCell align="center" style={columnStyles.taskName}>任务名称</TableCell>
              <TableCell align="center" style={columnStyles.type}>类型</TableCell>
              <TableCell align="center" style={columnStyles.status}>状态</TableCell>
              <TableCell align="center" style={columnStyles.progress}>进度</TableCell>
              <TableCell align="center" style={columnStyles.inputSource}>输入源</TableCell>
              <TableCell align="center" style={columnStyles.fileInfo}>文件信息</TableCell>
              <TableCell align="center" style={columnStyles.createTime}>创建时间</TableCell>
              <TableCell align="center" style={columnStyles.actions}>操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading
              ? renderLoadingData()
              : tasks.length > 0
              ? tasks.map((task: TaskData, index: number) => (
                  <TableRow key={task.id} className="advanced-filter-table-row">
                    <TableCell align="center" style={columnStyles.id}>{(currentPage - 1) * 5 + index + 1}</TableCell>
                    <TableCell align="center" style={columnStyles.taskName}>
                      {task.taskName}
                    </TableCell>
                    <TableCell align="center" style={columnStyles.type}>
                      <Chip
                        label={task.type}
                        size="small"
                        style={getTypeStyle(task.type)}
                        className="advanced-filter-type-chip"
                      />
                    </TableCell>
                    <TableCell align="center" style={columnStyles.status}>
                      <Chip
                        label={task.status}
                        size="small"
                        style={getStatusStyle(task.status)}
                        className="advanced-filter-status-chip"
                      />
                    </TableCell>
                    <TableCell align="center" style={columnStyles.progress}>
                      <Box display="flex" alignItems="center" width="100%" justifyContent="center">
                        <Box width="100%" mr={1}>
                          <LinearProgress
                            variant="determinate"
                            value={task.progress}
                            color={
                              getProgressColor(task.status) as
                                | 'primary'
                                | 'secondary'
                            }
                            className="advanced-filter-progress-bar"
                          />
                        </Box>
                        <Box minWidth={35}>
                          <Typography
                            variant="body2"
                            color="textSecondary">{`${task.progress}%`}</Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell align="center" style={columnStyles.inputSource}>
                      {task.inputSource}
                    </TableCell>
                    <TableCell align="center" style={columnStyles.fileInfo}>
                      {task.fileInfo}
                    </TableCell>
                    <TableCell align="center" style={columnStyles.createTime}>
                      {(() => {
                        try {
                          // 尝试解析时间戳并格式化
                          const date = new Date(task.createTime);
                          if (!isNaN(date.getTime())) {
                            return date.toLocaleString('zh-CN', {
                              year: 'numeric',
                              month: '2-digit',
                              day: '2-digit',
                              hour: '2-digit',
                              minute: '2-digit',
                              second: '2-digit',
                              hour12: false
                            });
                          }
                          return task.createTime; // 如果解析失败，返回原始值
                        } catch (error) {
                          return task.createTime;
                        }
                      })()}
                    </TableCell>
                    <TableCell align="center" style={columnStyles.actions}>
                      {renderActionButtons(task)}
                    </TableCell>
                  </TableRow>
                ))
              : renderEmptyData()}
          </TableBody>
        </Table>
      </TableContainer>

      {/* 分页控件 */}
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        mt={2}
        className="advanced-filter-pagination">
        <Button
          disabled={currentPage <= 1 || tasks.length === 0 || loading}
          onClick={() => onPageChange(currentPage - 1)}
          className="advanced-filter-page-btn">
          上一页
        </Button>

        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
          <Button
            key={page}
            variant={page === currentPage ? 'contained' : 'text'}
            color={page === currentPage ? 'primary' : 'default'}
            onClick={() => onPageChange(page)}
            className={`advanced-filter-page-num ${
              page === currentPage ? 'advanced-filter-current-page' : ''
            }`}
            disabled={tasks.length === 0 || loading}>
            {page}
          </Button>
        ))}

        <Button
          disabled={currentPage >= totalPages || tasks.length === 0 || loading}
          onClick={() => onPageChange(currentPage + 1)}
          className="advanced-filter-page-btn">
          下一页
        </Button>
      </Box>
    </Box>
  )
}

export default TaskTable
