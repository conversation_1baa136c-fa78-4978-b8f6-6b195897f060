import axios from "axios";

// 创建 Axios 实例
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_BACK_URL, // API基础URL
  timeout: 60000, // 请求超时时间
});



// 示例请求
// apiClient.get("/some-endpoint")
//   .then(response => {
//     console.log(response.data);
//   })
//   .catch(error => {
//     console.error(error);
//   });

// const response = await apiClient.post(`${api}/V1/platform/course_chapter_manage/get_chapter`, {
//   course_id,
//   class_id,
//   skip: (page - 1) * pageSize,
//   limit: pageSize
// });

export default apiClient;

