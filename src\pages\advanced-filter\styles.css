/* 高级筛选包装器 */
.advanced-filter-wrapper {
  width: 100%;
}

/* 高级筛选标题 */
.advanced-filter-header h2 {
  color: #333;
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

/* 筛选表单 */
.advanced-filter-form {
  padding: 8px 0;
  width: 100%;
}

/* 筛选区域 */
.advanced-filter-section {
  margin-bottom: 16px;
}

.advanced-filter-section h4 {
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 8px 0;
  color: #555;
}

/* 强制复选框单行显示 */
.advanced-filter-section .MuiBox-root {
  white-space: nowrap;
  overflow-x: auto;
}

.advanced-filter-section .MuiFormControlLabel-root {
  margin-right: 8px;
  white-space: nowrap;
}

/* 筛选操作按钮 */
.advanced-filter-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.advanced-filter-reset-btn {
  color: #666;
  border-color: #ddd;
}

.advanced-filter-submit-btn {
  background-color: #4caf50;
  color: white;
}

.advanced-filter-submit-btn:hover {
  background-color: #388e3c;
}

/* 任务表格 */
.advanced-filter-task-table {
  margin-top: 24px;
  width: 100%;
  overflow-x: auto;
}

/* 表格容器 */
.advanced-filter-task-table .MuiTableContainer-root {
  width: 100%;
}

/* 表格 */
.advanced-filter-task-table table {
  min-width: 100%;
}

/* 表格单元格 */
.advanced-filter-task-table .MuiTableCell-root {
  padding: 12px 16px;
  text-align: center;
}

.advanced-filter-table-head {
  background-color: #f5f5f5;
}

.advanced-filter-table-head th {
  font-weight: 600;
  color: #333;
}

.advanced-filter-table-row:hover {
  background-color: #f9f9f9;
}

/* 状态和类型标签 */
.advanced-filter-status-chip {
  font-size: 12px;
  height: 24px;
  border-radius: 12px;
}

.advanced-filter-type-chip {
  font-size: 12px;
  height: 24px;
  border-radius: 12px;
}

/* 进度条 */
.advanced-filter-progress-bar {
  height: 8px;
  border-radius: 4px;
}

/* 任务操作按钮 */
.advanced-filter-task-actions {
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  white-space: nowrap;
}

.advanced-filter-action-btn {
  min-width: auto;
  padding: 2px 6px;
  font-size: 11px;
  white-space: nowrap;
  flex-shrink: 0;
}

/* 操作列特殊样式 - 强制一行显示 */
.advanced-filter-task-table .MuiTableCell-root:last-child {
  white-space: nowrap;
  overflow: hidden;
}

/* 分页控件 */
.advanced-filter-pagination {
  margin: 16px 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.advanced-filter-page-btn {
  min-width: 80px;
  margin: 0 8px;
}

.advanced-filter-page-num {
  min-width: 36px;
  height: 36px;
  margin: 0 4px;
  border-radius: 18px;
}

.advanced-filter-current-page {
  background-color: #4caf50;
  color: white;
}

/* 响应式调整 */
@media (max-width: 960px) {
  .advanced-filter-container {
    padding: 16px;
    width: 98%;
  }
  
  .advanced-filter-paper {
    padding: 16px;
  }
}

@media (max-width: 600px) {
  .advanced-filter-container {
    padding: 8px;
    width: 100%;
  }
  
  .advanced-filter-paper {
    padding: 12px;
  }
  
  .advanced-filter-section {
    margin-bottom: 12px;
  }
} 